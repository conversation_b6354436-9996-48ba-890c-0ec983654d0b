{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/core/config.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n配置管理模块\n\n提供应用程序配置的加载、保存和管理功能。\n\"\"\"\n\nimport os\nimport json\nfrom pathlib import Path\nfrom typing import Dict, List, Any, Optional\n\nfrom .constants import (\n    DATA_FOLDER_NAME, CONFIG_FILE_NAME, CURSOR_ACCOUNT_FILE_NAME,\n    WINDSURF_ACCOUNT_FILE_NAME, LOG_FILE_NAME, LOGS_FOLDER_NAME\n)\nfrom .exceptions import ConfigError\nfrom .logger import get_logger\n\n\ndef get_app_data_dir() -> Path:\n    \"\"\"获取应用数据目录\n    \n    Returns:\n        应用数据目录路径\n    \"\"\"\n    if os.name == 'nt':  # Windows\n        app_data = os.environ.get('APPDATA', os.path.expanduser('~'))\n        return Path(app_data) / \"CursorAccountManager\"\n    else:  # Linux/macOS\n        return Path.home() / \".cursor_account_manager\"\n\n\nclass Config:\n    \"\"\"配置管理类\n    \n    管理应用程序的各项配置，包括邮箱设置、域名和浏览器参数。\n    支持两种邮箱验证方式：临时邮箱和IMAP邮箱。\n    支持从data/config.json加载多个配置文件。\n    \"\"\"\n    \n    # 默认配置\n    DEFAULT_CONFIG = {\n        # 基础配置\n        \"DOMAIN\": \"xiaofutools.xyz\",  # 你的CF路由填写的域名，用于生成邮箱地址和访问服务\n        \n        # 邮箱模式选择（只使用临时邮箱）\n        \"USE_TEMP_MAIL\": \"True\",\n        \n        # 临时邮箱配置\n        \"TEMP_MAIL\": \"<EMAIL>\",  # 临时邮箱完整地址（包括@后缀部分）\n        \"TEMP_MAIL_EPIN\": \"1234\",  # 临时邮箱PIN码，用于访问临时邮箱服务\n        \"TEMP_MAIL_EXT\": \"@mailto.plus\",  # 临时邮箱后缀，包括@符号\n        \n        # 浏览器配置\n        \"BROWSER_HEADLESS\": \"True\",  # 无头模式设置，True为浏览器在后台运行不显示界面，False为显示浏览器界面\n        \n        # Cursor路径配置\n        \"CURSOR_MANUAL_PATH\": \"\"  # 手动设置的Cursor.exe路径，为空时使用自动检测\n    }\n    \n    def __init__(self, config_index: int = 0):\n        \"\"\"初始化配置管理器\n        \n        从data/config.json加载设置，配置邮箱验证方式和其他参数。\n        如果文件不存在或加载失败，则使用默认配置。\n        \n        Args:\n            config_index: 使用的配置索引，默认为0\n        \"\"\"\n        self.logger = get_logger()\n        \n        # 存储配置索引\n        self.config_index = config_index\n        \n        # 设置路径\n        self._setup_paths()\n        \n        # 确保基础目录存在\n        self._ensure_directories()\n        \n        # 加载配置文件\n        self.config_list = self._load_config_file()\n        \n        # 如果配置列表为空或索引超出范围，则使用默认配置\n        if not self.config_list or config_index >= len(self.config_list):\n            self.config_data = self.DEFAULT_CONFIG\n            self.logger.info(\"使用默认配置\")\n        else:\n            self.config_data = self.config_list[config_index]\n            self.logger.info(f\"使用配置文件中的第 {config_index + 1} 个配置\")\n        \n        # 设置各项配置\n        self._setup_config_properties()\n    \n    def _setup_paths(self):\n        \"\"\"设置路径\"\"\"\n        # 基础目录\n        self.app_dir = get_app_data_dir()\n        self.data_folder = self.app_dir / DATA_FOLDER_NAME\n        self.logs_folder = self.app_dir / LOGS_FOLDER_NAME\n        \n        # 配置文件路径\n        self.config_file_path = self.data_folder / CONFIG_FILE_NAME\n        \n        # 账号文件路径\n        self.cursor_account_file = self.data_folder / CURSOR_ACCOUNT_FILE_NAME\n        self.windsurf_account_file = self.data_folder / WINDSURF_ACCOUNT_FILE_NAME\n        \n        # 日志文件路径\n        self.log_file_path = self.logs_folder / LOG_FILE_NAME\n    \n    def _ensure_directories(self) -> None:\n        \"\"\"确保所需目录存在\"\"\"\n        try:\n            self.data_folder.mkdir(parents=True, exist_ok=True)\n            self.logs_folder.mkdir(parents=True, exist_ok=True)\n        except Exception as e:\n            raise ConfigError(f\"创建目录失败: {e}\")\n    \n    def _setup_config_properties(self):\n        \"\"\"设置配置属性\"\"\"\n        try:\n            # 设置各项配置\n            self.use_temp_mail = True  # 始终使用临时邮箱\n            self.temp_mail = self.config_data[\"TEMP_MAIL\"].strip().split(\"@\")[0]\n            self.temp_mail_epin = self.config_data[\"TEMP_MAIL_EPIN\"].strip()\n            self.temp_mail_ext = self.config_data[\"TEMP_MAIL_EXT\"].strip()\n            self.domain = self.config_data[\"DOMAIN\"].strip()\n            self.browser_headless = self.config_data[\"BROWSER_HEADLESS\"].strip()\n            \n            # 不再使用IMAP\n            self.imap = False\n            \n        except KeyError as e:\n            raise ConfigError(f\"配置项缺失: {e}\")\n        except Exception as e:\n            raise ConfigError(f\"配置设置失败: {e}\")\n    \n    def _load_config_file(self) -> List[Dict[str, Any]]:\n        \"\"\"加载配置文件\n        \n        Returns:\n            配置列表\n        \"\"\"\n        try:\n            if self.config_file_path.exists():\n                with open(self.config_file_path, \"r\", encoding=\"utf-8\") as f:\n                    config_list = json.load(f)\n                    if isinstance(config_list, list):\n                        self.logger.info(f\"成功加载 {len(config_list)} 个配置\")\n                        return config_list\n                    else:\n                        self.logger.warning(\"配置文件格式错误，应为列表格式\")\n                        return []\n            else:\n                self.logger.info(\"配置文件不存在，将使用默认配置\")\n                return []\n        except json.JSONDecodeError as e:\n            self.logger.error(f\"配置文件JSON格式错误: {e}\")\n            return []\n        except Exception as e:\n            self.logger.error(f\"加载配置文件失败: {e}\")\n            return []\n    \n    # 属性访问方法\n    @property\n    def app_data_dir(self) -> Path:\n        \"\"\"应用数据目录\"\"\"\n        return self.app_dir\n    \n    @property\n    def data_dir(self) -> Path:\n        \"\"\"数据目录\"\"\"\n        return self.data_folder\n    \n    @property\n    def logs_dir(self) -> Path:\n        \"\"\"日志目录\"\"\"\n        return self.logs_folder\n    \n    # 邮箱相关配置获取方法\n    def get_temp_mail(self) -> str:\n        \"\"\"获取临时邮箱用户名\n        \n        Returns:\n            临时邮箱用户名部分（不包含@后缀）\n        \"\"\"\n        return self.temp_mail\n    \n    def get_temp_mail_epin(self) -> str:\n        \"\"\"获取临时邮箱PIN码\n        \n        Returns:\n            临时邮箱PIN码\n        \"\"\"\n        return self.temp_mail_epin\n    \n    def get_temp_mail_ext(self) -> str:\n        \"\"\"获取临时邮箱后缀\n        \n        Returns:\n            临时邮箱后缀（包含@符号）\n        \"\"\"\n        return self.temp_mail_ext\n    \n    def get_domain(self) -> str:\n        \"\"\"获取域名\n        \n        Returns:\n            域名\n        \"\"\"\n        return self.domain\n    \n    def is_browser_headless(self) -> bool:\n        \"\"\"是否使用无头浏览器模式\n        \n        Returns:\n            True表示无头模式，False表示显示浏览器界面\n        \"\"\"\n        return self.browser_headless.lower() == \"true\"\n    \n    def get_cursor_manual_path(self) -> str:\n        \"\"\"获取手动设置的Cursor路径\n        \n        Returns:\n            Cursor.exe路径，为空时使用自动检测\n        \"\"\"\n        return self.config_data.get(\"CURSOR_MANUAL_PATH\", \"\").strip()\n    \n    # 配置管理方法\n    def switch_config(self, index: int) -> bool:\n        \"\"\"切换配置\n        \n        Args:\n            index: 配置索引\n            \n        Returns:\n            是否切换成功\n        \"\"\"\n        try:\n            config_list = self.get_config_list()\n            if not config_list or index >= len(config_list) or index < 0:\n                self.logger.error(f\"配置索引 {index} 超出范围\")\n                return False\n            \n            # 更新配置索引\n            self.config_index = index\n            self.config_data = config_list[index]\n            \n            # 更新配置属性\n            self._setup_config_properties()\n            \n            self.logger.info(f\"已切换到配置 {index}\")\n            return True\n        except Exception as e:\n            self.logger.error(f\"切换配置失败: {e}\")\n            return False\n    \n    def get_config_list(self) -> List[Dict[str, Any]]:\n        \"\"\"获取配置列表\n        \n        Returns:\n            配置列表\n        \"\"\"\n        try:\n            if self.config_file_path.exists():\n                with open(self.config_file_path, \"r\", encoding=\"utf-8\") as f:\n                    return json.load(f)\n            return []\n        except Exception as e:\n            self.logger.error(f\"获取配置列表失败: {e}\")\n            return []\n    \n    def get_config_count(self) -> int:\n        \"\"\"获取配置数量\n        \n        Returns:\n            配置数量\n        \"\"\"\n        return len(self.get_config_list())\n    \n    def get_current_config_index(self) -> int:\n        \"\"\"获取当前配置索引\n        \n        Returns:\n            当前配置索引\n        \"\"\"\n        return self.config_index\n"}
{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/services/cursor/__init__.py"}, "originalCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nCursor服务模块\n\n提供Cursor相关的业务逻辑。\n\"\"\"\n\nfrom .registration import CursorRegistrationService\nfrom .account_manager import CursorAccountManager\nfrom .auth import CursorAuthService\n\n__all__ = [\n    'CursorRegistrationService',\n    'CursorAccountManager',\n    'CursorAuthService'\n]\n"}
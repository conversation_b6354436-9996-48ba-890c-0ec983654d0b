{"path": {"rootPath": "e:\\XIAOFUTools\\AndroidStudioProjects\\BabyLog", "relPath": "app\\src\\main\\java\\com\\example\\babylog\\ui\\screens\\HealthScreen.kt"}, "originalCode": "package com.example.babylog.ui.screens\n\nimport androidx.compose.foundation.background\nimport androidx.compose.foundation.layout.*\nimport androidx.compose.foundation.lazy.LazyColumn\nimport androidx.compose.foundation.lazy.items\nimport androidx.compose.foundation.shape.RoundedCornerShape\nimport androidx.compose.material.icons.Icons\nimport androidx.compose.material.icons.filled.*\nimport androidx.compose.material3.*\nimport androidx.compose.runtime.*\nimport androidx.compose.ui.Alignment\nimport androidx.compose.ui.Modifier\nimport androidx.compose.ui.draw.clip\nimport androidx.compose.ui.graphics.Color\nimport androidx.compose.ui.platform.LocalContext\nimport androidx.compose.ui.text.font.FontWeight\nimport androidx.compose.ui.unit.dp\nimport androidx.navigation.NavController\nimport com.example.babylog.data.database.BabyLogDatabase\nimport com.example.babylog.data.entity.Baby\nimport com.example.babylog.data.entity.HealthRecord\nimport com.example.babylog.data.repository.BabyRepository\nimport com.example.babylog.navigation.Screen\nimport com.example.babylog.ui.theme.*\nimport java.text.SimpleDateFormat\nimport java.util.*\n\n@OptIn(ExperimentalMaterial3Api::class)\n@Composable\nfun HealthScreen(navController: NavController) {\n    val context = LocalContext.current\n    val database = BabyLogDatabase.getDatabase(context)\n    val repository = BabyRepository(\n        database.babyDao(),\n        database.healthRecordDao(),\n        database.milestoneDao(),\n        database.feedingRecordDao(),\n        database.photoDao()\n    )\n    \n    val babies by repository.getAllBabies().collectAsState(initial = emptyList())\n    var selectedBaby by remember { mutableStateOf<Baby?>(null) }\n    var selectedTab by remember { mutableStateOf(0) }\n    \n    // Set first baby as selected if available\n    LaunchedEffect(babies) {\n        if (babies.isNotEmpty() && selectedBaby == null) {\n            selectedBaby = babies.first()\n        }\n    }\n    \n    val healthRecords by remember(selectedBaby) {\n        if (selectedBaby != null) {\n            repository.getHealthRecordsByBaby(selectedBaby!!.id)\n        } else {\n            kotlinx.coroutines.flow.flowOf(emptyList<HealthRecord>())\n        }\n    }.collectAsState(initial = emptyList())\n    \n    val tabs = listOf(\"全部\", \"身高体重\", \"疫苗接种\", \"体检记录\", \"用药记录\")\n    \n    Box(\n        modifier = Modifier\n            .fillMaxSize()\n            .background(NeumorphismColors.background)\n    ) {\n        LazyColumn(\n            modifier = Modifier\n                .fillMaxSize()\n                .padding(horizontal = 20.dp),\n            contentPadding = PaddingValues(vertical = 20.dp),\n            verticalArrangement = Arrangement.spacedBy(20.dp)\n        ) {\n            // Header\n            item {\n                Row(\n                    modifier = Modifier.fillMaxWidth(),\n                    horizontalArrangement = Arrangement.SpaceBetween,\n                    verticalAlignment = Alignment.CenterVertically\n                ) {\n                    Column {\n                        Text(\n                            text = \"健康记录\",\n                            style = MaterialTheme.typography.headlineLarge,\n                            fontWeight = FontWeight.Bold,\n                            color = NeumorphismColors.textPrimary\n                        )\n                        Text(\n                            text = \"记录宝宝的健康成长数据\",\n                            style = MaterialTheme.typography.bodyMedium,\n                            color = NeumorphismColors.textSecondary\n                        )\n                    }\n\n                    selectedBaby?.let { baby ->\n                        NeumorphismFloatingActionButton(\n                            onClick = {\n                                navController.navigate(\"${Screen.AddHealth.route}?babyId=${baby.id}\")\n                            },\n                            modifier = Modifier.size(56.dp)\n                        ) {\n                            Icon(\n                                Icons.Default.Add,\n                                contentDescription = \"添加记录\",\n                                tint = Color.White\n                            )\n                        }\n                    }\n                }\n            }\n\n            // Baby selector\n            if (babies.isNotEmpty()) {\n                item {\n            var expanded by remember { mutableStateOf(false) }\n            \n            ExposedDropdownMenuBox(\n                expanded = expanded,\n                onExpandedChange = { expanded = !expanded }\n            ) {\n                OutlinedTextField(\n                    value = selectedBaby?.name ?: \"\",\n                    onValueChange = { },\n                    readOnly = true,\n                    label = { Text(\"选择宝宝\") },\n                    trailingIcon = {\n                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)\n                    },\n                    modifier = Modifier\n                        .fillMaxWidth()\n                        .menuAnchor()\n                )\n                \n                ExposedDropdownMenu(\n                    expanded = expanded,\n                    onDismissRequest = { expanded = false }\n                ) {\n                    babies.forEach { baby ->\n                        DropdownMenuItem(\n                            text = { Text(baby.name) },\n                            onClick = {\n                                selectedBaby = baby\n                                expanded = false\n                            }\n                        )\n                    }\n                }\n                }\n            }\n\n            // Tabs and content would go here\n            // For now, let's show a simple list\n            items(healthRecords) { record ->\n                HealthRecordCard(record = record)\n            }\n        }\n    }\n}\n\n@Composable\nfun HealthRecordCard(record: HealthRecord) {\n    NeumorphismCard(\n        modifier = Modifier.fillMaxWidth(),\n        elevation = 4,\n        cornerRadius = 16\n    ) {\n        Column {\n            Row(\n                modifier = Modifier.fillMaxWidth(),\n                horizontalArrangement = Arrangement.SpaceBetween,\n                verticalAlignment = Alignment.CenterVertically\n            ) {\n                Text(\n                    text = getHealthTypeText(record.type),\n                    style = MaterialTheme.typography.titleMedium,\n                    fontWeight = FontWeight.Bold,\n                    color = NeumorphismColors.textPrimary\n                )\n                Text(\n                    text = SimpleDateFormat(\"MM-dd\", Locale.getDefault()).format(record.date),\n                    style = MaterialTheme.typography.bodySmall,\n                    color = NeumorphismColors.textSecondary\n                )\n            }\n\n            if (record.value > 0) {\n                Spacer(modifier = Modifier.height(8.dp))\n                Text(\n                    text = \"${record.value}${getHealthUnitText(record.type)}\",\n                    style = MaterialTheme.typography.bodyLarge,\n                    color = NeumorphismColors.primary,\n                    fontWeight = FontWeight.Medium\n                )\n            }\n\n            if (record.notes.isNotBlank()) {\n                Spacer(modifier = Modifier.height(8.dp))\n                Text(\n                    text = record.notes,\n                    style = MaterialTheme.typography.bodyMedium,\n                    color = NeumorphismColors.textSecondary\n                )\n            }\n        }\n    }\n}\n\nprivate fun getHealthTypeText(type: String): String = when (type) {\n    \"weight\" -> \"体重\"\n    \"height\" -> \"身高\"\n    \"vaccination\" -> \"疫苗接种\"\n    \"checkup\" -> \"体检记录\"\n    \"temperature\" -> \"体温\"\n    \"medication\" -> \"用药记录\"\n    else -> type\n}\n\nprivate fun getHealthUnitText(type: String): String = when (type) {\n    \"weight\" -> \"kg\"\n    \"height\" -> \"cm\"\n    \"temperature\" -> \"°C\"\n    else -> \"\"\n}\n                                \"temperature\" -> Icons.Default.Thermostat\n                                \"medication\" -> Icons.Default.MedicalServices\n                                else -> Icons.Default.HealthAndSafety\n                            },\n                            contentDescription = null,\n                            modifier = Modifier.size(20.dp),\n                            tint = MaterialTheme.colorScheme.primary\n                        )\n                        Spacer(modifier = Modifier.width(8.dp))\n                        Text(\n                            text = when (record.type) {\n                                \"weight\" -> \"体重记录\"\n                                \"height\" -> \"身高记录\"\n                                \"vaccination\" -> \"疫苗接种\"\n                                \"checkup\" -> \"体检记录\"\n                                \"temperature\" -> \"体温记录\"\n                                \"medication\" -> \"用药记录\"\n                                else -> \"健康记录\"\n                            },\n                            style = MaterialTheme.typography.titleMedium,\n                            fontWeight = FontWeight.Medium\n                        )\n                    }\n                    \n                    Spacer(modifier = Modifier.height(4.dp))\n                    \n                    record.value?.let { value ->\n                        Text(\n                            text = \"${value}${record.unit ?: \"\"}\",\n                            style = MaterialTheme.typography.bodyLarge,\n                            fontWeight = FontWeight.Bold,\n                            color = MaterialTheme.colorScheme.primary\n                        )\n                    }\n                    \n                    record.vaccineName?.let { vaccine ->\n                        Text(\n                            text = vaccine,\n                            style = MaterialTheme.typography.bodyMedium\n                        )\n                    }\n                    \n                    record.notes?.let { notes ->\n                        if (notes.isNotBlank()) {\n                            Text(\n                                text = notes,\n                                style = MaterialTheme.typography.bodySmall,\n                                color = MaterialTheme.colorScheme.onSurfaceVariant\n                            )\n                        }\n                    }\n                    \n                    record.doctorName?.let { doctor ->\n                        Text(\n                            text = \"医生: $doctor\",\n                            style = MaterialTheme.typography.bodySmall,\n                            color = MaterialTheme.colorScheme.onSurfaceVariant\n                        )\n                    }\n                    \n                    record.hospitalName?.let { hospital ->\n                        Text(\n                            text = \"医院: $hospital\",\n                            style = MaterialTheme.typography.bodySmall,\n                            color = MaterialTheme.colorScheme.onSurfaceVariant\n                        )\n                    }\n                }\n                \n                Text(\n                    text = dateFormat.format(record.date),\n                    style = MaterialTheme.typography.bodySmall,\n                    color = MaterialTheme.colorScheme.onSurfaceVariant\n                )\n            }\n        }\n    }\n}\n", "modifiedCode": "package com.example.babylog.ui.screens\n\nimport androidx.compose.foundation.background\nimport androidx.compose.foundation.layout.*\nimport androidx.compose.foundation.lazy.LazyColumn\nimport androidx.compose.foundation.lazy.items\nimport androidx.compose.foundation.shape.RoundedCornerShape\nimport androidx.compose.material.icons.Icons\nimport androidx.compose.material.icons.filled.*\nimport androidx.compose.material3.*\nimport androidx.compose.runtime.*\nimport androidx.compose.ui.Alignment\nimport androidx.compose.ui.Modifier\nimport androidx.compose.ui.draw.clip\nimport androidx.compose.ui.graphics.Color\nimport androidx.compose.ui.platform.LocalContext\nimport androidx.compose.ui.text.font.FontWeight\nimport androidx.compose.ui.unit.dp\nimport androidx.navigation.NavController\nimport com.example.babylog.data.database.BabyLogDatabase\nimport com.example.babylog.data.entity.Baby\nimport com.example.babylog.data.entity.HealthRecord\nimport com.example.babylog.data.repository.BabyRepository\nimport com.example.babylog.navigation.Screen\nimport com.example.babylog.ui.theme.*\nimport java.text.SimpleDateFormat\nimport java.util.*\n\n@OptIn(ExperimentalMaterial3Api::class)\n@Composable\nfun HealthScreen(navController: NavController) {\n    val context = LocalContext.current\n    val database = BabyLogDatabase.getDatabase(context)\n    val repository = BabyRepository(\n        database.babyDao(),\n        database.healthRecordDao(),\n        database.milestoneDao(),\n        database.feedingRecordDao(),\n        database.photoDao()\n    )\n    \n    val babies by repository.getAllBabies().collectAsState(initial = emptyList())\n    var selectedBaby by remember { mutableStateOf<Baby?>(null) }\n    var selectedTab by remember { mutableStateOf(0) }\n    \n    // Set first baby as selected if available\n    LaunchedEffect(babies) {\n        if (babies.isNotEmpty() && selectedBaby == null) {\n            selectedBaby = babies.first()\n        }\n    }\n    \n    val healthRecords by remember(selectedBaby) {\n        if (selectedBaby != null) {\n            repository.getHealthRecordsByBaby(selectedBaby!!.id)\n        } else {\n            kotlinx.coroutines.flow.flowOf(emptyList<HealthRecord>())\n        }\n    }.collectAsState(initial = emptyList())\n    \n    val tabs = listOf(\"全部\", \"身高体重\", \"疫苗接种\", \"体检记录\", \"用药记录\")\n    \n    Box(\n        modifier = Modifier\n            .fillMaxSize()\n            .background(NeumorphismColors.background)\n    ) {\n        LazyColumn(\n            modifier = Modifier\n                .fillMaxSize()\n                .padding(horizontal = 20.dp),\n            contentPadding = PaddingValues(vertical = 20.dp),\n            verticalArrangement = Arrangement.spacedBy(20.dp)\n        ) {\n            // Header\n            item {\n                Row(\n                    modifier = Modifier.fillMaxWidth(),\n                    horizontalArrangement = Arrangement.SpaceBetween,\n                    verticalAlignment = Alignment.CenterVertically\n                ) {\n                    Column {\n                        Text(\n                            text = \"健康记录\",\n                            style = MaterialTheme.typography.headlineLarge,\n                            fontWeight = FontWeight.Bold,\n                            color = NeumorphismColors.textPrimary\n                        )\n                        Text(\n                            text = \"记录宝宝的健康成长数据\",\n                            style = MaterialTheme.typography.bodyMedium,\n                            color = NeumorphismColors.textSecondary\n                        )\n                    }\n\n                    selectedBaby?.let { baby ->\n                        NeumorphismFloatingActionButton(\n                            onClick = {\n                                navController.navigate(\"${Screen.AddHealth.route}?babyId=${baby.id}\")\n                            },\n                            modifier = Modifier.size(56.dp)\n                        ) {\n                            Icon(\n                                Icons.Default.Add,\n                                contentDescription = \"添加记录\",\n                                tint = Color.White\n                            )\n                        }\n                    }\n                }\n            }\n\n            // Baby selector\n            if (babies.isNotEmpty()) {\n                item {\n            var expanded by remember { mutableStateOf(false) }\n            \n            ExposedDropdownMenuBox(\n                expanded = expanded,\n                onExpandedChange = { expanded = !expanded }\n            ) {\n                OutlinedTextField(\n                    value = selectedBaby?.name ?: \"\",\n                    onValueChange = { },\n                    readOnly = true,\n                    label = { Text(\"选择宝宝\") },\n                    trailingIcon = {\n                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)\n                    },\n                    modifier = Modifier\n                        .fillMaxWidth()\n                        .menuAnchor()\n                )\n                \n                ExposedDropdownMenu(\n                    expanded = expanded,\n                    onDismissRequest = { expanded = false }\n                ) {\n                    babies.forEach { baby ->\n                        DropdownMenuItem(\n                            text = { Text(baby.name) },\n                            onClick = {\n                                selectedBaby = baby\n                                expanded = false\n                            }\n                        )\n                    }\n                }\n                }\n            }\n\n            // Tabs and content would go here\n            // For now, let's show a simple list\n            items(healthRecords) { record ->\n                HealthRecordCard(record = record)\n            }\n        }\n    }\n}\n\n@Composable\nfun HealthRecordCard(record: HealthRecord) {\n    NeumorphismCard(\n        modifier = Modifier.fillMaxWidth(),\n        elevation = 4,\n        cornerRadius = 16\n    ) {\n        Column {\n            Row(\n                modifier = Modifier.fillMaxWidth(),\n                horizontalArrangement = Arrangement.SpaceBetween,\n                verticalAlignment = Alignment.CenterVertically\n            ) {\n                Text(\n                    text = getHealthTypeText(record.type),\n                    style = MaterialTheme.typography.titleMedium,\n                    fontWeight = FontWeight.Bold,\n                    color = NeumorphismColors.textPrimary\n                )\n                Text(\n                    text = SimpleDateFormat(\"MM-dd\", Locale.getDefault()).format(record.date),\n                    style = MaterialTheme.typography.bodySmall,\n                    color = NeumorphismColors.textSecondary\n                )\n            }\n\n            if (record.value > 0) {\n                Spacer(modifier = Modifier.height(8.dp))\n                Text(\n                    text = \"${record.value}${getHealthUnitText(record.type)}\",\n                    style = MaterialTheme.typography.bodyLarge,\n                    color = NeumorphismColors.primary,\n                    fontWeight = FontWeight.Medium\n                )\n            }\n\n            if (record.notes.isNotBlank()) {\n                Spacer(modifier = Modifier.height(8.dp))\n                Text(\n                    text = record.notes,\n                    style = MaterialTheme.typography.bodyMedium,\n                    color = NeumorphismColors.textSecondary\n                )\n            }\n        }\n    }\n}\n\nprivate fun getHealthTypeText(type: String): String = when (type) {\n    \"weight\" -> \"体重\"\n    \"height\" -> \"身高\"\n    \"vaccination\" -> \"疫苗接种\"\n    \"checkup\" -> \"体检记录\"\n    \"temperature\" -> \"体温\"\n    \"medication\" -> \"用药记录\"\n    else -> type\n}\n\nprivate fun getHealthUnitText(type: String): String = when (type) {\n    \"weight\" -> \"kg\"\n    \"height\" -> \"cm\"\n    \"temperature\" -> \"°C\"\n    else -> \"\"\n}\n                                \"temperature\" -> Icons.Default.Thermostat\n                                \"medication\" -> Icons.Default.MedicalServices\n                                else -> Icons.Default.HealthAndSafety\n                            },\n                            contentDescription = null,\n                            modifier = Modifier.size(20.dp),\n                            tint = MaterialTheme.colorScheme.primary\n                        )\n                        Spacer(modifier = Modifier.width(8.dp))\n                        Text(\n                            text = when (record.type) {\n                                \"weight\" -> \"体重记录\"\n                                \"height\" -> \"身高记录\"\n                                \"vaccination\" -> \"疫苗接种\"\n                                \"checkup\" -> \"体检记录\"\n                                \"temperature\" -> \"体温记录\"\n                                \"medication\" -> \"用药记录\"\n                                else -> \"健康记录\"\n                            },\n                            style = MaterialTheme.typography.titleMedium,\n                            fontWeight = FontWeight.Medium\n                        )\n                    }\n                    \n                    Spacer(modifier = Modifier.height(4.dp))\n                    \n                    record.value?.let { value ->\n                        Text(\n                            text = \"${value}${record.unit ?: \"\"}\",\n                            style = MaterialTheme.typography.bodyLarge,\n                            fontWeight = FontWeight.Bold,\n                            color = MaterialTheme.colorScheme.primary\n                        )\n                    }\n                    \n                    record.vaccineName?.let { vaccine ->\n                        Text(\n                            text = vaccine,\n                            style = MaterialTheme.typography.bodyMedium\n                        )\n                    }\n                    \n                    record.notes?.let { notes ->\n                        if (notes.isNotBlank()) {\n                            Text(\n                                text = notes,\n                                style = MaterialTheme.typography.bodySmall,\n                                color = MaterialTheme.colorScheme.onSurfaceVariant\n                            )\n                        }\n                    }\n                    \n                    record.doctorName?.let { doctor ->\n                        Text(\n                            text = \"医生: $doctor\",\n                            style = MaterialTheme.typography.bodySmall,\n                            color = MaterialTheme.colorScheme.onSurfaceVariant\n                        )\n                    }\n                    \n                    record.hospitalName?.let { hospital ->\n                        Text(\n                            text = \"医院: $hospital\",\n                            style = MaterialTheme.typography.bodySmall,\n                            color = MaterialTheme.colorScheme.onSurfaceVariant\n                        )\n                    }\n                }\n                \n                Text(\n                    text = dateFormat.format(record.date),\n                    style = MaterialTheme.typography.bodySmall,\n                    color = MaterialTheme.colorScheme.onSurfaceVariant\n                )\n            }\n        }\n    }\n}\n"}
{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/services/account_service.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n账号管理服务\n\n提供账号的增删改查、状态检查等功能。\n\"\"\"\n\nimport json\nfrom pathlib import Path\nfrom typing import List, Dict, Any, Optional, Union\nfrom datetime import datetime\n\nfrom ..core.config import Config\nfrom ..core.logger import get_logger\nfrom ..core.exceptions import AccountError, FileOperationError\nfrom ..models.account import Account, CursorAccount, WindsurfAccount\n\n\nclass AccountService:\n    \"\"\"账号管理服务类\"\"\"\n    \n    def __init__(self, config: Config):\n        \"\"\"初始化账号服务\n        \n        Args:\n            config: 配置对象\n        \"\"\"\n        self.config = config\n        self.logger = get_logger()\n    \n    def load_cursor_accounts(self) -> List[CursorAccount]:\n        \"\"\"加载Cursor账号列表\n        \n        Returns:\n            Cursor账号列表\n        \"\"\"\n        try:\n            account_file = self.config.cursor_account_file\n            if not account_file.exists():\n                self.logger.info(\"Cursor账号文件不存在，返回空列表\")\n                return []\n            \n            with open(account_file, 'r', encoding='utf-8') as f:\n                content = f.read().strip()\n                if not content:\n                    return []\n                \n                data = json.loads(content)\n                if not isinstance(data, list):\n                    self.logger.warning(\"Cursor账号文件格式错误，应为列表格式\")\n                    return []\n                \n                accounts = []\n                for item in data:\n                    try:\n                        account = CursorAccount.from_dict(item)\n                        accounts.append(account)\n                    except Exception as e:\n                        self.logger.error(f\"解析Cursor账号数据失败: {e}\")\n                        continue\n                \n                self.logger.info(f\"成功加载 {len(accounts)} 个Cursor账号\")\n                return accounts\n                \n        except json.JSONDecodeError as e:\n            self.logger.error(f\"Cursor账号文件JSON格式错误: {e}\")\n            return []\n        except Exception as e:\n            self.logger.error(f\"加载Cursor账号失败: {e}\")\n            return []\n    \n    def save_cursor_accounts(self, accounts: List[CursorAccount]) -> bool:\n        \"\"\"保存Cursor账号列表\n        \n        Args:\n            accounts: Cursor账号列表\n            \n        Returns:\n            是否保存成功\n        \"\"\"\n        try:\n            account_file = self.config.cursor_account_file\n            \n            # 确保目录存在\n            account_file.parent.mkdir(parents=True, exist_ok=True)\n            \n            # 转换为字典列表\n            data = [account.to_dict() for account in accounts]\n            \n            # 保存到文件\n            with open(account_file, 'w', encoding='utf-8') as f:\n                json.dump(data, f, ensure_ascii=False, indent=2)\n            \n            self.logger.info(f\"成功保存 {len(accounts)} 个Cursor账号\")\n            return True\n            \n        except Exception as e:\n            self.logger.error(f\"保存Cursor账号失败: {e}\")\n            return False\n    \n    def load_windsurf_accounts(self) -> List[WindsurfAccount]:\n        \"\"\"加载Windsurf账号列表\n        \n        Returns:\n            Windsurf账号列表\n        \"\"\"\n        try:\n            account_file = self.config.windsurf_account_file\n            if not account_file.exists():\n                self.logger.info(\"Windsurf账号文件不存在，返回空列表\")\n                return []\n            \n            with open(account_file, 'r', encoding='utf-8') as f:\n                content = f.read().strip()\n                if not content:\n                    return []\n                \n                data = json.loads(content)\n                if not isinstance(data, list):\n                    self.logger.warning(\"Windsurf账号文件格式错误，应为列表格式\")\n                    return []\n                \n                accounts = []\n                for item in data:\n                    try:\n                        account = WindsurfAccount.from_dict(item)\n                        accounts.append(account)\n                    except Exception as e:\n                        self.logger.error(f\"解析Windsurf账号数据失败: {e}\")\n                        continue\n                \n                self.logger.info(f\"成功加载 {len(accounts)} 个Windsurf账号\")\n                return accounts\n                \n        except json.JSONDecodeError as e:\n            self.logger.error(f\"Windsurf账号文件JSON格式错误: {e}\")\n            return []\n        except Exception as e:\n            self.logger.error(f\"加载Windsurf账号失败: {e}\")\n            return []\n    \n    def save_windsurf_accounts(self, accounts: List[WindsurfAccount]) -> bool:\n        \"\"\"保存Windsurf账号列表\n        \n        Args:\n            accounts: Windsurf账号列表\n            \n        Returns:\n            是否保存成功\n        \"\"\"\n        try:\n            account_file = self.config.windsurf_account_file\n            \n            # 确保目录存在\n            account_file.parent.mkdir(parents=True, exist_ok=True)\n            \n            # 转换为字典列表\n            data = [account.to_dict() for account in accounts]\n            \n            # 保存到文件\n            with open(account_file, 'w', encoding='utf-8') as f:\n                json.dump(data, f, ensure_ascii=False, indent=2)\n            \n            self.logger.info(f\"成功保存 {len(accounts)} 个Windsurf账号\")\n            return True\n            \n        except Exception as e:\n            self.logger.error(f\"保存Windsurf账号失败: {e}\")\n            return False\n    \n    def add_cursor_account(self, account: CursorAccount) -> bool:\n        \"\"\"添加Cursor账号\n        \n        Args:\n            account: Cursor账号对象\n            \n        Returns:\n            是否添加成功\n        \"\"\"\n        try:\n            accounts = self.load_cursor_accounts()\n            \n            # 检查是否已存在相同邮箱的账号\n            for existing_account in accounts:\n                if existing_account.email == account.email:\n                    self.logger.warning(f\"Cursor账号 {account.email} 已存在\")\n                    return False\n            \n            accounts.append(account)\n            return self.save_cursor_accounts(accounts)\n            \n        except Exception as e:\n            self.logger.error(f\"添加Cursor账号失败: {e}\")\n            return False\n    \n    def add_windsurf_account(self, account: WindsurfAccount) -> bool:\n        \"\"\"添加Windsurf账号\n        \n        Args:\n            account: Windsurf账号对象\n            \n        Returns:\n            是否添加成功\n        \"\"\"\n        try:\n            accounts = self.load_windsurf_accounts()\n            \n            # 检查是否已存在相同邮箱的账号\n            for existing_account in accounts:\n                if existing_account.email == account.email:\n                    self.logger.warning(f\"Windsurf账号 {account.email} 已存在\")\n                    return False\n            \n            accounts.append(account)\n            return self.save_windsurf_accounts(accounts)\n            \n        except Exception as e:\n            self.logger.error(f\"添加Windsurf账号失败: {e}\")\n            return False\n    \n    def update_cursor_account(self, updated_account: CursorAccount) -> bool:\n        \"\"\"更新Cursor账号\n        \n        Args:\n            updated_account: 更新后的账号对象\n            \n        Returns:\n            是否更新成功\n        \"\"\"\n        try:\n            accounts = self.load_cursor_accounts()\n            \n            for i, account in enumerate(accounts):\n                if account.email == updated_account.email:\n                    accounts[i] = updated_account\n                    return self.save_cursor_accounts(accounts)\n            \n            self.logger.warning(f\"未找到要更新的Cursor账号: {updated_account.email}\")\n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"更新Cursor账号失败: {e}\")\n            return False\n    \n    def update_windsurf_account(self, updated_account: WindsurfAccount) -> bool:\n        \"\"\"更新Windsurf账号\n        \n        Args:\n            updated_account: 更新后的账号对象\n            \n        Returns:\n            是否更新成功\n        \"\"\"\n        try:\n            accounts = self.load_windsurf_accounts()\n            \n            for i, account in enumerate(accounts):\n                if account.email == updated_account.email:\n                    accounts[i] = updated_account\n                    return self.save_windsurf_accounts(accounts)\n            \n            self.logger.warning(f\"未找到要更新的Windsurf账号: {updated_account.email}\")\n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"更新Windsurf账号失败: {e}\")\n            return False\n    \n    def delete_cursor_account(self, email: str) -> bool:\n        \"\"\"删除Cursor账号\n        \n        Args:\n            email: 账号邮箱\n            \n        Returns:\n            是否删除成功\n        \"\"\"\n        try:\n            accounts = self.load_cursor_accounts()\n            \n            original_count = len(accounts)\n            accounts = [acc for acc in accounts if acc.email != email]\n            \n            if len(accounts) < original_count:\n                return self.save_cursor_accounts(accounts)\n            else:\n                self.logger.warning(f\"未找到要删除的Cursor账号: {email}\")\n                return False\n                \n        except Exception as e:\n            self.logger.error(f\"删除Cursor账号失败: {e}\")\n            return False\n    \n    def delete_windsurf_account(self, email: str) -> bool:\n        \"\"\"删除Windsurf账号\n        \n        Args:\n            email: 账号邮箱\n            \n        Returns:\n            是否删除成功\n        \"\"\"\n        try:\n            accounts = self.load_windsurf_accounts()\n            \n            original_count = len(accounts)\n            accounts = [acc for acc in accounts if acc.email != email]\n            \n            if len(accounts) < original_count:\n                return self.save_windsurf_accounts(accounts)\n            else:\n                self.logger.warning(f\"未找到要删除的Windsurf账号: {email}\")\n                return False\n                \n        except Exception as e:\n            self.logger.error(f\"删除Windsurf账号失败: {e}\")\n            return False\n    \n    def find_cursor_account(self, email: str) -> Optional[CursorAccount]:\n        \"\"\"查找Cursor账号\n        \n        Args:\n            email: 账号邮箱\n            \n        Returns:\n            找到的账号对象，未找到返回None\n        \"\"\"\n        try:\n            accounts = self.load_cursor_accounts()\n            for account in accounts:\n                if account.email == email:\n                    return account\n            return None\n            \n        except Exception as e:\n            self.logger.error(f\"查找Cursor账号失败: {e}\")\n            return None\n    \n    def find_windsurf_account(self, email: str) -> Optional[WindsurfAccount]:\n        \"\"\"查找Windsurf账号\n        \n        Args:\n            email: 账号邮箱\n            \n        Returns:\n            找到的账号对象，未找到返回None\n        \"\"\"\n        try:\n            accounts = self.load_windsurf_accounts()\n            for account in accounts:\n                if account.email == email:\n                    return account\n            return None\n            \n        except Exception as e:\n            self.logger.error(f\"查找Windsurf账号失败: {e}\")\n            return None\n"}
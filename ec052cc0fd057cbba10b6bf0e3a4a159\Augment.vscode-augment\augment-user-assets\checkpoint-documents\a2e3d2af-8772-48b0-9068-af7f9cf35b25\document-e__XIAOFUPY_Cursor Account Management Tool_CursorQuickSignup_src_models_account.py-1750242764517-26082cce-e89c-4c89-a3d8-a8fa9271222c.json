{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/models/account.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n账号数据模型\n\n定义账号相关的数据模型类。\n\"\"\"\n\nfrom dataclasses import dataclass, field\nfrom typing import Optional, Dict, Any, List\nfrom datetime import datetime\nfrom enum import Enum\n\nfrom ..core.constants import (\n    ACCOUNT_STATUS_ACTIVE, ACCOUNT_STATUS_INACTIVE, \n    ACCOUNT_STATUS_EXPIRED, ACCOUNT_STATUS_UNKNOWN,\n    MEMBERSHIP_FREE, MEMBERSHIP_PRO, MEMBERSHIP_BUSINESS\n)\n\n\nclass AccountStatus(Enum):\n    \"\"\"账号状态枚举\"\"\"\n    ACTIVE = ACCOUNT_STATUS_ACTIVE\n    INACTIVE = ACCOUNT_STATUS_INACTIVE\n    EXPIRED = ACCOUNT_STATUS_EXPIRED\n    UNKNOWN = ACCOUNT_STATUS_UNKNOWN\n\n\nclass MembershipType(Enum):\n    \"\"\"会员类型枚举\"\"\"\n    FREE = MEMBERSHIP_FREE\n    PRO = MEMBERSHIP_PRO\n    BUSINESS = MEMBERSHIP_BUSINESS\n\n\n@dataclass\nclass MembershipInfo:\n    \"\"\"会员信息\"\"\"\n    membership_type: str = MEMBERSHIP_FREE\n    trial_days_remaining: Optional[int] = None\n    is_trial: bool = False\n    expiry_date: Optional[str] = None\n    \n    def to_dict(self) -> Dict[str, Any]:\n        \"\"\"转换为字典\"\"\"\n        return {\n            'membership_type': self.membership_type,\n            'trial_days_remaining': self.trial_days_remaining,\n            'is_trial': self.is_trial,\n            'expiry_date': self.expiry_date\n        }\n    \n    @classmethod\n    def from_dict(cls, data: Dict[str, Any]) -> 'MembershipInfo':\n        \"\"\"从字典创建实例\"\"\"\n        return cls(\n            membership_type=data.get('membership_type', MEMBERSHIP_FREE),\n            trial_days_remaining=data.get('trial_days_remaining'),\n            is_trial=data.get('is_trial', False),\n            expiry_date=data.get('expiry_date')\n        )\n\n\n@dataclass\nclass UsageInfo:\n    \"\"\"使用量信息\"\"\"\n    gpt4_used: int = 0\n    gpt4_limit: Optional[int] = None\n    gpt4_start_of_month: Optional[str] = None\n    other_models: Dict[str, Dict[str, Any]] = field(default_factory=dict)\n    \n    def to_dict(self) -> Dict[str, Any]:\n        \"\"\"转换为字典\"\"\"\n        return {\n            'gpt4_used': self.gpt4_used,\n            'gpt4_limit': self.gpt4_limit,\n            'gpt4_start_of_month': self.gpt4_start_of_month,\n            'other_models': self.other_models\n        }\n    \n    @classmethod\n    def from_dict(cls, data: Dict[str, Any]) -> 'UsageInfo':\n        \"\"\"从字典创建实例\"\"\"\n        return cls(\n            gpt4_used=data.get('gpt4_used', 0),\n            gpt4_limit=data.get('gpt4_limit'),\n            gpt4_start_of_month=data.get('gpt4_start_of_month'),\n            other_models=data.get('other_models', {})\n        )\n\n\n@dataclass\nclass Account:\n    \"\"\"基础账号模型\"\"\"\n    email: str\n    password: str = \"\"\n    token: str = \"\"\n    status: str = ACCOUNT_STATUS_UNKNOWN\n    created_at: Optional[str] = None\n    last_check: Optional[str] = None\n    membership_info: Optional[MembershipInfo] = None\n    usage_info: Optional[UsageInfo] = None\n    extra_data: Dict[str, Any] = field(default_factory=dict)\n    \n    def __post_init__(self):\n        \"\"\"初始化后处理\"\"\"\n        if self.created_at is None:\n            self.created_at = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n        \n        if self.membership_info is None:\n            self.membership_info = MembershipInfo()\n        \n        if self.usage_info is None:\n            self.usage_info = UsageInfo()\n    \n    def to_dict(self) -> Dict[str, Any]:\n        \"\"\"转换为字典\"\"\"\n        data = {\n            'email': self.email,\n            'password': self.password,\n            'token': self.token,\n            'status': self.status,\n            'created_at': self.created_at,\n            'last_check': self.last_check,\n            'membership_info': self.membership_info.to_dict() if self.membership_info else None,\n            'usage_info': self.usage_info.to_dict() if self.usage_info else None,\n        }\n        data.update(self.extra_data)\n        return data\n    \n    @classmethod\n    def from_dict(cls, data: Dict[str, Any]) -> 'Account':\n        \"\"\"从字典创建实例\"\"\"\n        # 提取基础字段\n        base_fields = {\n            'email', 'password', 'token', 'status', \n            'created_at', 'last_check'\n        }\n        \n        kwargs = {}\n        extra_data = {}\n        \n        for key, value in data.items():\n            if key in base_fields:\n                kwargs[key] = value\n            elif key == 'membership_info' and value:\n                kwargs['membership_info'] = MembershipInfo.from_dict(value)\n            elif key == 'usage_info' and value:\n                kwargs['usage_info'] = UsageInfo.from_dict(value)\n            else:\n                extra_data[key] = value\n        \n        kwargs['extra_data'] = extra_data\n        return cls(**kwargs)\n    \n    def update_status(self, status: str):\n        \"\"\"更新状态\"\"\"\n        self.status = status\n        self.last_check = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n    \n    def is_valid(self) -> bool:\n        \"\"\"检查账号是否有效\"\"\"\n        return self.status == ACCOUNT_STATUS_ACTIVE and bool(self.token)\n\n\n@dataclass\nclass CursorAccount(Account):\n    \"\"\"Cursor账号模型\"\"\"\n    \n    def __post_init__(self):\n        \"\"\"初始化后处理\"\"\"\n        super().__post_init__()\n        # Cursor特有的初始化逻辑\n        if 'platform' not in self.extra_data:\n            self.extra_data['platform'] = 'cursor'\n    \n    @property\n    def token_info(self) -> Dict[str, Any]:\n        \"\"\"获取Token信息\"\"\"\n        return self.extra_data.get('token_info', {})\n    \n    @token_info.setter\n    def token_info(self, value: Dict[str, Any]):\n        \"\"\"设置Token信息\"\"\"\n        self.extra_data['token_info'] = value\n    \n    def get_gpt4_quota_info(self) -> str:\n        \"\"\"获取GPT-4额度信息\"\"\"\n        if not self.usage_info:\n            return \"无使用量信息\"\n        \n        used = self.usage_info.gpt4_used\n        limit = self.usage_info.gpt4_limit\n        \n        if limit is None:\n            return f\"已使用: {used} 次\\n总额度: 无限制\"\n        \n        remaining = limit - used\n        return f\"已使用: {used} 次\\n总额度: {limit} 次\\n剩余: {remaining} 次\"\n\n\n@dataclass\nclass WindsurfAccount(Account):\n    \"\"\"Windsurf账号模型\"\"\"\n    first_name: str = \"\"\n    last_name: str = \"\"\n    \n    def __post_init__(self):\n        \"\"\"初始化后处理\"\"\"\n        super().__post_init__()\n        # Windsurf特有的初始化逻辑\n        if 'platform' not in self.extra_data:\n            self.extra_data['platform'] = 'windsurf'\n    \n    @property\n    def full_name(self) -> str:\n        \"\"\"获取全名\"\"\"\n        return f\"{self.first_name} {self.last_name}\".strip()\n    \n    def to_dict(self) -> Dict[str, Any]:\n        \"\"\"转换为字典\"\"\"\n        data = super().to_dict()\n        data.update({\n            'first_name': self.first_name,\n            'last_name': self.last_name,\n        })\n        return data\n    \n    @classmethod\n    def from_dict(cls, data: Dict[str, Any]) -> 'WindsurfAccount':\n        \"\"\"从字典创建实例\"\"\"\n        # 提取基础字段\n        base_fields = {\n            'email', 'password', 'token', 'status', \n            'created_at', 'last_check', 'first_name', 'last_name'\n        }\n        \n        kwargs = {}\n        extra_data = {}\n        \n        for key, value in data.items():\n            if key in base_fields:\n                kwargs[key] = value\n            elif key == 'membership_info' and value:\n                kwargs['membership_info'] = MembershipInfo.from_dict(value)\n            elif key == 'usage_info' and value:\n                kwargs['usage_info'] = UsageInfo.from_dict(value)\n            else:\n                extra_data[key] = value\n        \n        kwargs['extra_data'] = extra_data\n        return cls(**kwargs)\n"}
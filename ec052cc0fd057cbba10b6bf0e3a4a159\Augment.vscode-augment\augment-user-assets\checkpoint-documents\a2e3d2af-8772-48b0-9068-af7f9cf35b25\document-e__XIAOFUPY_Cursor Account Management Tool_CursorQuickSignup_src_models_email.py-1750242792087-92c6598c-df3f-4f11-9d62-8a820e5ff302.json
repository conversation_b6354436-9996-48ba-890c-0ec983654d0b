{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/models/email.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n邮箱数据模型\n\n定义邮箱相关的数据模型类。\n\"\"\"\n\nfrom dataclasses import dataclass, field\nfrom typing import Optional, Dict, Any, List\nfrom datetime import datetime\nfrom enum import Enum\n\n\nclass EmailType(Enum):\n    \"\"\"邮件类型枚举\"\"\"\n    VERIFICATION = \"verification\"\n    NOTIFICATION = \"notification\"\n    MARKETING = \"marketing\"\n    OTHER = \"other\"\n\n\n@dataclass\nclass EmailMessage:\n    \"\"\"邮件消息模型\"\"\"\n    id: str\n    sender: str\n    recipient: str\n    subject: str\n    content: str\n    received_at: str\n    email_type: str = EmailType.OTHER.value\n    is_read: bool = False\n    attachments: List[str] = field(default_factory=list)\n    headers: Dict[str, str] = field(default_factory=dict)\n    \n    def __post_init__(self):\n        \"\"\"初始化后处理\"\"\"\n        if not self.received_at:\n            self.received_at = datetime.now().isoformat()\n    \n    def to_dict(self) -> Dict[str, Any]:\n        \"\"\"转换为字典\"\"\"\n        return {\n            'id': self.id,\n            'sender': self.sender,\n            'recipient': self.recipient,\n            'subject': self.subject,\n            'content': self.content,\n            'received_at': self.received_at,\n            'email_type': self.email_type,\n            'is_read': self.is_read,\n            'attachments': self.attachments,\n            'headers': self.headers\n        }\n    \n    @classmethod\n    def from_dict(cls, data: Dict[str, Any]) -> 'EmailMessage':\n        \"\"\"从字典创建实例\"\"\"\n        return cls(\n            id=data.get('id', ''),\n            sender=data.get('sender', ''),\n            recipient=data.get('recipient', ''),\n            subject=data.get('subject', ''),\n            content=data.get('content', ''),\n            received_at=data.get('received_at', ''),\n            email_type=data.get('email_type', EmailType.OTHER.value),\n            is_read=data.get('is_read', False),\n            attachments=data.get('attachments', []),\n            headers=data.get('headers', {})\n        )\n    \n    def mark_as_read(self):\n        \"\"\"标记为已读\"\"\"\n        self.is_read = True\n    \n    def is_verification_email(self) -> bool:\n        \"\"\"检查是否为验证邮件\"\"\"\n        return self.email_type == EmailType.VERIFICATION.value\n    \n    def contains_verification_code(self) -> bool:\n        \"\"\"检查是否包含验证码\"\"\"\n        # 简单的验证码检测逻辑\n        import re\n        code_patterns = [\n            r'\\b\\d{4,8}\\b',  # 4-8位数字\n            r'\\b[A-Z0-9]{4,8}\\b',  # 4-8位大写字母和数字\n        ]\n        \n        for pattern in code_patterns:\n            if re.search(pattern, self.content):\n                return True\n        return False\n\n\n@dataclass\nclass EmailVerificationCode:\n    \"\"\"邮箱验证码模型\"\"\"\n    code: str\n    email: str\n    service: str  # cursor, windsurf, etc.\n    extracted_at: str\n    expires_at: Optional[str] = None\n    is_used: bool = False\n    source_message_id: Optional[str] = None\n    \n    def __post_init__(self):\n        \"\"\"初始化后处理\"\"\"\n        if not self.extracted_at:\n            self.extracted_at = datetime.now().isoformat()\n    \n    def to_dict(self) -> Dict[str, Any]:\n        \"\"\"转换为字典\"\"\"\n        return {\n            'code': self.code,\n            'email': self.email,\n            'service': self.service,\n            'extracted_at': self.extracted_at,\n            'expires_at': self.expires_at,\n            'is_used': self.is_used,\n            'source_message_id': self.source_message_id\n        }\n    \n    @classmethod\n    def from_dict(cls, data: Dict[str, Any]) -> 'EmailVerificationCode':\n        \"\"\"从字典创建实例\"\"\"\n        return cls(\n            code=data.get('code', ''),\n            email=data.get('email', ''),\n            service=data.get('service', ''),\n            extracted_at=data.get('extracted_at', ''),\n            expires_at=data.get('expires_at'),\n            is_used=data.get('is_used', False),\n            source_message_id=data.get('source_message_id')\n        )\n    \n    def mark_as_used(self):\n        \"\"\"标记为已使用\"\"\"\n        self.is_used = True\n    \n    def is_expired(self) -> bool:\n        \"\"\"检查是否已过期\"\"\"\n        if not self.expires_at:\n            return False\n        \n        try:\n            expires = datetime.fromisoformat(self.expires_at)\n            return datetime.now() > expires\n        except ValueError:\n            return False\n    \n    def is_valid(self) -> bool:\n        \"\"\"检查验证码是否有效\"\"\"\n        return not self.is_used and not self.is_expired()\n\n\n@dataclass\nclass EmailAccount:\n    \"\"\"邮箱账号模型\"\"\"\n    email: str\n    password: str = \"\"\n    provider: str = \"\"  # gmail, outlook, temp_mail, etc.\n    imap_server: str = \"\"\n    imap_port: int = 993\n    smtp_server: str = \"\"\n    smtp_port: int = 587\n    use_ssl: bool = True\n    is_active: bool = True\n    last_check: Optional[str] = None\n    settings: Dict[str, Any] = field(default_factory=dict)\n    \n    def to_dict(self) -> Dict[str, Any]:\n        \"\"\"转换为字典\"\"\"\n        return {\n            'email': self.email,\n            'password': self.password,\n            'provider': self.provider,\n            'imap_server': self.imap_server,\n            'imap_port': self.imap_port,\n            'smtp_server': self.smtp_server,\n            'smtp_port': self.smtp_port,\n            'use_ssl': self.use_ssl,\n            'is_active': self.is_active,\n            'last_check': self.last_check,\n            'settings': self.settings\n        }\n    \n    @classmethod\n    def from_dict(cls, data: Dict[str, Any]) -> 'EmailAccount':\n        \"\"\"从字典创建实例\"\"\"\n        return cls(\n            email=data.get('email', ''),\n            password=data.get('password', ''),\n            provider=data.get('provider', ''),\n            imap_server=data.get('imap_server', ''),\n            imap_port=data.get('imap_port', 993),\n            smtp_server=data.get('smtp_server', ''),\n            smtp_port=data.get('smtp_port', 587),\n            use_ssl=data.get('use_ssl', True),\n            is_active=data.get('is_active', True),\n            last_check=data.get('last_check'),\n            settings=data.get('settings', {})\n        )\n    \n    def update_last_check(self):\n        \"\"\"更新最后检查时间\"\"\"\n        self.last_check = datetime.now().isoformat()\n    \n    def is_temp_mail(self) -> bool:\n        \"\"\"检查是否为临时邮箱\"\"\"\n        temp_mail_domains = [\n            'mailto.plus', 'tempmail.plus', '10minutemail.com',\n            'guerrillamail.com', 'mailinator.com'\n        ]\n        domain = self.email.split('@')[-1].lower()\n        return domain in temp_mail_domains\n"}
{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src\\services\\email_service.py"}, "originalCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n邮箱服务\n\n提供邮箱验证码获取、邮件处理等功能。\n\"\"\"\n\nimport re\nimport time\nimport requests\nfrom typing import Optional, List, Dict, Any, Tuple\nfrom datetime import datetime, timedelta\n\nfrom ..core.config import Config\nfrom ..core.logger import get_logger\nfrom ..core.exceptions import EmailError, NetworkError\nfrom ..core.constants import (\n    TEMP_MAIL_API_BASE, DEFAULT_CURSOR_SENDER, DEFAULT_CURSOR_SUBJECT,\n    DEFAULT_WINDSURF_SENDER, DEFAULT_WINDSURF_SUBJECT, VERIFICATION_CODE_TIMEOUT\n)\nfrom ..models.email import EmailMessage, EmailVerificationCode, EmailAccount\n\n\nclass EmailService:\n    \"\"\"邮箱服务类\"\"\"\n    \n    def __init__(self, config: Config):\n        \"\"\"初始化邮箱服务\n        \n        Args:\n            config: 配置对象\n        \"\"\"\n        self.config = config\n        self.logger = get_logger()\n        self.session = requests.Session()\n        self.session.headers.update({\n            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n        })\n    \n    def generate_temp_email(self) -> str:\n        \"\"\"生成临时邮箱地址\n        \n        Returns:\n            临时邮箱地址\n        \"\"\"\n        try:\n            base_email = self.config.get_temp_mail()\n            domain = self.config.get_temp_mail_ext()\n            \n            # 添加随机后缀\n            import random\n            import string\n            suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))\n            \n            email = f\"{base_email}{suffix}{domain}\"\n            self.logger.info(f\"生成临时邮箱: {email}\")\n            return email\n            \n        except Exception as e:\n            self.logger.error(f\"生成临时邮箱失败: {e}\")\n            raise EmailError(f\"生成临时邮箱失败: {e}\")\n    \n    def get_temp_mail_messages(self, email: str, timeout: int = VERIFICATION_CODE_TIMEOUT) -> List[EmailMessage]:\n        \"\"\"获取临时邮箱的邮件列表\n        \n        Args:\n            email: 邮箱地址\n            timeout: 超时时间（秒）\n            \n        Returns:\n            邮件列表\n        \"\"\"\n        try:\n            # 提取邮箱用户名和域名\n            username, domain = email.split('@', 1)\n            \n            # 构建API URL\n            api_url = f\"{TEMP_MAIL_API_BASE}/mails\"\n            params = {\n                'email': email,\n                'epin': self.config.get_temp_mail_epin()\n            }\n            \n            start_time = time.time()\n            messages = []\n            \n            while time.time() - start_time < timeout:\n                try:\n                    response = self.session.get(api_url, params=params, timeout=10)\n                    response.raise_for_status()\n                    \n                    data = response.json()\n                    if data.get('success') and data.get('result'):\n                        for item in data['result']:\n                            message = EmailMessage(\n                                id=item.get('id', ''),\n                                sender=item.get('from', ''),\n                                recipient=email,\n                                subject=item.get('subject', ''),\n                                content=item.get('body', ''),\n                                received_at=item.get('date', ''),\n                                email_type='verification' if self._is_verification_email(item) else 'other'\n                            )\n                            messages.append(message)\n                        \n                        if messages:\n                            self.logger.info(f\"获取到 {len(messages)} 封邮件\")\n                            return messages\n                    \n                    time.sleep(2)  # 等待2秒后重试\n                    \n                except requests.RequestException as e:\n                    self.logger.warning(f\"获取邮件失败，重试中: {e}\")\n                    time.sleep(5)\n                    continue\n            \n            self.logger.warning(f\"超时未获取到邮件: {email}\")\n            return messages\n            \n        except Exception as e:\n            self.logger.error(f\"获取临时邮箱邮件失败: {e}\")\n            raise EmailError(f\"获取临时邮箱邮件失败: {e}\")\n    \n    def _is_verification_email(self, email_data: Dict[str, Any]) -> bool:\n        \"\"\"判断是否为验证邮件\n        \n        Args:\n            email_data: 邮件数据\n            \n        Returns:\n            是否为验证邮件\n        \"\"\"\n        sender = email_data.get('from', '').lower()\n        subject = email_data.get('subject', '').lower()\n        \n        # 检查发件人\n        verification_senders = [\n            DEFAULT_CURSOR_SENDER.lower(),\n            DEFAULT_WINDSURF_SENDER.lower(),\n            'no-reply@',\n            'noreply@'\n        ]\n        \n        for vs in verification_senders:\n            if vs in sender:\n                return True\n        \n        # 检查主题\n        verification_subjects = [\n            'verify', 'verification', 'confirm', 'activation',\n            '验证', '确认', '激活'\n        ]\n        \n        for vs in verification_subjects:\n            if vs in subject:\n                return True\n        \n        return False\n\n    def extract_verification_code(self, message: EmailMessage, service: str = \"unknown\") -> Optional[EmailVerificationCode]:\n        \"\"\"从邮件中提取验证码\n\n        Args:\n            message: 邮件对象\n            service: 服务名称（cursor, windsurf等）\n\n        Returns:\n            验证码对象，未找到返回None\n        \"\"\"\n        try:\n            content = message.content\n\n            # 验证码匹配模式\n            patterns = [\n                r'验证码[：:\\s]*([A-Z0-9]{4,8})',  # 中文验证码\n                r'verification code[：:\\s]*([A-Z0-9]{4,8})',  # 英文验证码\n                r'code[：:\\s]*([A-Z0-9]{4,8})',  # 简单code\n                r'\\b([A-Z0-9]{6})\\b',  # 6位大写字母数字\n                r'\\b(\\d{4,8})\\b',  # 4-8位数字\n            ]\n\n            for pattern in patterns:\n                matches = re.findall(pattern, content, re.IGNORECASE)\n                if matches:\n                    code = matches[0]\n\n                    # 验证码长度检查\n                    if 4 <= len(code) <= 8:\n                        verification_code = EmailVerificationCode(\n                            code=code,\n                            email=message.recipient,\n                            service=service,\n                            extracted_at=datetime.now().isoformat(),\n                            source_message_id=message.id\n                        )\n\n                        self.logger.info(f\"提取到验证码: {code}\")\n                        return verification_code\n\n            self.logger.warning(f\"未在邮件中找到验证码: {message.subject}\")\n            return None\n\n        except Exception as e:\n            self.logger.error(f\"提取验证码失败: {e}\")\n            return None\n\n    def get_cursor_verification_code(self, email: str, timeout: int = VERIFICATION_CODE_TIMEOUT) -> Optional[str]:\n        \"\"\"获取Cursor验证码\n\n        Args:\n            email: 邮箱地址\n            timeout: 超时时间（秒）\n\n        Returns:\n            验证码，未找到返回None\n        \"\"\"\n        try:\n            self.logger.info(f\"开始获取Cursor验证码: {email}\")\n\n            messages = self.get_temp_mail_messages(email, timeout)\n\n            for message in messages:\n                # 检查是否为Cursor验证邮件\n                if (DEFAULT_CURSOR_SENDER.lower() in message.sender.lower() or\n                    DEFAULT_CURSOR_SUBJECT.lower() in message.subject.lower()):\n\n                    verification_code = self.extract_verification_code(message, \"cursor\")\n                    if verification_code:\n                        return verification_code.code\n\n            self.logger.warning(f\"未获取到Cursor验证码: {email}\")\n            return None\n\n        except Exception as e:\n            self.logger.error(f\"获取Cursor验证码失败: {e}\")\n            return None\n\n    def get_windsurf_verification_code(self, email: str, timeout: int = VERIFICATION_CODE_TIMEOUT) -> Optional[str]:\n        \"\"\"获取Windsurf验证码\n\n        Args:\n            email: 邮箱地址\n            timeout: 超时时间（秒）\n\n        Returns:\n            验证码，未找到返回None\n        \"\"\"\n        try:\n            self.logger.info(f\"开始获取Windsurf验证码: {email}\")\n\n            messages = self.get_temp_mail_messages(email, timeout)\n\n            for message in messages:\n                # 检查是否为Windsurf验证邮件\n                if (DEFAULT_WINDSURF_SENDER.lower() in message.sender.lower() or\n                    DEFAULT_WINDSURF_SUBJECT.lower() in message.subject.lower()):\n\n                    verification_code = self.extract_verification_code(message, \"windsurf\")\n                    if verification_code:\n                        return verification_code.code\n\n            self.logger.warning(f\"未获取到Windsurf验证码: {email}\")\n            return None\n\n        except Exception as e:\n            self.logger.error(f\"获取Windsurf验证码失败: {e}\")\n            return None\n\n    def wait_for_verification_email(self, email: str, service: str,\n                                  timeout: int = VERIFICATION_CODE_TIMEOUT) -> Optional[EmailVerificationCode]:\n        \"\"\"等待验证邮件并提取验证码\n\n        Args:\n            email: 邮箱地址\n            service: 服务名称\n            timeout: 超时时间（秒）\n\n        Returns:\n            验证码对象，未找到返回None\n        \"\"\"\n        try:\n            self.logger.info(f\"等待 {service} 验证邮件: {email}\")\n\n            start_time = time.time()\n\n            while time.time() - start_time < timeout:\n                messages = self.get_temp_mail_messages(email, 30)  # 每次等待30秒\n\n                for message in messages:\n                    if message.is_verification_email():\n                        verification_code = self.extract_verification_code(message, service)\n                        if verification_code:\n                            return verification_code\n\n                time.sleep(10)  # 等待10秒后重试\n\n            self.logger.warning(f\"超时未收到 {service} 验证邮件: {email}\")\n            return None\n\n        except Exception as e:\n            self.logger.error(f\"等待验证邮件失败: {e}\")\n            return None\n", "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n邮箱服务\n\n提供邮箱验证码获取、邮件处理等功能。\n\"\"\"\n\nimport re\nimport time\nimport requests\nfrom typing import Optional, List, Dict, Any, Tuple\nfrom datetime import datetime, timedelta\n\nfrom ..core.config import Config\nfrom ..core.logger import get_logger\nfrom ..core.exceptions import EmailError, NetworkError\nfrom ..core.constants import (\n    TEMP_MAIL_API_BASE, DEFAULT_CURSOR_SENDER, DEFAULT_CURSOR_SUBJECT,\n    DEFAULT_WINDSURF_SENDER, DEFAULT_WINDSURF_SUBJECT, VERIFICATION_CODE_TIMEOUT\n)\nfrom ..models.email import EmailMessage, EmailVerificationCode, EmailAccount\n\n\nclass EmailService:\n    \"\"\"邮箱服务类\"\"\"\n    \n    def __init__(self, config: Config):\n        \"\"\"初始化邮箱服务\n        \n        Args:\n            config: 配置对象\n        \"\"\"\n        self.config = config\n        self.logger = get_logger()\n        self.session = requests.Session()\n        self.session.headers.update({\n            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n        })\n    \n    def generate_temp_email(self) -> str:\n        \"\"\"生成临时邮箱地址\n        \n        Returns:\n            临时邮箱地址\n        \"\"\"\n        try:\n            base_email = self.config.get_temp_mail()\n            domain = self.config.get_temp_mail_ext()\n            \n            # 添加随机后缀\n            import random\n            import string\n            suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))\n            \n            email = f\"{base_email}{suffix}{domain}\"\n            self.logger.info(f\"生成临时邮箱: {email}\")\n            return email\n            \n        except Exception as e:\n            self.logger.error(f\"生成临时邮箱失败: {e}\")\n            raise EmailError(f\"生成临时邮箱失败: {e}\")\n    \n    def get_temp_mail_messages(self, email: str, timeout: int = VERIFICATION_CODE_TIMEOUT) -> List[EmailMessage]:\n        \"\"\"获取临时邮箱的邮件列表\n        \n        Args:\n            email: 邮箱地址\n            timeout: 超时时间（秒）\n            \n        Returns:\n            邮件列表\n        \"\"\"\n        try:\n            # 提取邮箱用户名和域名\n            username, domain = email.split('@', 1)\n            \n            # 构建API URL\n            api_url = f\"{TEMP_MAIL_API_BASE}/mails\"\n            params = {\n                'email': email,\n                'epin': self.config.get_temp_mail_epin()\n            }\n            \n            start_time = time.time()\n            messages = []\n            \n            while time.time() - start_time < timeout:\n                try:\n                    response = self.session.get(api_url, params=params, timeout=10)\n                    response.raise_for_status()\n                    \n                    data = response.json()\n                    if data.get('success') and data.get('result'):\n                        for item in data['result']:\n                            message = EmailMessage(\n                                id=item.get('id', ''),\n                                sender=item.get('from', ''),\n                                recipient=email,\n                                subject=item.get('subject', ''),\n                                content=item.get('body', ''),\n                                received_at=item.get('date', ''),\n                                email_type='verification' if self._is_verification_email(item) else 'other'\n                            )\n                            messages.append(message)\n                        \n                        if messages:\n                            self.logger.info(f\"获取到 {len(messages)} 封邮件\")\n                            return messages\n                    \n                    time.sleep(2)  # 等待2秒后重试\n                    \n                except requests.RequestException as e:\n                    self.logger.warning(f\"获取邮件失败，重试中: {e}\")\n                    time.sleep(5)\n                    continue\n            \n            self.logger.warning(f\"超时未获取到邮件: {email}\")\n            return messages\n            \n        except Exception as e:\n            self.logger.error(f\"获取临时邮箱邮件失败: {e}\")\n            raise EmailError(f\"获取临时邮箱邮件失败: {e}\")\n    \n    def _is_verification_email(self, email_data: Dict[str, Any]) -> bool:\n        \"\"\"判断是否为验证邮件\n        \n        Args:\n            email_data: 邮件数据\n            \n        Returns:\n            是否为验证邮件\n        \"\"\"\n        sender = email_data.get('from', '').lower()\n        subject = email_data.get('subject', '').lower()\n        \n        # 检查发件人\n        verification_senders = [\n            DEFAULT_CURSOR_SENDER.lower(),\n            DEFAULT_WINDSURF_SENDER.lower(),\n            'no-reply@',\n            'noreply@'\n        ]\n        \n        for vs in verification_senders:\n            if vs in sender:\n                return True\n        \n        # 检查主题\n        verification_subjects = [\n            'verify', 'verification', 'confirm', 'activation',\n            '验证', '确认', '激活'\n        ]\n        \n        for vs in verification_subjects:\n            if vs in subject:\n                return True\n        \n        return False\n\n    def extract_verification_code(self, message: EmailMessage, service: str = \"unknown\") -> Optional[EmailVerificationCode]:\n        \"\"\"从邮件中提取验证码\n\n        Args:\n            message: 邮件对象\n            service: 服务名称（cursor, windsurf等）\n\n        Returns:\n            验证码对象，未找到返回None\n        \"\"\"\n        try:\n            content = message.content\n\n            # 验证码匹配模式\n            patterns = [\n                r'验证码[：:\\s]*([A-Z0-9]{4,8})',  # 中文验证码\n                r'verification code[：:\\s]*([A-Z0-9]{4,8})',  # 英文验证码\n                r'code[：:\\s]*([A-Z0-9]{4,8})',  # 简单code\n                r'\\b([A-Z0-9]{6})\\b',  # 6位大写字母数字\n                r'\\b(\\d{4,8})\\b',  # 4-8位数字\n            ]\n\n            for pattern in patterns:\n                matches = re.findall(pattern, content, re.IGNORECASE)\n                if matches:\n                    code = matches[0]\n\n                    # 验证码长度检查\n                    if 4 <= len(code) <= 8:\n                        verification_code = EmailVerificationCode(\n                            code=code,\n                            email=message.recipient,\n                            service=service,\n                            extracted_at=datetime.now().isoformat(),\n                            source_message_id=message.id\n                        )\n\n                        self.logger.info(f\"提取到验证码: {code}\")\n                        return verification_code\n\n            self.logger.warning(f\"未在邮件中找到验证码: {message.subject}\")\n            return None\n\n        except Exception as e:\n            self.logger.error(f\"提取验证码失败: {e}\")\n            return None\n\n    def get_cursor_verification_code(self, email: str, timeout: int = VERIFICATION_CODE_TIMEOUT) -> Optional[str]:\n        \"\"\"获取Cursor验证码\n\n        Args:\n            email: 邮箱地址\n            timeout: 超时时间（秒）\n\n        Returns:\n            验证码，未找到返回None\n        \"\"\"\n        try:\n            self.logger.info(f\"开始获取Cursor验证码: {email}\")\n\n            messages = self.get_temp_mail_messages(email, timeout)\n\n            for message in messages:\n                # 检查是否为Cursor验证邮件\n                if (DEFAULT_CURSOR_SENDER.lower() in message.sender.lower() or\n                    DEFAULT_CURSOR_SUBJECT.lower() in message.subject.lower()):\n\n                    verification_code = self.extract_verification_code(message, \"cursor\")\n                    if verification_code:\n                        return verification_code.code\n\n            self.logger.warning(f\"未获取到Cursor验证码: {email}\")\n            return None\n\n        except Exception as e:\n            self.logger.error(f\"获取Cursor验证码失败: {e}\")\n            return None\n\n    def get_windsurf_verification_code(self, email: str, timeout: int = VERIFICATION_CODE_TIMEOUT) -> Optional[str]:\n        \"\"\"获取Windsurf验证码\n\n        Args:\n            email: 邮箱地址\n            timeout: 超时时间（秒）\n\n        Returns:\n            验证码，未找到返回None\n        \"\"\"\n        try:\n            self.logger.info(f\"开始获取Windsurf验证码: {email}\")\n\n            messages = self.get_temp_mail_messages(email, timeout)\n\n            for message in messages:\n                # 检查是否为Windsurf验证邮件\n                if (DEFAULT_WINDSURF_SENDER.lower() in message.sender.lower() or\n                    DEFAULT_WINDSURF_SUBJECT.lower() in message.subject.lower()):\n\n                    verification_code = self.extract_verification_code(message, \"windsurf\")\n                    if verification_code:\n                        return verification_code.code\n\n            self.logger.warning(f\"未获取到Windsurf验证码: {email}\")\n            return None\n\n        except Exception as e:\n            self.logger.error(f\"获取Windsurf验证码失败: {e}\")\n            return None\n\n    def wait_for_verification_email(self, email: str, service: str,\n                                  timeout: int = VERIFICATION_CODE_TIMEOUT) -> Optional[EmailVerificationCode]:\n        \"\"\"等待验证邮件并提取验证码\n\n        Args:\n            email: 邮箱地址\n            service: 服务名称\n            timeout: 超时时间（秒）\n\n        Returns:\n            验证码对象，未找到返回None\n        \"\"\"\n        try:\n            self.logger.info(f\"等待 {service} 验证邮件: {email}\")\n\n            start_time = time.time()\n\n            while time.time() - start_time < timeout:\n                messages = self.get_temp_mail_messages(email, 30)  # 每次等待30秒\n\n                for message in messages:\n                    if message.is_verification_email():\n                        verification_code = self.extract_verification_code(message, service)\n                        if verification_code:\n                            return verification_code\n\n                time.sleep(10)  # 等待10秒后重试\n\n            self.logger.warning(f\"超时未收到 {service} 验证邮件: {email}\")\n            return None\n\n        except Exception as e:\n            self.logger.error(f\"等待验证邮件失败: {e}\")\n            return None\n"}
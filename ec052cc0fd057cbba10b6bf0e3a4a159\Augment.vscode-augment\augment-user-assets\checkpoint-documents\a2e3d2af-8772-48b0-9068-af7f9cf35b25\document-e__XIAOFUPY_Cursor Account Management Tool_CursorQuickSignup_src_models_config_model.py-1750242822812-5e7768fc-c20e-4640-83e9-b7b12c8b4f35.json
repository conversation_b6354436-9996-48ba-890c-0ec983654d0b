{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/models/config_model.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n配置数据模型\n\n定义配置相关的数据模型类。\n\"\"\"\n\nfrom dataclasses import dataclass, field\nfrom typing import Dict, Any, Optional, List\nfrom enum import Enum\n\n\nclass BrowserType(Enum):\n    \"\"\"浏览器类型枚举\"\"\"\n    CHROME = \"chrome\"\n    FIREFOX = \"firefox\"\n    EDGE = \"edge\"\n    SAFARI = \"safari\"\n\n\nclass EmailProvider(Enum):\n    \"\"\"邮箱提供商枚举\"\"\"\n    TEMP_MAIL = \"temp_mail\"\n    GMAIL = \"gmail\"\n    OUTLOOK = \"outlook\"\n    CUSTOM = \"custom\"\n\n\n@dataclass\nclass BrowserConfig:\n    \"\"\"浏览器配置模型\"\"\"\n    browser_type: str = BrowserType.CHROME.value\n    headless: bool = True\n    window_size: tuple = (1920, 1080)\n    user_agent: str = \"\"\n    proxy: Optional[str] = None\n    download_dir: Optional[str] = None\n    extensions: List[str] = field(default_factory=list)\n    options: Dict[str, Any] = field(default_factory=dict)\n    \n    def to_dict(self) -> Dict[str, Any]:\n        \"\"\"转换为字典\"\"\"\n        return {\n            'browser_type': self.browser_type,\n            'headless': self.headless,\n            'window_size': list(self.window_size),\n            'user_agent': self.user_agent,\n            'proxy': self.proxy,\n            'download_dir': self.download_dir,\n            'extensions': self.extensions,\n            'options': self.options\n        }\n    \n    @classmethod\n    def from_dict(cls, data: Dict[str, Any]) -> 'BrowserConfig':\n        \"\"\"从字典创建实例\"\"\"\n        window_size = data.get('window_size', [1920, 1080])\n        if isinstance(window_size, list) and len(window_size) >= 2:\n            window_size = tuple(window_size[:2])\n        else:\n            window_size = (1920, 1080)\n        \n        return cls(\n            browser_type=data.get('browser_type', BrowserType.CHROME.value),\n            headless=data.get('headless', True),\n            window_size=window_size,\n            user_agent=data.get('user_agent', ''),\n            proxy=data.get('proxy'),\n            download_dir=data.get('download_dir'),\n            extensions=data.get('extensions', []),\n            options=data.get('options', {})\n        )\n\n\n@dataclass\nclass EmailConfig:\n    \"\"\"邮箱配置模型\"\"\"\n    provider: str = EmailProvider.TEMP_MAIL.value\n    temp_mail_address: str = \"\"\n    temp_mail_pin: str = \"\"\n    temp_mail_domain: str = \"\"\n    imap_server: str = \"\"\n    imap_port: int = 993\n    smtp_server: str = \"\"\n    smtp_port: int = 587\n    username: str = \"\"\n    password: str = \"\"\n    use_ssl: bool = True\n    timeout: int = 30\n    \n    def to_dict(self) -> Dict[str, Any]:\n        \"\"\"转换为字典\"\"\"\n        return {\n            'provider': self.provider,\n            'temp_mail_address': self.temp_mail_address,\n            'temp_mail_pin': self.temp_mail_pin,\n            'temp_mail_domain': self.temp_mail_domain,\n            'imap_server': self.imap_server,\n            'imap_port': self.imap_port,\n            'smtp_server': self.smtp_server,\n            'smtp_port': self.smtp_port,\n            'username': self.username,\n            'password': self.password,\n            'use_ssl': self.use_ssl,\n            'timeout': self.timeout\n        }\n    \n    @classmethod\n    def from_dict(cls, data: Dict[str, Any]) -> 'EmailConfig':\n        \"\"\"从字典创建实例\"\"\"\n        return cls(\n            provider=data.get('provider', EmailProvider.TEMP_MAIL.value),\n            temp_mail_address=data.get('temp_mail_address', ''),\n            temp_mail_pin=data.get('temp_mail_pin', ''),\n            temp_mail_domain=data.get('temp_mail_domain', ''),\n            imap_server=data.get('imap_server', ''),\n            imap_port=data.get('imap_port', 993),\n            smtp_server=data.get('smtp_server', ''),\n            smtp_port=data.get('smtp_port', 587),\n            username=data.get('username', ''),\n            password=data.get('password', ''),\n            use_ssl=data.get('use_ssl', True),\n            timeout=data.get('timeout', 30)\n        )\n    \n    def is_temp_mail(self) -> bool:\n        \"\"\"检查是否使用临时邮箱\"\"\"\n        return self.provider == EmailProvider.TEMP_MAIL.value\n\n\n@dataclass\nclass ServiceConfig:\n    \"\"\"服务配置模型\"\"\"\n    domain: str = \"\"\n    api_timeout: int = 30\n    retry_count: int = 3\n    retry_delay: int = 1\n    enable_logging: bool = True\n    log_level: str = \"INFO\"\n    custom_settings: Dict[str, Any] = field(default_factory=dict)\n    \n    def to_dict(self) -> Dict[str, Any]:\n        \"\"\"转换为字典\"\"\"\n        return {\n            'domain': self.domain,\n            'api_timeout': self.api_timeout,\n            'retry_count': self.retry_count,\n            'retry_delay': self.retry_delay,\n            'enable_logging': self.enable_logging,\n            'log_level': self.log_level,\n            'custom_settings': self.custom_settings\n        }\n    \n    @classmethod\n    def from_dict(cls, data: Dict[str, Any]) -> 'ServiceConfig':\n        \"\"\"从字典创建实例\"\"\"\n        return cls(\n            domain=data.get('domain', ''),\n            api_timeout=data.get('api_timeout', 30),\n            retry_count=data.get('retry_count', 3),\n            retry_delay=data.get('retry_delay', 1),\n            enable_logging=data.get('enable_logging', True),\n            log_level=data.get('log_level', 'INFO'),\n            custom_settings=data.get('custom_settings', {})\n        )\n\n\n@dataclass\nclass ConfigModel:\n    \"\"\"完整配置模型\"\"\"\n    name: str = \"默认配置\"\n    description: str = \"\"\n    browser_config: BrowserConfig = field(default_factory=BrowserConfig)\n    email_config: EmailConfig = field(default_factory=EmailConfig)\n    service_config: ServiceConfig = field(default_factory=ServiceConfig)\n    cursor_path: str = \"\"\n    windsurf_path: str = \"\"\n    is_active: bool = True\n    created_at: Optional[str] = None\n    updated_at: Optional[str] = None\n    \n    def to_dict(self) -> Dict[str, Any]:\n        \"\"\"转换为字典\"\"\"\n        return {\n            'name': self.name,\n            'description': self.description,\n            'browser_config': self.browser_config.to_dict(),\n            'email_config': self.email_config.to_dict(),\n            'service_config': self.service_config.to_dict(),\n            'cursor_path': self.cursor_path,\n            'windsurf_path': self.windsurf_path,\n            'is_active': self.is_active,\n            'created_at': self.created_at,\n            'updated_at': self.updated_at\n        }\n    \n    @classmethod\n    def from_dict(cls, data: Dict[str, Any]) -> 'ConfigModel':\n        \"\"\"从字典创建实例\"\"\"\n        browser_config = BrowserConfig.from_dict(data.get('browser_config', {}))\n        email_config = EmailConfig.from_dict(data.get('email_config', {}))\n        service_config = ServiceConfig.from_dict(data.get('service_config', {}))\n        \n        return cls(\n            name=data.get('name', '默认配置'),\n            description=data.get('description', ''),\n            browser_config=browser_config,\n            email_config=email_config,\n            service_config=service_config,\n            cursor_path=data.get('cursor_path', ''),\n            windsurf_path=data.get('windsurf_path', ''),\n            is_active=data.get('is_active', True),\n            created_at=data.get('created_at'),\n            updated_at=data.get('updated_at')\n        )\n    \n    def update_timestamp(self):\n        \"\"\"更新时间戳\"\"\"\n        from datetime import datetime\n        self.updated_at = datetime.now().isoformat()\n        if not self.created_at:\n            self.created_at = self.updated_at\n"}
{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/services/cursor/registration.py"}, "originalCode": "#!/usr/bin/env python3\r\n# -*- coding: utf-8 -*-\r\n\"\"\"\r\nCursor注册服务\r\n\r\n提供Cursor账号注册的业务逻辑。\r\n\"\"\"\r\n\r\nimport os\r\nimport sys\r\nimport time\r\nimport random\r\nimport re\r\nimport json\r\nimport uuid\r\nimport base64\r\nimport hashlib\r\nimport secrets\r\nfrom enum import Enum\r\nfrom typing import Optional, Tuple, Dict, Any\r\nfrom datetime import datetime\r\n\r\nfrom ...core import Config, info, error, warning, debug, get_database\r\nfrom ...models import CursorAccount, RegistrationResponse\r\nfrom ...utils import BrowserManager, CursorEmailHandler\r\nfrom ...core.exceptions import RegistrationError, BrowserError, EmailError\r\n\r\n\r\n# 常量定义\r\nSIGN_UP_URL = \"https://authenticator.cursor.sh/sign-up\"\r\nSETTINGS_URL = \"https://www.cursor.com/settings\"\r\nLOGIN_URL = \"https://authenticator.cursor.sh\"\r\nDEFAULT_USER_AGENT = \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"\r\n\r\n\r\nclass VerificationStatus(Enum):\r\n    \"\"\"验证状态枚举\"\"\"\r\n    PASSWORD_PAGE = \"@name=password\"\r\n    CAPTCHA_PAGE = \"@data-index=0\"\r\n    ACCOUNT_SETTINGS = \"Account Settings\"\r\n\r\n\r\nclass TurnstileError(Exception):\r\n    \"\"\"Turnstile 验证相关异常\"\"\"\r\n    pass\r\n\r\n\r\nclass CursorRegistrationService:\r\n    \"\"\"Cursor注册服务类\"\"\"\r\n\r\n    def __init__(self, config: Optional[Config] = None):\r\n        \"\"\"初始化注册服务\r\n\r\n        Args:\r\n            config: 配置实例，为None时使用默认配置\r\n        \"\"\"\r\n        self.config = config or Config()\r\n        self.database = get_database()\r\n        self.browser_manager: Optional[BrowserManager] = None\r\n        self.email_handler: Optional[CursorEmailHandler] = None\r\n\r\n    def quick_signup_process(self, config_index: int = 0) -> RegistrationResponse:\r\n        \"\"\"快速注册流程\r\n\r\n        Args:\r\n            config_index: 配置索引\r\n\r\n        Returns:\r\n            RegistrationResponse: 注册结果\r\n        \"\"\"\r\n        try:\r\n            info(\"开始Cursor快速注册流程\")\r\n\r\n            # 切换到指定配置\r\n            if not self.config.switch_config(config_index):\r\n                return RegistrationResponse.error_response(\"切换配置失败\")\r\n\r\n            # 初始化浏览器管理器\r\n            self.browser_manager = BrowserManager()\r\n\r\n            # 获取真实User-Agent\r\n            user_agent = self._get_real_user_agent()\r\n\r\n            # 初始化浏览器\r\n            browser = self.browser_manager.init_browser(user_agent, self.config.browser_headless)\r\n\r\n            # 初始化邮箱处理器\r\n            self.email_handler = CursorEmailHandler(self.config)\r\n\r\n            # 生成账号信息\r\n            email = self.email_handler.generate_email()\r\n            password = self.email_handler.generate_password()\r\n            first_name = self._generate_random_name()\r\n            last_name = self._generate_random_name()\r\n\r\n            info(f\"生成的邮箱: {email}\")\r\n            info(f\"生成的姓名: {first_name} {last_name}\")\r\n\r\n            # 执行注册流程\r\n            success, account_data = self._perform_registration(\r\n                browser, email, password, first_name, last_name\r\n            )\r\n\r\n            if success:\r\n                # 保存账号到数据库\r\n                cursor_account = CursorAccount(\r\n                    email=email,\r\n                    password=password,\r\n                    token=account_data.get(\"token\", \"\"),\r\n                    user_id=account_data.get(\"user_id\", \"\"),\r\n                    token_info=account_data.get(\"token_info\", {}),\r\n                    status=\"active\"\r\n                )\r\n\r\n                self.database.add_cursor_account(cursor_account.to_dict())\r\n\r\n                return RegistrationResponse.success_response(\r\n                    email=email,\r\n                    token=account_data.get(\"token\", \"\"),\r\n                    user_id=account_data.get(\"user_id\", \"\"),\r\n                    account_data=account_data\r\n                )\r\n            else:\r\n                error_msg = account_data.get(\"error\", \"注册失败\")\r\n                return RegistrationResponse.error_response(error_msg)\r\n\r\n        except Exception as e:\r\n            error(f\"注册过程发生错误: {e}\")\r\n            return RegistrationResponse.error_response(f\"注册过程发生错误: {e}\")\r\n        finally:\r\n            # 清理资源\r\n            self._cleanup()\r\n\r\n    def _get_real_user_agent(self) -> str:\r\n        \"\"\"获取真实User-Agent，剔除Headless标记\r\n\r\n        Returns:\r\n            str: 处理后的User-Agent字符串\r\n        \"\"\"\r\n        try:\r\n            info(\"正在获取真实User-Agent...\")\r\n            temp_manager = BrowserManager()\r\n            try:\r\n                temp_browser = temp_manager.init_browser(headless_value=self.config.browser_headless)\r\n                user_agent = temp_browser.latest_tab.run_js(\"return navigator.userAgent\")\r\n                info(\"成功获取User-Agent\")\r\n            except Exception as e:\r\n                error(f\"获取User-Agent时出错: {e}\")\r\n                user_agent = DEFAULT_USER_AGENT\r\n            finally:\r\n                try:\r\n                    time.sleep(0.5)\r\n                    temp_manager.quit()\r\n                    info(\"临时浏览器资源已释放\")\r\n                except Exception as e:\r\n                    error(f\"释放临时浏览器资源时出错: {e}\")\r\n\r\n            if not user_agent:\r\n                user_agent = DEFAULT_USER_AGENT\r\n            return user_agent.replace(\"HeadlessChrome\", \"Chrome\")\r\n        except Exception as e:\r\n            error(f\"获取user agent失败: {e}\")\r\n            return DEFAULT_USER_AGENT\r\n\r\n    def _generate_random_name(self, length: int = 6) -> str:\r\n        \"\"\"生成随机用户名\r\n\r\n        Args:\r\n            length: 用户名长度\r\n\r\n        Returns:\r\n            str: 随机生成的用户名\r\n        \"\"\"\r\n        first_letter = random.choice(\"ABCDEFGHIJKLMNOPQRSTUVWXYZ\")\r\n        rest_letters = \"\".join(\r\n            random.choices(\"abcdefghijklmnopqrstuvwxyz\", k=length - 1)\r\n        )\r\n        return first_letter + rest_letters\r\n\r\n    def _perform_registration(self, browser, email: str, password: str,\r\n                            first_name: str, last_name: str) -> Tuple[bool, Dict[str, Any]]:\r\n        \"\"\"执行注册流程\r\n\r\n        Args:\r\n            browser: 浏览器实例\r\n            email: 邮箱地址\r\n            password: 密码\r\n            first_name: 名字\r\n            last_name: 姓氏\r\n\r\n        Returns:\r\n            Tuple[bool, Dict[str, Any]]: (是否成功, 账号数据)\r\n        \"\"\"\r\n        try:\r\n            # 初始化浏览器标签页\r\n            tab = browser.new_tab()\r\n\r\n            # 开始注册流程\r\n            info(\"\\n=== 开始注册流程 ===\")\r\n            info(f\"正在访问登录页面: {LOGIN_URL}\")\r\n            tab.get(LOGIN_URL)\r\n\r\n            # 执行注册\r\n            signup_result, result_info = self._sign_up_account(\r\n                browser, tab, email, password, first_name, last_name\r\n            )\r\n\r\n            if signup_result:\r\n                # 获取长效token\r\n                info(\"\\n=== 开始获取长效Token ===\")\r\n                token_info = self._get_long_term_token(browser=browser, tab=tab)\r\n\r\n                if token_info and token_info.get(\"token\"):\r\n                    # 构建完整的账号信息\r\n                    account_info = {\r\n                        \"email\": email,\r\n                        \"password\": password,\r\n                        \"first_name\": first_name,\r\n                        \"last_name\": last_name,\r\n                        \"token\": token_info[\"token\"],\r\n                        \"user_id\": token_info.get(\"user_id\", \"\"),\r\n                        \"token_info\": token_info,\r\n                        \"status\": \"Account status normal\",\r\n                        \"timestamp\": datetime.now().strftime('%Y-%m-%d %H:%M:%S')\r\n                    }\r\n\r\n                    info(\"Cursor账号注册成功\")\r\n                    return True, account_info\r\n                else:\r\n                    error(\"获取Token失败\")\r\n                    return False, {\"error\": \"获取Token失败\"}\r\n            else:\r\n                error(f\"注册失败: {result_info}\")\r\n                return False, {\"error\": result_info or \"注册失败\"}\r\n\r\n        except Exception as e:\r\n            error(f\"注册流程执行失败: {e}\")\r\n            return False, {\"error\": str(e)}\r\n\r\n    def _sign_up_account(self, browser, tab, account: str, password: str,\r\n                        first_name: str, last_name: str) -> Tuple[bool, Optional[str]]:\r\n        \"\"\"注册Cursor账号\r\n\r\n        Args:\r\n            browser: 浏览器对象\r\n            tab: 浏览器标签页对象\r\n            account: 邮箱账号\r\n            password: 密码\r\n            first_name: 名字\r\n            last_name: 姓氏\r\n\r\n        Returns:\r\n            Tuple[bool, Optional[str]]: (是否成功, 错误信息或None)\r\n        \"\"\"\r\n        info(\"=== 开始注册账号流程 ===\")\r\n        info(f\"正在访问注册页面: {SIGN_UP_URL}\")\r\n        tab.get(SIGN_UP_URL)\r\n        time.sleep(2)\r\n\r\n        # 首先检测是否直接进入个人信息填写页面\r\n        if tab.ele(\"@name=first_name\", timeout=2):\r\n            info(\"检测到个人信息填写页面，开始填写...\")\r\n            try:\r\n                tab.actions.click(\"@name=first_name\").input(first_name)\r\n                info(f\"已输入名字: {first_name}\")\r\n                time.sleep(random.uniform(1, 2))\r\n\r\n                tab.actions.click(\"@name=last_name\").input(last_name)\r\n                info(f\"已输入姓氏: {last_name}\")\r\n                time.sleep(random.uniform(1, 2))\r\n\r\n                tab.actions.click(\"@name=email\").input(account)\r\n                info(f\"已输入邮箱: {account}\")\r\n                time.sleep(random.uniform(1, 2))\r\n\r\n                # 点击继续按钮\r\n                continue_button = tab.ele(\"@type=submit\")\r\n                if continue_button:\r\n                    continue_button.click()\r\n                    info(\"已点击继续按钮\")\r\n                    time.sleep(3)\r\n                else:\r\n                    error(\"未找到继续按钮\")\r\n                    return False, \"未找到继续按钮\"\r\n\r\n            except Exception as e:\r\n                error(f\"填写个人信息时出错: {e}\")\r\n                return False, f\"填写个人信息时出错: {e}\"\r\n\r\n        # 处理Turnstile验证\r\n        try:\r\n            if not self._handle_turnstile(tab, is_signup_page=True):\r\n                error(\"Turnstile验证失败\")\r\n                return False, \"Turnstile验证失败\"\r\n        except TurnstileError as e:\r\n            error(f\"Turnstile验证异常: {e}\")\r\n            return False, f\"Turnstile验证异常: {e}\"\r\n\r\n        # 检查是否到达密码设置页面\r\n        if tab.ele(\"@name=password\", timeout=10):\r\n            info(\"已到达密码设置页面\")\r\n            try:\r\n                # 输入密码\r\n                tab.actions.click(\"@name=password\").input(password)\r\n                info(\"已输入密码\")\r\n                time.sleep(random.uniform(1, 2))\r\n\r\n                # 点击创建账号按钮\r\n                create_button = tab.ele(\"@type=submit\")\r\n                if create_button:\r\n                    create_button.click()\r\n                    info(\"已点击创建账号按钮\")\r\n                    time.sleep(3)\r\n                else:\r\n                    error(\"未找到创建账号按钮\")\r\n                    return False, \"未找到创建账号按钮\"\r\n\r\n            except Exception as e:\r\n                error(f\"设置密码时出错: {e}\")\r\n                return False, f\"设置密码时出错: {e}\"\r\n        else:\r\n            error(\"未到达密码设置页面\")\r\n            return False, \"未到达密码设置页面\"\r\n\r\n        # 处理邮箱验证\r\n        info(\"=== 开始邮箱验证流程 ===\")\r\n        verification_success = self._handle_email_verification(tab, account)\r\n\r\n        if not verification_success:\r\n            error(\"邮箱验证失败\")\r\n            return False, \"邮箱验证失败\"\r\n\r\n        info(\"账号注册成功\")\r\n        return True, None", "modifiedCode": "#!/usr/bin/env python3\r\n# -*- coding: utf-8 -*-\r\n\"\"\"\r\nCursor注册服务\r\n\r\n提供Cursor账号注册的业务逻辑。\r\n\"\"\"\r\n\r\nimport os\r\nimport sys\r\nimport time\r\nimport random\r\nimport re\r\nimport json\r\nimport uuid\r\nimport base64\r\nimport hashlib\r\nimport secrets\r\nfrom enum import Enum\r\nfrom typing import Optional, Tuple, Dict, Any\r\nfrom datetime import datetime\r\n\r\nfrom ...core import Config, info, error, warning, debug, get_database\r\nfrom ...models import CursorAccount, RegistrationResponse\r\nfrom ...utils import BrowserManager, CursorEmailHandler\r\nfrom ...core.exceptions import RegistrationError, BrowserError, EmailError\r\n\r\n\r\n# 常量定义\r\nSIGN_UP_URL = \"https://authenticator.cursor.sh/sign-up\"\r\nSETTINGS_URL = \"https://www.cursor.com/settings\"\r\nLOGIN_URL = \"https://authenticator.cursor.sh\"\r\nDEFAULT_USER_AGENT = \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"\r\n\r\n\r\nclass VerificationStatus(Enum):\r\n    \"\"\"验证状态枚举\"\"\"\r\n    PASSWORD_PAGE = \"@name=password\"\r\n    CAPTCHA_PAGE = \"@data-index=0\"\r\n    ACCOUNT_SETTINGS = \"Account Settings\"\r\n\r\n\r\nclass TurnstileError(Exception):\r\n    \"\"\"Turnstile 验证相关异常\"\"\"\r\n    pass\r\n\r\n\r\nclass CursorRegistrationService:\r\n    \"\"\"Cursor注册服务类\"\"\"\r\n\r\n    def __init__(self, config: Optional[Config] = None):\r\n        \"\"\"初始化注册服务\r\n\r\n        Args:\r\n            config: 配置实例，为None时使用默认配置\r\n        \"\"\"\r\n        self.config = config or Config()\r\n        self.database = get_database()\r\n        self.browser_manager: Optional[BrowserManager] = None\r\n        self.email_handler: Optional[CursorEmailHandler] = None\r\n\r\n    def quick_signup_process(self, config_index: int = 0) -> RegistrationResponse:\r\n        \"\"\"快速注册流程\r\n\r\n        Args:\r\n            config_index: 配置索引\r\n\r\n        Returns:\r\n            RegistrationResponse: 注册结果\r\n        \"\"\"\r\n        try:\r\n            info(\"开始Cursor快速注册流程\")\r\n\r\n            # 切换到指定配置\r\n            if not self.config.switch_config(config_index):\r\n                return RegistrationResponse.error_response(\"切换配置失败\")\r\n\r\n            # 初始化浏览器管理器\r\n            self.browser_manager = BrowserManager()\r\n\r\n            # 获取真实User-Agent\r\n            user_agent = self._get_real_user_agent()\r\n\r\n            # 初始化浏览器\r\n            browser = self.browser_manager.init_browser(user_agent, self.config.browser_headless)\r\n\r\n            # 初始化邮箱处理器\r\n            self.email_handler = CursorEmailHandler(self.config)\r\n\r\n            # 生成账号信息\r\n            email = self.email_handler.generate_email()\r\n            password = self.email_handler.generate_password()\r\n            first_name = self._generate_random_name()\r\n            last_name = self._generate_random_name()\r\n\r\n            info(f\"生成的邮箱: {email}\")\r\n            info(f\"生成的姓名: {first_name} {last_name}\")\r\n\r\n            # 执行注册流程\r\n            success, account_data = self._perform_registration(\r\n                browser, email, password, first_name, last_name\r\n            )\r\n\r\n            if success:\r\n                # 保存账号到数据库\r\n                cursor_account = CursorAccount(\r\n                    email=email,\r\n                    password=password,\r\n                    token=account_data.get(\"token\", \"\"),\r\n                    user_id=account_data.get(\"user_id\", \"\"),\r\n                    token_info=account_data.get(\"token_info\", {}),\r\n                    status=\"active\"\r\n                )\r\n\r\n                self.database.add_cursor_account(cursor_account.to_dict())\r\n\r\n                return RegistrationResponse.success_response(\r\n                    email=email,\r\n                    token=account_data.get(\"token\", \"\"),\r\n                    user_id=account_data.get(\"user_id\", \"\"),\r\n                    account_data=account_data\r\n                )\r\n            else:\r\n                error_msg = account_data.get(\"error\", \"注册失败\")\r\n                return RegistrationResponse.error_response(error_msg)\r\n\r\n        except Exception as e:\r\n            error(f\"注册过程发生错误: {e}\")\r\n            return RegistrationResponse.error_response(f\"注册过程发生错误: {e}\")\r\n        finally:\r\n            # 清理资源\r\n            self._cleanup()\r\n\r\n    def _get_real_user_agent(self) -> str:\r\n        \"\"\"获取真实User-Agent，剔除Headless标记\r\n\r\n        Returns:\r\n            str: 处理后的User-Agent字符串\r\n        \"\"\"\r\n        try:\r\n            info(\"正在获取真实User-Agent...\")\r\n            temp_manager = BrowserManager()\r\n            try:\r\n                temp_browser = temp_manager.init_browser(headless_value=self.config.browser_headless)\r\n                user_agent = temp_browser.latest_tab.run_js(\"return navigator.userAgent\")\r\n                info(\"成功获取User-Agent\")\r\n            except Exception as e:\r\n                error(f\"获取User-Agent时出错: {e}\")\r\n                user_agent = DEFAULT_USER_AGENT\r\n            finally:\r\n                try:\r\n                    time.sleep(0.5)\r\n                    temp_manager.quit()\r\n                    info(\"临时浏览器资源已释放\")\r\n                except Exception as e:\r\n                    error(f\"释放临时浏览器资源时出错: {e}\")\r\n\r\n            if not user_agent:\r\n                user_agent = DEFAULT_USER_AGENT\r\n            return user_agent.replace(\"HeadlessChrome\", \"Chrome\")\r\n        except Exception as e:\r\n            error(f\"获取user agent失败: {e}\")\r\n            return DEFAULT_USER_AGENT\r\n\r\n    def _generate_random_name(self, length: int = 6) -> str:\r\n        \"\"\"生成随机用户名\r\n\r\n        Args:\r\n            length: 用户名长度\r\n\r\n        Returns:\r\n            str: 随机生成的用户名\r\n        \"\"\"\r\n        first_letter = random.choice(\"ABCDEFGHIJKLMNOPQRSTUVWXYZ\")\r\n        rest_letters = \"\".join(\r\n            random.choices(\"abcdefghijklmnopqrstuvwxyz\", k=length - 1)\r\n        )\r\n        return first_letter + rest_letters\r\n\r\n    def _perform_registration(self, browser, email: str, password: str,\r\n                            first_name: str, last_name: str) -> Tuple[bool, Dict[str, Any]]:\r\n        \"\"\"执行注册流程\r\n\r\n        Args:\r\n            browser: 浏览器实例\r\n            email: 邮箱地址\r\n            password: 密码\r\n            first_name: 名字\r\n            last_name: 姓氏\r\n\r\n        Returns:\r\n            Tuple[bool, Dict[str, Any]]: (是否成功, 账号数据)\r\n        \"\"\"\r\n        try:\r\n            # 初始化浏览器标签页\r\n            tab = browser.new_tab()\r\n\r\n            # 开始注册流程\r\n            info(\"\\n=== 开始注册流程 ===\")\r\n            info(f\"正在访问登录页面: {LOGIN_URL}\")\r\n            tab.get(LOGIN_URL)\r\n\r\n            # 执行注册\r\n            signup_result, result_info = self._sign_up_account(\r\n                browser, tab, email, password, first_name, last_name\r\n            )\r\n\r\n            if signup_result:\r\n                # 获取长效token\r\n                info(\"\\n=== 开始获取长效Token ===\")\r\n                token_info = self._get_long_term_token(browser=browser, tab=tab)\r\n\r\n                if token_info and token_info.get(\"token\"):\r\n                    # 构建完整的账号信息\r\n                    account_info = {\r\n                        \"email\": email,\r\n                        \"password\": password,\r\n                        \"first_name\": first_name,\r\n                        \"last_name\": last_name,\r\n                        \"token\": token_info[\"token\"],\r\n                        \"user_id\": token_info.get(\"user_id\", \"\"),\r\n                        \"token_info\": token_info,\r\n                        \"status\": \"Account status normal\",\r\n                        \"timestamp\": datetime.now().strftime('%Y-%m-%d %H:%M:%S')\r\n                    }\r\n\r\n                    info(\"Cursor账号注册成功\")\r\n                    return True, account_info\r\n                else:\r\n                    error(\"获取Token失败\")\r\n                    return False, {\"error\": \"获取Token失败\"}\r\n            else:\r\n                error(f\"注册失败: {result_info}\")\r\n                return False, {\"error\": result_info or \"注册失败\"}\r\n\r\n        except Exception as e:\r\n            error(f\"注册流程执行失败: {e}\")\r\n            return False, {\"error\": str(e)}\r\n\r\n    def _sign_up_account(self, browser, tab, account: str, password: str,\r\n                        first_name: str, last_name: str) -> Tuple[bool, Optional[str]]:\r\n        \"\"\"注册Cursor账号\r\n\r\n        Args:\r\n            browser: 浏览器对象\r\n            tab: 浏览器标签页对象\r\n            account: 邮箱账号\r\n            password: 密码\r\n            first_name: 名字\r\n            last_name: 姓氏\r\n\r\n        Returns:\r\n            Tuple[bool, Optional[str]]: (是否成功, 错误信息或None)\r\n        \"\"\"\r\n        info(\"=== 开始注册账号流程 ===\")\r\n        info(f\"正在访问注册页面: {SIGN_UP_URL}\")\r\n        tab.get(SIGN_UP_URL)\r\n        time.sleep(2)\r\n\r\n        # 首先检测是否直接进入个人信息填写页面\r\n        if tab.ele(\"@name=first_name\", timeout=2):\r\n            info(\"检测到个人信息填写页面，开始填写...\")\r\n            try:\r\n                tab.actions.click(\"@name=first_name\").input(first_name)\r\n                info(f\"已输入名字: {first_name}\")\r\n                time.sleep(random.uniform(1, 2))\r\n\r\n                tab.actions.click(\"@name=last_name\").input(last_name)\r\n                info(f\"已输入姓氏: {last_name}\")\r\n                time.sleep(random.uniform(1, 2))\r\n\r\n                tab.actions.click(\"@name=email\").input(account)\r\n                info(f\"已输入邮箱: {account}\")\r\n                time.sleep(random.uniform(1, 2))\r\n\r\n                # 点击继续按钮\r\n                continue_button = tab.ele(\"@type=submit\")\r\n                if continue_button:\r\n                    continue_button.click()\r\n                    info(\"已点击继续按钮\")\r\n                    time.sleep(3)\r\n                else:\r\n                    error(\"未找到继续按钮\")\r\n                    return False, \"未找到继续按钮\"\r\n\r\n            except Exception as e:\r\n                error(f\"填写个人信息时出错: {e}\")\r\n                return False, f\"填写个人信息时出错: {e}\"\r\n\r\n        # 处理Turnstile验证\r\n        try:\r\n            if not self._handle_turnstile(tab, is_signup_page=True):\r\n                error(\"Turnstile验证失败\")\r\n                return False, \"Turnstile验证失败\"\r\n        except TurnstileError as e:\r\n            error(f\"Turnstile验证异常: {e}\")\r\n            return False, f\"Turnstile验证异常: {e}\"\r\n\r\n        # 检查是否到达密码设置页面\r\n        if tab.ele(\"@name=password\", timeout=10):\r\n            info(\"已到达密码设置页面\")\r\n            try:\r\n                # 输入密码\r\n                tab.actions.click(\"@name=password\").input(password)\r\n                info(\"已输入密码\")\r\n                time.sleep(random.uniform(1, 2))\r\n\r\n                # 点击创建账号按钮\r\n                create_button = tab.ele(\"@type=submit\")\r\n                if create_button:\r\n                    create_button.click()\r\n                    info(\"已点击创建账号按钮\")\r\n                    time.sleep(3)\r\n                else:\r\n                    error(\"未找到创建账号按钮\")\r\n                    return False, \"未找到创建账号按钮\"\r\n\r\n            except Exception as e:\r\n                error(f\"设置密码时出错: {e}\")\r\n                return False, f\"设置密码时出错: {e}\"\r\n        else:\r\n            error(\"未到达密码设置页面\")\r\n            return False, \"未到达密码设置页面\"\r\n\r\n        # 处理邮箱验证\r\n        info(\"=== 开始邮箱验证流程 ===\")\r\n        verification_success = self._handle_email_verification(tab, account)\r\n\r\n        if not verification_success:\r\n            error(\"邮箱验证失败\")\r\n            return False, \"邮箱验证失败\"\r\n\r\n        info(\"账号注册成功\")\r\n        return True, None\r\n\r\n    def _handle_turnstile(self, tab, max_retries: int = 2, retry_interval: tuple = (1, 2),\r\n                         is_signup_page: bool = False) -> bool:\r\n        \"\"\"处理 Turnstile 人机验证\r\n\r\n        Args:\r\n            tab: 浏览器标签页对象\r\n            max_retries: 最大重试次数\r\n            retry_interval: 重试间隔时间范围(秒)\r\n            is_signup_page: 是否为注册页面专用处理方式\r\n\r\n        Returns:\r\n            bool: 验证是否成功\r\n\r\n        Raises:\r\n            TurnstileError: 验证过程发生异常\r\n        \"\"\"\r\n        info(\"正在检测 Turnstile 验证...\")\r\n        retry_count = 0\r\n\r\n        try:\r\n            while retry_count < max_retries:\r\n                retry_count += 1\r\n                debug(f\"第 {retry_count} 次尝试验证\")\r\n\r\n                try:\r\n                    # 注册页面专用Turnstile点击逻辑\r\n                    if is_signup_page:\r\n                        info(\"[注册页面专用] 尝试点击Turnstile验证按钮...\")\r\n                        try:\r\n                            challenge_solution = tab.ele(\"@name=cf-turnstile-response\", timeout=2)\r\n                            if challenge_solution:\r\n                                challenge_wrapper = challenge_solution.parent()\r\n                                challenge_iframe = challenge_wrapper.shadow_root.ele(\"tag:iframe\")\r\n                                challenge_iframe_body = challenge_iframe.ele(\"tag:body\").shadow_root\r\n                                challenge_button = challenge_iframe_body.ele(\"tag:input\")\r\n                                if challenge_button:\r\n                                    info(\"[注册页面专用] 成功定位并点击Turnstile按钮！\")\r\n                                    time.sleep(random.uniform(1, 2))\r\n                                    challenge_button.click()\r\n                                    time.sleep(2)\r\n\r\n                                    # 重新跳转到注册页面\r\n                                    info(\"[注册页面专用] Turnstile验证后重新跳转到注册页面\")\r\n                                    tab.get(SIGN_UP_URL)\r\n                                    time.sleep(2)\r\n\r\n                                    # 检查是否成功跳转到注册页面\r\n                                    if tab.ele(\"@name=first_name\", timeout=3) or tab.ele(\"@id=cf-turnstile\", timeout=1):\r\n                                        info(\"[注册页面专用] 已成功跳转到注册页面\")\r\n                                        return True\r\n                                    else:\r\n                                        info(\"[注册页面专用] 跳转到注册页面后未检测到预期元素，继续尝试\")\r\n                        except Exception as e:\r\n                            debug(f\"[注册页面专用] cf-turnstile-response链路点击失败: {e}\")\r\n\r\n                    # 通用Turnstile验证处理逻辑\r\n                    challenge_check = (\r\n                        tab.ele(\"@id=cf-turnstile\", timeout=2)\r\n                        .child()\r\n                        .shadow_root.ele(\"tag:iframe\")\r\n                        .ele(\"tag:body\")\r\n                        .sr(\"tag:input\")\r\n                    )\r\n                    if challenge_check:\r\n                        info(\"检测到 Turnstile 验证框，开始处理...\")\r\n                        time.sleep(random.uniform(1, 3))\r\n                        challenge_check.click()\r\n                        time.sleep(2)\r\n                        verification_status = self._check_verification_success(tab)\r\n                        if verification_status:\r\n                            info(f\"Turnstile 验证通过 - 已到达{verification_status.name}页面\")\r\n                            return True\r\n\r\n                except Exception as e:\r\n                    debug(f\"当前尝试未成功: {e}\")\r\n\r\n                # 检查是否已经验证成功\r\n                verification_status = self._check_verification_success(tab)\r\n                if verification_status:\r\n                    info(f\"验证成功 - 已到达{verification_status.name}页面\")\r\n                    return True\r\n\r\n                # 随机延时后继续下一次尝试\r\n                time.sleep(random.uniform(*retry_interval))\r\n\r\n            # 超出最大重试次数\r\n            error(f\"验证失败 - 已达到最大重试次数 {max_retries}\")\r\n            return False\r\n\r\n        except Exception as e:\r\n            error_msg = f\"Turnstile 验证过程发生异常: {e}\"\r\n            error(error_msg)\r\n            raise TurnstileError(error_msg)\r\n\r\n    def _check_verification_success(self, tab) -> Optional[VerificationStatus]:\r\n        \"\"\"检查验证是否成功\r\n\r\n        Args:\r\n            tab: 浏览器标签页对象\r\n\r\n        Returns:\r\n            Optional[VerificationStatus]: 验证状态枚举值，验证失败返回None\r\n        \"\"\"\r\n        try:\r\n            # 检查是否存在后续表单元素，这表示验证已通过\r\n            if tab.ele(\"@name=password\", timeout=0.5):\r\n                return VerificationStatus.PASSWORD_PAGE\r\n            elif tab.ele(\"@data-index=0\", timeout=0.5):\r\n                return VerificationStatus.CAPTCHA_PAGE\r\n            elif tab.ele(\"Account Settings\", timeout=0.5):\r\n                return VerificationStatus.ACCOUNT_SETTINGS\r\n\r\n            # 检查是否出现错误消息\r\n            error_messages = [\r\n                'xpath://div[contains(text(), \"Can\\'t verify the user is human\")]',\r\n                'xpath://div[contains(text(), \"Error: 600010\")]',\r\n                'xpath://div[contains(text(), \"Please try again\")]',\r\n                'xpath://div[contains(text(), \"Sign up is restricted\")]'\r\n            ]\r\n\r\n            for error_xpath in error_messages:\r\n                if tab.ele(error_xpath):\r\n                    warning(f\"检测到验证错误: {error_xpath}\")\r\n                    return None\r\n\r\n            return None\r\n        except Exception as e:\r\n            error(f\"验证状态检查失败: {e}\")\r\n            return None"}
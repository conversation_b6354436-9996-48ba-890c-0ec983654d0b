{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/core/logger.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n日志系统模块\n\n提供统一的日志管理功能，支持文件输出和回调函数。\n\"\"\"\n\nimport os\nimport sys\nimport logging\nimport logging.handlers\nfrom pathlib import Path\nfrom typing import Optional, Callable, Any\nfrom datetime import datetime\n\nfrom .constants import LOG_ROTATION, LOG_INTERVAL, LOG_BACKUP_COUNT\n\n\nclass ColoredFormatter(logging.Formatter):\n    \"\"\"彩色日志格式化器\"\"\"\n    \n    # 颜色代码\n    COLORS = {\n        'DEBUG': '\\033[36m',    # 青色\n        'INFO': '\\033[32m',     # 绿色\n        'WARNING': '\\033[33m',  # 黄色\n        'ERROR': '\\033[31m',    # 红色\n        'CRITICAL': '\\033[35m', # 紫色\n        'RESET': '\\033[0m'      # 重置\n    }\n    \n    def format(self, record):\n        \"\"\"格式化日志记录\"\"\"\n        # 添加颜色\n        if record.levelname in self.COLORS:\n            record.levelname = f\"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}\"\n        \n        return super().format(record)\n\n\nclass CallbackHandler(logging.Handler):\n    \"\"\"回调处理器，将日志发送到回调函数\"\"\"\n    \n    def __init__(self, callback: Callable[[str], None]):\n        \"\"\"初始化回调处理器\n        \n        Args:\n            callback: 日志回调函数\n        \"\"\"\n        super().__init__()\n        self.callback = callback\n    \n    def emit(self, record):\n        \"\"\"发送日志记录\"\"\"\n        try:\n            msg = self.format(record)\n            self.callback(msg)\n        except Exception:\n            self.handleError(record)\n\n\nclass Logger:\n    \"\"\"日志管理器\"\"\"\n    \n    def __init__(self, name: str = \"app\"):\n        \"\"\"初始化日志管理器\n        \n        Args:\n            name: 日志器名称\n        \"\"\"\n        self.logger = logging.getLogger(name)\n        self.logger.setLevel(logging.DEBUG)\n        self.callback_handler: Optional[CallbackHandler] = None\n        self._setup_default_handlers()\n    \n    def _setup_default_handlers(self):\n        \"\"\"设置默认处理器\"\"\"\n        # 清除现有处理器\n        self.logger.handlers.clear()\n        \n        # 控制台处理器\n        console_handler = logging.StreamHandler(sys.stdout)\n        console_handler.setLevel(logging.INFO)\n        console_formatter = ColoredFormatter(\n            '%(asctime)s - %(levelname)s - %(message)s',\n            datefmt='%H:%M:%S'\n        )\n        console_handler.setFormatter(console_formatter)\n        self.logger.addHandler(console_handler)\n    \n    def setup_file_handler(self, log_file: Path):\n        \"\"\"设置文件处理器\n        \n        Args:\n            log_file: 日志文件路径\n        \"\"\"\n        try:\n            # 确保日志目录存在\n            log_file.parent.mkdir(parents=True, exist_ok=True)\n            \n            # 文件处理器（带轮换）\n            file_handler = logging.handlers.TimedRotatingFileHandler(\n                filename=str(log_file),\n                when=LOG_ROTATION,\n                interval=LOG_INTERVAL,\n                backupCount=LOG_BACKUP_COUNT,\n                encoding='utf-8'\n            )\n            file_handler.setLevel(logging.DEBUG)\n            file_formatter = logging.Formatter(\n                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n                datefmt='%Y-%m-%d %H:%M:%S'\n            )\n            file_handler.setFormatter(file_formatter)\n            self.logger.addHandler(file_handler)\n            \n        except Exception as e:\n            self.logger.error(f\"设置文件日志处理器失败: {e}\")\n    \n    def setup_callback_handler(self, callback: Callable[[str], None]):\n        \"\"\"设置回调处理器\n        \n        Args:\n            callback: 日志回调函数\n        \"\"\"\n        try:\n            # 移除旧的回调处理器\n            if self.callback_handler:\n                self.logger.removeHandler(self.callback_handler)\n            \n            # 添加新的回调处理器\n            self.callback_handler = CallbackHandler(callback)\n            self.callback_handler.setLevel(logging.INFO)\n            callback_formatter = logging.Formatter('%(message)s')\n            self.callback_handler.setFormatter(callback_formatter)\n            self.logger.addHandler(self.callback_handler)\n            \n        except Exception as e:\n            self.logger.error(f\"设置回调日志处理器失败: {e}\")\n    \n    def debug(self, message: str, *args, **kwargs):\n        \"\"\"调试日志\"\"\"\n        self.logger.debug(message, *args, **kwargs)\n    \n    def info(self, message: str, *args, **kwargs):\n        \"\"\"信息日志\"\"\"\n        self.logger.info(message, *args, **kwargs)\n    \n    def warning(self, message: str, *args, **kwargs):\n        \"\"\"警告日志\"\"\"\n        self.logger.warning(message, *args, **kwargs)\n    \n    def error(self, message: str, *args, **kwargs):\n        \"\"\"错误日志\"\"\"\n        self.logger.error(message, *args, **kwargs)\n    \n    def critical(self, message: str, *args, **kwargs):\n        \"\"\"严重错误日志\"\"\"\n        self.logger.critical(message, *args, **kwargs)\n\n\n# 全局日志实例\n_logger_instance: Optional[Logger] = None\n\n\ndef setup_logger(name: str = \"app\", log_file: Optional[Path] = None, \n                callback: Optional[Callable[[str], None]] = None) -> Logger:\n    \"\"\"设置全局日志器\n    \n    Args:\n        name: 日志器名称\n        log_file: 日志文件路径\n        callback: 日志回调函数\n    \n    Returns:\n        日志器实例\n    \"\"\"\n    global _logger_instance\n    \n    _logger_instance = Logger(name)\n    \n    if log_file:\n        _logger_instance.setup_file_handler(log_file)\n    \n    if callback:\n        _logger_instance.setup_callback_handler(callback)\n    \n    return _logger_instance\n\n\ndef get_logger() -> Logger:\n    \"\"\"获取全局日志器\n    \n    Returns:\n        日志器实例\n    \"\"\"\n    global _logger_instance\n    \n    if _logger_instance is None:\n        _logger_instance = Logger()\n    \n    return _logger_instance\n\n\n# 便捷函数\ndef debug(message: str, *args, **kwargs):\n    \"\"\"调试日志\"\"\"\n    get_logger().debug(message, *args, **kwargs)\n\n\ndef info(message: str, *args, **kwargs):\n    \"\"\"信息日志\"\"\"\n    get_logger().info(message, *args, **kwargs)\n\n\ndef warning(message: str, *args, **kwargs):\n    \"\"\"警告日志\"\"\"\n    get_logger().warning(message, *args, **kwargs)\n\n\ndef error(message: str, *args, **kwargs):\n    \"\"\"错误日志\"\"\"\n    get_logger().error(message, *args, **kwargs)\n\n\ndef critical(message: str, *args, **kwargs):\n    \"\"\"严重错误日志\"\"\"\n    get_logger().critical(message, *args, **kwargs)\n"}
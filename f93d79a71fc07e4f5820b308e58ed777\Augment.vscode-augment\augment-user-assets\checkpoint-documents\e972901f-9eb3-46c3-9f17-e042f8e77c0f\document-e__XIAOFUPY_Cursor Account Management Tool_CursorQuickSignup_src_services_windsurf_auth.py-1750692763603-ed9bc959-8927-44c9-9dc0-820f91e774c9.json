{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/services/windsurf/auth.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nWindsurf认证服务\n\n提供Windsurf账号认证相关功能。\n\"\"\"\n\nimport os\nimport sys\nimport json\nimport subprocess\nfrom pathlib import Path\nfrom typing import Optional, Dict, Any\n\nfrom ...core import info, error, warning, debug\nfrom ...models import AuthResponse\nfrom ...core.exceptions import AuthenticationError, FileOperationError\n\n\nclass WindsurfAuthService:\n    \"\"\"Windsurf认证服务类\"\"\"\n    \n    def __init__(self):\n        \"\"\"初始化认证服务\"\"\"\n        self.system = sys.platform\n    \n    def get_current_account(self) -> Optional[Dict[str, str]]:\n        \"\"\"获取当前登录的账号信息\n        \n        Returns:\n            Optional[Dict[str, str]]: 当前账号信息，未登录时返回None\n        \"\"\"\n        try:\n            # 获取Windsurf配置目录\n            config_dir = self._get_windsurf_config_dir()\n            if not config_dir:\n                return None\n            \n            # 尝试从配置文件读取\n            auth_file = config_dir / \"auth.json\"\n            if auth_file.exists():\n                try:\n                    with open(auth_file, 'r', encoding='utf-8') as f:\n                        auth_data = json.load(f)\n                    \n                    email = auth_data.get(\"email\")\n                    token = auth_data.get(\"accessToken\") or auth_data.get(\"token\")\n                    \n                    if email and token:\n                        return {\"email\": email, \"token\": token}\n                        \n                except Exception as e:\n                    debug(f\"读取Windsurf auth.json失败: {e}\")\n            \n            return None\n            \n        except Exception as e:\n            error(f\"获取当前Windsurf账号信息失败: {e}\")\n            return None\n    \n    def switch_account(self, email: str, token: str) -> bool:\n        \"\"\"切换到指定账号\n        \n        Args:\n            email: 邮箱地址\n            token: 访问Token\n            \n        Returns:\n            bool: 切换是否成功\n        \"\"\"\n        try:\n            info(f\"正在切换到Windsurf账号: {email}\")\n            \n            # 先退出Windsurf\n            self._exit_windsurf()\n            \n            # 更新认证信息\n            success = self._update_auth_info(email, token)\n            \n            if success:\n                # 启动Windsurf\n                self._start_windsurf()\n                info(f\"成功切换到Windsurf账号: {email}\")\n                return True\n            else:\n                error(\"更新Windsurf认证信息失败\")\n                return False\n                \n        except Exception as e:\n            error(f\"切换Windsurf账号失败: {e}\")\n            return False\n    \n    def logout(self) -> bool:\n        \"\"\"退出当前账号\n        \n        Returns:\n            bool: 退出是否成功\n        \"\"\"\n        try:\n            info(\"正在退出当前Windsurf账号\")\n            \n            # 退出Windsurf\n            self._exit_windsurf()\n            \n            # 清除认证信息\n            success = self._clear_auth_info()\n            \n            if success:\n                info(\"成功退出Windsurf账号\")\n                return True\n            else:\n                error(\"清除Windsurf认证信息失败\")\n                return False\n                \n        except Exception as e:\n            error(f\"退出Windsurf账号失败: {e}\")\n            return False\n    \n    def _get_windsurf_config_dir(self) -> Optional[Path]:\n        \"\"\"获取Windsurf配置目录\n        \n        Returns:\n            Optional[Path]: 配置目录路径，未找到时返回None\n        \"\"\"\n        try:\n            if self.system == \"win32\":  # Windows\n                appdata = os.getenv(\"APPDATA\")\n                if appdata:\n                    return Path(appdata) / \"Windsurf\"\n            elif self.system == \"darwin\":  # macOS\n                return Path.home() / \"Library\" / \"Application Support\" / \"Windsurf\"\n            elif self.system == \"linux\":  # Linux\n                return Path.home() / \".config\" / \"Windsurf\"\n            \n            return None\n            \n        except Exception as e:\n            debug(f\"获取Windsurf配置目录失败: {e}\")\n            return None\n    \n    def _update_auth_info(self, email: str, token: str) -> bool:\n        \"\"\"更新认证信息\n        \n        Args:\n            email: 邮箱地址\n            token: 访问Token\n            \n        Returns:\n            bool: 更新是否成功\n        \"\"\"\n        try:\n            config_dir = self._get_windsurf_config_dir()\n            if not config_dir:\n                raise FileOperationError(\"无法找到Windsurf配置目录\")\n            \n            # 确保配置目录存在\n            config_dir.mkdir(parents=True, exist_ok=True)\n            \n            # 更新auth.json\n            auth_file = config_dir / \"auth.json\"\n            auth_data = {\n                \"email\": email,\n                \"accessToken\": token,\n                \"refreshToken\": token,\n                \"tokenType\": \"Bearer\"\n            }\n            \n            try:\n                with open(auth_file, 'w', encoding='utf-8') as f:\n                    json.dump(auth_data, f, ensure_ascii=False, indent=2)\n                debug(\"Windsurf auth.json更新成功\")\n            except Exception as e:\n                warning(f\"更新Windsurf auth.json失败: {e}\")\n            \n            return True\n            \n        except Exception as e:\n            error(f\"更新Windsurf认证信息失败: {e}\")\n            return False\n    \n    def _clear_auth_info(self) -> bool:\n        \"\"\"清除认证信息\n        \n        Returns:\n            bool: 清除是否成功\n        \"\"\"\n        try:\n            config_dir = self._get_windsurf_config_dir()\n            if not config_dir:\n                return False\n            \n            # 删除auth.json\n            auth_file = config_dir / \"auth.json\"\n            if auth_file.exists():\n                try:\n                    auth_file.unlink()\n                    debug(\"Windsurf auth.json已删除\")\n                except Exception as e:\n                    warning(f\"删除Windsurf auth.json失败: {e}\")\n            \n            return True\n            \n        except Exception as e:\n            error(f\"清除Windsurf认证信息失败: {e}\")\n            return False\n    \n    def _exit_windsurf(self):\n        \"\"\"退出Windsurf进程\"\"\"\n        try:\n            if self.system == \"win32\":\n                # Windows: 使用taskkill命令\n                subprocess.run([\"taskkill\", \"/f\", \"/im\", \"Windsurf.exe\"], \n                             capture_output=True, check=False)\n            elif self.system == \"darwin\":\n                # macOS: 使用pkill命令\n                subprocess.run([\"pkill\", \"-f\", \"Windsurf\"], \n                             capture_output=True, check=False)\n            elif self.system == \"linux\":\n                # Linux: 使用pkill命令\n                subprocess.run([\"pkill\", \"-f\", \"windsurf\"], \n                             capture_output=True, check=False)\n            \n            debug(\"Windsurf进程已退出\")\n            \n        except Exception as e:\n            warning(f\"退出Windsurf进程失败: {e}\")\n    \n    def _start_windsurf(self):\n        \"\"\"启动Windsurf\"\"\"\n        try:\n            if self.system == \"win32\":\n                # Windows: 尝试启动Windsurf\n                subprocess.Popen([\"windsurf\"], shell=True)\n            elif self.system == \"darwin\":\n                # macOS: 使用open命令\n                subprocess.Popen([\"open\", \"-a\", \"Windsurf\"])\n            elif self.system == \"linux\":\n                # Linux: 直接启动\n                subprocess.Popen([\"windsurf\"])\n            \n            debug(\"Windsurf已启动\")\n            \n        except Exception as e:\n            warning(f\"启动Windsurf失败: {e}\")\n\n\n# 便捷函数\ndef switch_windsurf_account(email: str, token: str) -> bool:\n    \"\"\"切换Windsurf账号（兼容旧接口）\n    \n    Args:\n        email: 邮箱地址\n        token: 访问Token\n        \n    Returns:\n        bool: 切换是否成功\n    \"\"\"\n    service = WindsurfAuthService()\n    return service.switch_account(email, token)\n\n\ndef get_current_windsurf_account() -> Optional[Dict[str, str]]:\n    \"\"\"获取当前Windsurf账号（兼容旧接口）\n    \n    Returns:\n        Optional[Dict[str, str]]: 当前账号信息\n    \"\"\"\n    service = WindsurfAuthService()\n    return service.get_current_account()\n"}
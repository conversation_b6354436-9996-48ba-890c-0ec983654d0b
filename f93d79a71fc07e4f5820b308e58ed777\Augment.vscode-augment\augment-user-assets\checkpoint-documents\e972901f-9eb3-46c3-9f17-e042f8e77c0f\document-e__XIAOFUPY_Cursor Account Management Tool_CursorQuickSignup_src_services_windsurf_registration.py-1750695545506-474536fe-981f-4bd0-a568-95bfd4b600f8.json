{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src\\services\\windsurf\\registration.py"}, "originalCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nWindsurf注册服务\n\n提供Windsurf账号注册的业务逻辑。\n\"\"\"\n\nimport os\nimport sys\nimport time\nimport random\nimport re\nfrom enum import Enum\nfrom typing import Optional, Tuple, Dict, Any\nfrom datetime import datetime\n\nfrom ...core import Config, info, error, warning, debug, get_database\nfrom ...models import WindsurfAccount, RegistrationResponse\nfrom ...utils import BrowserManager, WindsurfEmailHandler\nfrom ...core.exceptions import RegistrationError, BrowserError, EmailError\n\n\n# 常量定义\nSIGN_UP_URL = \"https://windsurf.com/account/register\"\nTOKEN_URL = \"https://windsurf.com/editor/signin?response_type=token&redirect_uri=show-auth-token\"\nONBOARDING_URL = \"https://windsurf.com/account/onboarding\"\nDEFAULT_USER_AGENT = \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"\n\n\nclass VerificationStatus(Enum):\n    \"\"\"验证状态枚举\"\"\"\n    PASSWORD_PAGE = \"Continue\"\n    CAPTCHA_PAGE = \"@data-index=0\"\n    ACCOUNT_SETTINGS = \"Account Settings\"\n\n\nclass TurnstileError(Exception):\n    \"\"\"Turnstile 验证相关异常\"\"\"\n    pass\n\n\nclass WindsurfRegistrationService:\n    \"\"\"Windsurf注册服务类\"\"\"\n    \n    def __init__(self, config: Optional[Config] = None):\n        \"\"\"初始化注册服务\n        \n        Args:\n            config: 配置实例，为None时使用默认配置\n        \"\"\"\n        self.config = config or Config()\n        self.database = get_database()\n        self.browser_manager: Optional[BrowserManager] = None\n        self.email_handler: Optional[WindsurfEmailHandler] = None\n    \n    def register_account(self, config_index: int = 0) -> RegistrationResponse:\n        \"\"\"注册单个账号\n\n        Args:\n            config_index: 配置索引\n\n        Returns:\n            RegistrationResponse: 注册结果\n        \"\"\"\n        try:\n            info(\"开始Windsurf账号注册流程\")\n\n            # 切换到指定配置\n            if not self.config.switch_config(config_index):\n                return RegistrationResponse.error_response(\"切换配置失败\")\n\n            # 初始化浏览器管理器\n            self.browser_manager = BrowserManager()\n\n            # 获取真实User-Agent\n            user_agent = self._get_real_user_agent()\n\n            # 初始化浏览器\n            browser = self.browser_manager.init_browser(user_agent, self.config.browser_headless)\n\n            # 初始化邮箱处理器\n            self.email_handler = WindsurfEmailHandler(self.config)\n\n            # 生成账号信息\n            email = self.email_handler.generate_email()\n            password = self.email_handler.generate_password()\n            first_name = self._generate_random_name()\n            last_name = self._generate_random_name()\n\n            info(f\"生成的邮箱: {email}\")\n            info(f\"生成的姓名: {first_name} {last_name}\")\n\n            # 执行注册流程\n            success, result_info = self._sign_up_account(\n                browser, browser.latest_tab, email, password,\n                first_name, last_name, SIGN_UP_URL\n            )\n\n            if success:\n                # 构建账号信息\n                account_data = {\n                    \"email\": email,\n                    \"password\": password,\n                    \"first_name\": first_name,\n                    \"last_name\": last_name,\n                    \"token\": result_info if isinstance(result_info, str) else None,\n                    \"timestamp\": datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n                }\n\n                # 保存账号到数据库\n                windsurf_account = WindsurfAccount(\n                    email=email,\n                    password=password,\n                    token=account_data.get(\"token\", \"\"),\n                    status=\"active\"\n                )\n\n                self.database.add_windsurf_account(windsurf_account.to_dict())\n\n                return RegistrationResponse.success_response(\n                    email=email,\n                    token=account_data.get(\"token\", \"\"),\n                    user_id=\"\",\n                    account_data=account_data\n                )\n            else:\n                error_msg = result_info or \"注册失败\"\n                return RegistrationResponse.error_response(error_msg)\n\n        except Exception as e:\n            error(f\"Windsurf账号注册失败: {e}\")\n            return RegistrationResponse.error_response(f\"注册失败: {e}\")\n        finally:\n            self._cleanup()\n    \n    def register_multiple_accounts(self, count: int, use_random_config: bool = False) -> Dict[str, Any]:\n        \"\"\"批量注册账号\n        \n        Args:\n            count: 注册数量\n            use_random_config: 是否使用随机配置\n            \n        Returns:\n            Dict[str, Any]: 批量注册结果\n        \"\"\"\n        results = {\n            \"total\": count,\n            \"success\": 0,\n            \"failed\": 0,\n            \"accounts\": [],\n            \"errors\": []\n        }\n        \n        # 保存原始配置索引\n        original_config_index = self.config.get_current_config_index()\n        \n        try:\n            for i in range(count):\n                info(f\"开始第 {i+1}/{count} 次Windsurf注册\")\n                \n                # 选择配置\n                config_index = self._select_config_index(use_random_config, original_config_index)\n                \n                # 注册账号\n                result = self.register_account(config_index)\n                \n                if result.success:\n                    results[\"success\"] += 1\n                    results[\"accounts\"].append({\n                        \"email\": result.email,\n                        \"token\": result.token,\n                        \"config_index\": config_index\n                    })\n                    info(f\"第 {i+1} 次Windsurf注册成功: {result.email}\")\n                else:\n                    results[\"failed\"] += 1\n                    results[\"errors\"].append({\n                        \"attempt\": i + 1,\n                        \"error\": result.message,\n                        \"config_index\": config_index\n                    })\n                    error(f\"第 {i+1} 次Windsurf注册失败: {result.message}\")\n                \n                # 等待间隔（除了最后一次）\n                if i < count - 1:\n                    wait_time = random.randint(5, 10)  # Windsurf可能需要更长等待时间\n                    info(f\"等待 {wait_time} 秒后继续...\")\n                    time.sleep(wait_time)\n            \n        finally:\n            # 恢复原始配置\n            if use_random_config:\n                self.config.switch_config(original_config_index)\n        \n        return results\n    \n    def _init_browser(self):\n        \"\"\"初始化浏览器\"\"\"\n        try:\n            self.browser_manager = BrowserManager()\n            user_agent = self._get_real_user_agent()\n            self.browser_manager.init_browser(user_agent, self.config.browser_headless)\n            info(\"浏览器初始化成功\")\n        except Exception as e:\n            raise BrowserError(f\"浏览器初始化失败: {e}\")\n    \n    def _init_email_handler(self, config_index: int):\n        \"\"\"初始化邮箱处理器\n        \n        Args:\n            config_index: 配置索引\n        \"\"\"\n        try:\n            self.email_handler = EmailHandler(config_index)\n            info(\"邮箱处理器初始化成功\")\n        except Exception as e:\n            raise EmailError(f\"邮箱处理器初始化失败: {e}\")\n    \n    def _generate_account_info(self) -> Dict[str, str]:\n        \"\"\"生成账号信息\n        \n        Returns:\n            Dict[str, str]: 账号信息\n        \"\"\"\n        # 这里应该调用邮箱生成器\n        # 暂时返回模拟数据\n        return {\n            \"email\": f\"windsurf_{int(time.time())}@example.com\",\n            \"password\": \"WindsurfPassword123!\",\n            \"first_name\": \"Windsurf\",\n            \"last_name\": \"User\"\n        }\n    \n    def _perform_registration(self, account_info: Dict[str, str]) -> RegistrationResponse:\n        \"\"\"执行注册流程\n        \n        Args:\n            account_info: 账号信息\n            \n        Returns:\n            RegistrationResponse: 注册结果\n        \"\"\"\n        try:\n            # 这里应该实现具体的Windsurf注册逻辑\n            # 包括：\n            # 1. 访问Windsurf注册页面\n            # 2. 填写注册信息\n            # 3. 处理人机验证\n            # 4. 处理邮箱验证码\n            # 5. 获取Token\n            \n            info(\"模拟Windsurf注册流程...\")\n            time.sleep(3)  # 模拟注册时间\n            \n            return RegistrationResponse.success_response(\n                email=account_info[\"email\"],\n                token=f\"windsurf_token_{int(time.time())}\",\n                user_id=f\"windsurf_user_{int(time.time())}\",\n                account_data=account_info\n            )\n            \n        except Exception as e:\n            return RegistrationResponse.error_response(f\"Windsurf注册流程失败: {e}\")\n    \n    def _save_account(self, result: RegistrationResponse):\n        \"\"\"保存账号到数据库\n        \n        Args:\n            result: 注册结果\n        \"\"\"\n        try:\n            account = WindsurfAccount(\n                email=result.email,\n                token=result.token,\n                user_id=result.user_id,\n                password=result.account_data.get(\"password\", \"\") if result.account_data else \"\",\n                status=\"active\"\n            )\n            \n            self.database.add_windsurf_account(account.to_dict())\n            \n        except Exception as e:\n            warning(f\"保存Windsurf账号到数据库失败: {e}\")\n    \n    def _select_config_index(self, use_random_config: bool, original_index: int) -> int:\n        \"\"\"选择配置索引\n        \n        Args:\n            use_random_config: 是否使用随机配置\n            original_index: 原始配置索引\n            \n        Returns:\n            int: 配置索引\n        \"\"\"\n        if not use_random_config:\n            return original_index\n        \n        config_count = self.config.get_config_count()\n        if config_count <= 1:\n            return original_index\n        \n        # 随机选择一个不同的配置\n        available_indices = list(range(config_count))\n        if original_index in available_indices and len(available_indices) > 1:\n            available_indices.remove(original_index)\n        \n        return random.choice(available_indices)\n    \n    def _get_real_user_agent(self) -> str:\n        \"\"\"获取真实的User-Agent\n        \n        Returns:\n            str: User-Agent字符串\n        \"\"\"\n        # 这里应该实现获取真实User-Agent的逻辑\n        # 暂时返回默认值\n        return \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"\n    \n    def _cleanup(self):\n        \"\"\"清理资源\"\"\"\n        if self.browser_manager:\n            try:\n                self.browser_manager.quit()\n                info(\"浏览器已关闭\")\n            except Exception as e:\n                warning(f\"关闭浏览器时出错: {e}\")\n            finally:\n                self.browser_manager = None\n        \n        self.email_handler = None\n\n\n# 便捷函数\ndef windsurf_quick_signup_process(config: Config, config_index: int = 0) -> Tuple[bool, Dict[str, Any], Optional[BrowserManager]]:\n    \"\"\"Windsurf快速注册流程（兼容旧接口）\n    \n    Args:\n        config: 配置实例\n        config_index: 配置索引\n        \n    Returns:\n        Tuple[bool, Dict[str, Any], Optional[BrowserManager]]: (成功状态, 账号数据, 浏览器管理器)\n    \"\"\"\n    service = WindsurfRegistrationService(config)\n    result = service.register_account(config_index)\n    \n    if result.success:\n        account_data = {\n            \"email\": result.email,\n            \"token\": result.token,\n            \"user_id\": result.user_id\n        }\n        if result.account_data:\n            account_data.update(result.account_data)\n        \n        return True, account_data, service.browser_manager\n    else:\n        return False, {\"error\": result.message}, service.browser_manager\n", "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nWindsurf注册服务\n\n提供Windsurf账号注册的业务逻辑。\n\"\"\"\n\nimport os\nimport sys\nimport time\nimport random\nimport re\nfrom enum import Enum\nfrom typing import Optional, Tuple, Dict, Any\nfrom datetime import datetime\n\nfrom ...core import Config, info, error, warning, debug, get_database\nfrom ...models import WindsurfAccount, RegistrationResponse\nfrom ...utils import BrowserManager, WindsurfEmailHandler\nfrom ...core.exceptions import RegistrationError, BrowserError, EmailError\n\n\n# 常量定义\nSIGN_UP_URL = \"https://windsurf.com/account/register\"\nTOKEN_URL = \"https://windsurf.com/editor/signin?response_type=token&redirect_uri=show-auth-token\"\nONBOARDING_URL = \"https://windsurf.com/account/onboarding\"\nDEFAULT_USER_AGENT = \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"\n\n\nclass VerificationStatus(Enum):\n    \"\"\"验证状态枚举\"\"\"\n    PASSWORD_PAGE = \"Continue\"\n    CAPTCHA_PAGE = \"@data-index=0\"\n    ACCOUNT_SETTINGS = \"Account Settings\"\n\n\nclass TurnstileError(Exception):\n    \"\"\"Turnstile 验证相关异常\"\"\"\n    pass\n\n\nclass WindsurfRegistrationService:\n    \"\"\"Windsurf注册服务类\"\"\"\n    \n    def __init__(self, config: Optional[Config] = None):\n        \"\"\"初始化注册服务\n        \n        Args:\n            config: 配置实例，为None时使用默认配置\n        \"\"\"\n        self.config = config or Config()\n        self.database = get_database()\n        self.browser_manager: Optional[BrowserManager] = None\n        self.email_handler: Optional[WindsurfEmailHandler] = None\n    \n    def register_account(self, config_index: int = 0) -> RegistrationResponse:\n        \"\"\"注册单个账号\n\n        Args:\n            config_index: 配置索引\n\n        Returns:\n            RegistrationResponse: 注册结果\n        \"\"\"\n        try:\n            info(\"开始Windsurf账号注册流程\")\n\n            # 切换到指定配置\n            if not self.config.switch_config(config_index):\n                return RegistrationResponse.error_response(\"切换配置失败\")\n\n            # 初始化浏览器管理器\n            self.browser_manager = BrowserManager()\n\n            # 获取真实User-Agent\n            user_agent = self._get_real_user_agent()\n\n            # 初始化浏览器\n            browser = self.browser_manager.init_browser(user_agent, self.config.browser_headless)\n\n            # 初始化邮箱处理器\n            self.email_handler = WindsurfEmailHandler(self.config)\n\n            # 生成账号信息\n            email = self.email_handler.generate_email()\n            password = self.email_handler.generate_password()\n            first_name = self._generate_random_name()\n            last_name = self._generate_random_name()\n\n            info(f\"生成的邮箱: {email}\")\n            info(f\"生成的姓名: {first_name} {last_name}\")\n\n            # 执行注册流程\n            success, result_info = self._sign_up_account(\n                browser, browser.latest_tab, email, password,\n                first_name, last_name, SIGN_UP_URL\n            )\n\n            if success:\n                # 构建账号信息\n                account_data = {\n                    \"email\": email,\n                    \"password\": password,\n                    \"first_name\": first_name,\n                    \"last_name\": last_name,\n                    \"token\": result_info if isinstance(result_info, str) else None,\n                    \"timestamp\": datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n                }\n\n                # 保存账号到数据库\n                windsurf_account = WindsurfAccount(\n                    email=email,\n                    password=password,\n                    token=account_data.get(\"token\", \"\"),\n                    status=\"active\"\n                )\n\n                self.database.add_windsurf_account(windsurf_account.to_dict())\n\n                return RegistrationResponse.success_response(\n                    email=email,\n                    token=account_data.get(\"token\", \"\"),\n                    user_id=\"\",\n                    account_data=account_data\n                )\n            else:\n                error_msg = result_info or \"注册失败\"\n                return RegistrationResponse.error_response(error_msg)\n\n        except Exception as e:\n            error(f\"Windsurf账号注册失败: {e}\")\n            return RegistrationResponse.error_response(f\"注册失败: {e}\")\n        finally:\n            self._cleanup()\n    \n    def register_multiple_accounts(self, count: int, use_random_config: bool = False) -> Dict[str, Any]:\n        \"\"\"批量注册账号\n        \n        Args:\n            count: 注册数量\n            use_random_config: 是否使用随机配置\n            \n        Returns:\n            Dict[str, Any]: 批量注册结果\n        \"\"\"\n        results = {\n            \"total\": count,\n            \"success\": 0,\n            \"failed\": 0,\n            \"accounts\": [],\n            \"errors\": []\n        }\n        \n        # 保存原始配置索引\n        original_config_index = self.config.get_current_config_index()\n        \n        try:\n            for i in range(count):\n                info(f\"开始第 {i+1}/{count} 次Windsurf注册\")\n                \n                # 选择配置\n                config_index = self._select_config_index(use_random_config, original_config_index)\n                \n                # 注册账号\n                result = self.register_account(config_index)\n                \n                if result.success:\n                    results[\"success\"] += 1\n                    results[\"accounts\"].append({\n                        \"email\": result.email,\n                        \"token\": result.token,\n                        \"config_index\": config_index\n                    })\n                    info(f\"第 {i+1} 次Windsurf注册成功: {result.email}\")\n                else:\n                    results[\"failed\"] += 1\n                    results[\"errors\"].append({\n                        \"attempt\": i + 1,\n                        \"error\": result.message,\n                        \"config_index\": config_index\n                    })\n                    error(f\"第 {i+1} 次Windsurf注册失败: {result.message}\")\n                \n                # 等待间隔（除了最后一次）\n                if i < count - 1:\n                    wait_time = random.randint(5, 10)  # Windsurf可能需要更长等待时间\n                    info(f\"等待 {wait_time} 秒后继续...\")\n                    time.sleep(wait_time)\n            \n        finally:\n            # 恢复原始配置\n            if use_random_config:\n                self.config.switch_config(original_config_index)\n        \n        return results\n    \n    def _init_browser(self):\n        \"\"\"初始化浏览器\"\"\"\n        try:\n            self.browser_manager = BrowserManager()\n            user_agent = self._get_real_user_agent()\n            self.browser_manager.init_browser(user_agent, self.config.browser_headless)\n            info(\"浏览器初始化成功\")\n        except Exception as e:\n            raise BrowserError(f\"浏览器初始化失败: {e}\")\n    \n    def _init_email_handler(self, config_index: int):\n        \"\"\"初始化邮箱处理器\n        \n        Args:\n            config_index: 配置索引\n        \"\"\"\n        try:\n            self.email_handler = EmailHandler(config_index)\n            info(\"邮箱处理器初始化成功\")\n        except Exception as e:\n            raise EmailError(f\"邮箱处理器初始化失败: {e}\")\n    \n    def _generate_account_info(self) -> Dict[str, str]:\n        \"\"\"生成账号信息\n        \n        Returns:\n            Dict[str, str]: 账号信息\n        \"\"\"\n        # 这里应该调用邮箱生成器\n        # 暂时返回模拟数据\n        return {\n            \"email\": f\"windsurf_{int(time.time())}@example.com\",\n            \"password\": \"WindsurfPassword123!\",\n            \"first_name\": \"Windsurf\",\n            \"last_name\": \"User\"\n        }\n    \n    def _perform_registration(self, account_info: Dict[str, str]) -> RegistrationResponse:\n        \"\"\"执行注册流程\n        \n        Args:\n            account_info: 账号信息\n            \n        Returns:\n            RegistrationResponse: 注册结果\n        \"\"\"\n        try:\n            # 这里应该实现具体的Windsurf注册逻辑\n            # 包括：\n            # 1. 访问Windsurf注册页面\n            # 2. 填写注册信息\n            # 3. 处理人机验证\n            # 4. 处理邮箱验证码\n            # 5. 获取Token\n            \n            info(\"模拟Windsurf注册流程...\")\n            time.sleep(3)  # 模拟注册时间\n            \n            return RegistrationResponse.success_response(\n                email=account_info[\"email\"],\n                token=f\"windsurf_token_{int(time.time())}\",\n                user_id=f\"windsurf_user_{int(time.time())}\",\n                account_data=account_info\n            )\n            \n        except Exception as e:\n            return RegistrationResponse.error_response(f\"Windsurf注册流程失败: {e}\")\n    \n    def _save_account(self, result: RegistrationResponse):\n        \"\"\"保存账号到数据库\n        \n        Args:\n            result: 注册结果\n        \"\"\"\n        try:\n            account = WindsurfAccount(\n                email=result.email,\n                token=result.token,\n                user_id=result.user_id,\n                password=result.account_data.get(\"password\", \"\") if result.account_data else \"\",\n                status=\"active\"\n            )\n            \n            self.database.add_windsurf_account(account.to_dict())\n            \n        except Exception as e:\n            warning(f\"保存Windsurf账号到数据库失败: {e}\")\n    \n    def _select_config_index(self, use_random_config: bool, original_index: int) -> int:\n        \"\"\"选择配置索引\n        \n        Args:\n            use_random_config: 是否使用随机配置\n            original_index: 原始配置索引\n            \n        Returns:\n            int: 配置索引\n        \"\"\"\n        if not use_random_config:\n            return original_index\n        \n        config_count = self.config.get_config_count()\n        if config_count <= 1:\n            return original_index\n        \n        # 随机选择一个不同的配置\n        available_indices = list(range(config_count))\n        if original_index in available_indices and len(available_indices) > 1:\n            available_indices.remove(original_index)\n        \n        return random.choice(available_indices)\n    \n    def _get_real_user_agent(self) -> str:\n        \"\"\"获取真实的User-Agent\n        \n        Returns:\n            str: User-Agent字符串\n        \"\"\"\n        # 这里应该实现获取真实User-Agent的逻辑\n        # 暂时返回默认值\n        return \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"\n    \n    def _cleanup(self):\n        \"\"\"清理资源\"\"\"\n        if self.browser_manager:\n            try:\n                self.browser_manager.quit()\n                info(\"浏览器已关闭\")\n            except Exception as e:\n                warning(f\"关闭浏览器时出错: {e}\")\n            finally:\n                self.browser_manager = None\n        \n        self.email_handler = None\n\n\n# 便捷函数\ndef windsurf_quick_signup_process(config: Config, config_index: int = 0) -> Tuple[bool, Dict[str, Any], Optional[BrowserManager]]:\n    \"\"\"Windsurf快速注册流程（兼容旧接口）\n    \n    Args:\n        config: 配置实例\n        config_index: 配置索引\n        \n    Returns:\n        Tuple[bool, Dict[str, Any], Optional[BrowserManager]]: (成功状态, 账号数据, 浏览器管理器)\n    \"\"\"\n    service = WindsurfRegistrationService(config)\n    result = service.register_account(config_index)\n    \n    if result.success:\n        account_data = {\n            \"email\": result.email,\n            \"token\": result.token,\n            \"user_id\": result.user_id\n        }\n        if result.account_data:\n            account_data.update(result.account_data)\n        \n        return True, account_data, service.browser_manager\n    else:\n        return False, {\"error\": result.message}, service.browser_manager\n"}
{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "main_gui.py"}, "originalCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nCursor工具主界面\n\n提供图形用户界面，包含邮箱配置、快速注册功能、账号管理和日志显示区域。\n使用标签页布局组织不同功能模块。\n\n模块化重构版本 v4.0 - 保持向后兼容\n\"\"\"\n\nimport os\nimport sys\nimport time\nimport threading\nimport random\nimport subprocess\nfrom pathlib import Path\nfrom typing import Optional, List, Dict, Any\n\nfrom PySide6.QtCore import Qt, QSize, Signal, Slot, QTimer, QEvent\nfrom PySide6.QtGui import QIcon, QFont, QTextCursor, QAction, QColor, QIntValidator\nfrom PySide6.QtWidgets import (\n    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,\n    QPushButton, QLabel, QLineEdit, QTextEdit, QGroupBox,\n    QFormLayout, QComboBox, QCheckBox, QMessageBox, QFileDialog,\n    QTabWidget, QSplitter, QToolBar, QStatusBar, QDialog\n)\n\n# 尝试导入新的模块化组件\ntry:\n    # 添加src目录到Python路径\n    src_path = Path(__file__).parent / \"src\"\n    if str(src_path) not in sys.path:\n        sys.path.insert(0, str(src_path))\n\n    # 导入新的模块化组件\n    from src.ui.main_window import MainWindow as NewMainWindow, create_application\n    USE_NEW_UI = True\n    print(\"使用新的模块化UI界面\")\nexcept ImportError as e:\n    print(f\"新模块化组件导入失败，使用原始界面: {e}\")\n    USE_NEW_UI = False\n\n# 导入原始项目模块\nfrom config import Config\nfrom custom_print import set_print_callback, info, error, warning, debug\nfrom cursor_quick_register import quick_signup_process\nimport app_utils\nfrom account_manager import AccountManagerWidget\nfrom augment_tools_widget import AugmentToolsWidget\nfrom windsurf_registration_widget import WindsurfRegistrationWidget, RestoreWindsurfButtonEvent, RestoreWindsurfResetButtonEvent\nfrom windsurf_account_manager import WindsurfAccountManagerWidget\n\n# 辅助函数：创建带样式的消息框\ndef create_styled_message_box(parent, icon, title, text, buttons=QMessageBox.Ok, default_button=QMessageBox.Ok):\n    \"\"\"创建带样式的消息框\n\n    Args:\n        parent: 父窗口\n        icon: 消息框图标类型\n        title: 标题\n        text: 消息内容\n        buttons: 按钮组合\n        default_button: 默认按钮\n\n    Returns:\n        消息框对象\n    \"\"\"\n    msg_box = QMessageBox(icon, title, text, buttons, parent)\n    msg_box.setDefaultButton(default_button)\n    msg_box.setStyleSheet(\"\"\"\n        QMessageBox {\n            background-color: white;\n            color: #333333;\n        }\n        QMessageBox QLabel {\n            color: #333333;\n            background-color: transparent;\n        }\n        QPushButton {\n            color: white;\n            background-color: #4285f4;\n            border: none;\n            border-radius: 4px;\n            padding: 6px 12px;\n            font-weight: 600;\n            font-size: 12px;\n        }\n        QPushButton:hover {\n            background-color: #3275e4;\n        }\n    \"\"\")\n    return msg_box\n\nclass LogHandler:\n    \"\"\"日志处理器，用于将日志输出到GUI\"\"\"\n\n    def __init__(self, text_edit: QTextEdit):\n        \"\"\"初始化日志处理器\n\n        Args:\n            text_edit: 用于显示日志的QTextEdit控件\n        \"\"\"\n        self.text_edit = text_edit\n\n    def handle_log(self, log_type: str, message: str):\n        \"\"\"处理日志消息\n\n        Args:\n            log_type: 日志类型，如'info', 'error', 'warning', 'debug'\n            message: 日志消息内容\n        \"\"\"\n        # 根据日志类型设置不同颜色，确保颜色足够深，便于查看\n        color_map = {\n            'info': '#000000',    # 黑色\n            'error': '#FF0000',   # 红色\n            'warning': '#FF6600', # 橙色\n            'debug': '#0000FF'    # 蓝色\n        }\n        color = color_map.get(log_type, '#000000')\n\n        # 获取当前时间\n        timestamp = time.strftime('%H:%M:%S')\n\n        # 格式化日志消息，确保使用HTML格式\n        formatted_message = f\"<span style='color:{color}; font-weight:normal;'>[{timestamp}] [{log_type.upper()}] {message}</span>\"\n\n        # 添加到文本框并滚动到底部\n        self.text_edit.append(formatted_message)\n        self.text_edit.moveCursor(QTextCursor.End)\n\n\nclass RegistrationWidget(QWidget):\n    \"\"\"注册功能标签页\"\"\"\n\n    def __init__(self, parent=None):\n        \"\"\"初始化注册标签页\"\"\"\n        super().__init__(parent)\n\n        # 加载配置\n        self.config = Config()\n\n        # 创建主布局\n        self.main_layout = QVBoxLayout(self)\n        self.main_layout.setContentsMargins(8, 8, 8, 8)\n        self.main_layout.setSpacing(6)\n\n        # 创建配置区域\n        self.create_config_section()\n\n        # 创建按钮区域\n        self.create_button_section()\n\n        # 创建日志区域\n        self.create_log_section()\n\n        # 加载配置到UI\n        self.load_config_to_ui()\n\n    def create_config_section(self):\n        \"\"\"创建配置区域\"\"\"\n        config_group = QGroupBox(\"邮箱配置\")\n        config_layout = QFormLayout()\n        config_layout.setSpacing(4)\n        config_layout.setContentsMargins(8, 8, 8, 8)\n\n        # 配置选择和管理\n        config_selection_layout = QHBoxLayout()\n        config_selection_layout.setSpacing(6)\n\n        # 配置选择下拉框\n        self.config_combo = QComboBox()\n        self.config_combo.currentIndexChanged.connect(self.on_config_selected)\n        config_selection_layout.addWidget(self.config_combo, 2)\n\n        # 配置管理按钮\n        manage_config_btn = QPushButton(\"管理配置\")\n        manage_config_btn.clicked.connect(self.manage_configs)\n        config_selection_layout.addWidget(manage_config_btn)\n\n        config_layout.addRow(\"当前配置:\", config_selection_layout)\n\n        # 不显示任何额外信息，保持界面简洁\n\n        # 循环注册次数\n        loop_layout = QHBoxLayout()\n        loop_layout.setSpacing(6)\n\n        self.loop_count_input = QLineEdit(\"1\")\n        self.loop_count_input.setFixedWidth(60)\n        # 只允许输入数字\n        self.loop_count_input.setValidator(QIntValidator(1, 100))\n        loop_layout.addWidget(self.loop_count_input)\n\n        loop_label = QLabel(\"次 (最多100次)\")\n        loop_layout.addWidget(loop_label)\n\n        # 添加随机配置选项\n        self.random_config_checkbox = QCheckBox(\"随机配置\")\n        self.random_config_checkbox.setToolTip(\"启用后，每次循环将随机选择一个配置进行注册\")\n        # 只有当循环次数大于1时才启用\n        self.random_config_checkbox.setEnabled(False)\n        # 连接循环次数变化信号\n        self.loop_count_input.textChanged.connect(self.update_random_config_state)\n        loop_layout.addWidget(self.random_config_checkbox)\n\n        loop_layout.addStretch()\n\n        config_layout.addRow(\"循环注册:\", loop_layout)\n\n        # Cursor路径设置\n        cursor_path_layout = QHBoxLayout()\n        cursor_path_layout.setSpacing(6)\n\n        # 显示当前Cursor路径\n        self.cursor_path_label = QLabel(\"自动检测\")\n        self.cursor_path_label.setStyleSheet(\"color: #666666; font-size: 10pt;\")\n        self.cursor_path_label.setWordWrap(True)\n        cursor_path_layout.addWidget(self.cursor_path_label, 2)\n\n        # 浏览按钮\n        browse_cursor_btn = QPushButton(\"浏览\")\n        browse_cursor_btn.setFixedWidth(60)\n        browse_cursor_btn.clicked.connect(self.browse_cursor_path)\n        cursor_path_layout.addWidget(browse_cursor_btn)\n\n        # 清除按钮\n        clear_cursor_btn = QPushButton(\"清除\")\n        clear_cursor_btn.setFixedWidth(60)\n        clear_cursor_btn.clicked.connect(self.clear_cursor_path)\n        cursor_path_layout.addWidget(clear_cursor_btn)\n\n        config_layout.addRow(\"Cursor路径:\", cursor_path_layout)\n\n        # 不再需要浏览器配置勾选框，因为已经在配置中设置了\n\n        config_group.setLayout(config_layout)\n        self.main_layout.addWidget(config_group)\n\n        # 加载配置列表\n        self.load_config_list()\n\n    def create_button_section(self):\n        \"\"\"创建按钮区域\"\"\"\n        button_layout = QHBoxLayout()\n        button_layout.setSpacing(8)\n\n        # 重置ID按钮\n        self.reset_id_btn = QPushButton(\"重置ID码\")\n        self.reset_id_btn.setMinimumHeight(36)\n        self.reset_id_btn.clicked.connect(self.reset_id)\n        button_layout.addWidget(self.reset_id_btn)\n\n        # 快速注册按钮\n        self.register_btn = QPushButton(\"快速注册\")\n        self.register_btn.setMinimumHeight(36)\n        self.register_btn.clicked.connect(self.start_registration)\n        button_layout.addWidget(self.register_btn)\n\n        # 完整流程按钮\n        self.full_process_btn = QPushButton(\"完整流程\")\n        self.full_process_btn.setMinimumHeight(36)\n        self.full_process_btn.clicked.connect(self.start_full_process)\n        button_layout.addWidget(self.full_process_btn)\n\n        self.main_layout.addLayout(button_layout)\n\n    def create_log_section(self):\n        \"\"\"创建日志区域\"\"\"\n        log_group = QGroupBox(\"日志\")\n        log_layout = QVBoxLayout()\n        log_layout.setContentsMargins(8, 8, 8, 8)\n        log_layout.setSpacing(4)\n\n        # 日志文本框\n        self.log_text = QTextEdit()\n        self.log_text.setReadOnly(True)\n        self.log_text.setLineWrapMode(QTextEdit.WidgetWidth)\n        self.log_text.setStyleSheet(\"\"\"\n            font-family: Consolas, monospace;\n            font-size: 10pt;\n            color: black;\n            background-color: white;\n            border: 1px solid #cccccc;\n            padding: 4px;\n        \"\"\")\n        log_layout.addWidget(self.log_text)\n\n        # 在初始化时放入一条测试信息\n        self.log_text.append(\"<span style='color:#0000FF; font-weight:normal;'>[测试] 日志区域已初始化，文本应该可见</span>\")\n\n        # 清空日志按钮\n        clear_log_btn = QPushButton(\"清空日志\")\n        clear_log_btn.clicked.connect(self.clear_log)\n        log_layout.addWidget(clear_log_btn)\n\n        log_group.setLayout(log_layout)\n        self.main_layout.addWidget(log_group, 1)  # 1表示拉伸因子，让日志区域占据更多空间\n\n    def load_config_list(self):\n        \"\"\"加载配置列表到下拉框\"\"\"\n        try:\n            # 清空下拉框\n            self.config_combo.clear()\n\n            # 获取配置列表\n            config_list = self.config.get_config_list()\n\n            # 添加到下拉框\n            for i, config_data in enumerate(config_list):\n                # 获取配置名称\n                temp_mail = config_data.get(\"TEMP_MAIL\", \"\")\n                domain = config_data.get(\"DOMAIN\", \"\")\n\n                # 创建显示文本\n                display_text = f\"配置 {i+1}: {temp_mail} - {domain}\"\n\n                # 添加到下拉框\n                self.config_combo.addItem(display_text, i)\n\n            # 选择当前配置\n            current_index = self.config.get_current_config_index()\n            if current_index < self.config_combo.count():\n                self.config_combo.setCurrentIndex(current_index)\n\n            info(f\"已加载 {self.config_combo.count()} 个配置\")\n        except Exception as e:\n            error(f\"加载配置列表失败: {e}\")\n\n    def on_config_selected(self, index):\n        \"\"\"配置选择事件处理\n\n        Args:\n            index: 选中的配置索引\n        \"\"\"\n        if index < 0:\n            return\n\n        try:\n            # 切换到选中的配置\n            if self.config.switch_config(index):\n                info(f\"已切换到配置 {index}\")\n\n                # 更新UI\n                self.load_config_to_ui()\n\n                app_utils.update_status(f\"已切换到配置 {index+1}\")\n            else:\n                error(f\"切换配置失败\")\n        except Exception as e:\n            error(f\"切换配置失败: {e}\")\n\n    def manage_configs(self):\n        \"\"\"打开配置管理对话框\"\"\"\n        try:\n            # 导入配置管理对话框\n            from config_manager_dialog import ConfigManagerDialog\n\n            # 创建对话框\n            dialog = ConfigManagerDialog(self.config, self)\n\n            # 连接配置变更信号\n            dialog.config_changed.connect(self.on_config_changed)\n\n            # 显示对话框\n            dialog.exec()\n        except Exception as e:\n            error(f\"打开配置管理对话框失败: {e}\")\n            msg_box = create_styled_message_box(self, QMessageBox.Critical, \"错误\", f\"打开配置管理对话框失败: {e}\")\n            msg_box.exec()\n\n    def on_config_changed(self):\n        \"\"\"配置变更事件处理\"\"\"\n        try:\n            # 重新加载配置列表\n            self.load_config_list()\n\n            # 更新UI\n            self.load_config_to_ui()\n\n            # 更新随机配置复选框状态\n            self.update_random_config_state()\n\n            info(\"配置已更新\")\n        except Exception as e:\n            error(f\"更新配置失败: {e}\")\n\n    def update_random_config_state(self):\n        \"\"\"更新随机配置复选框状态\"\"\"\n        try:\n            # 获取循环次数\n            try:\n                loop_count = int(self.loop_count_input.text())\n            except ValueError:\n                loop_count = 1\n\n            # 获取配置数量\n            config_count = self.config.get_config_count()\n\n            # 只有当循环次数大于1且配置数量大于1时才启用随机配置选项\n            self.random_config_checkbox.setEnabled(loop_count > 1 and config_count > 1)\n\n            # 如果不满足条件，取消选中\n            if not (loop_count > 1 and config_count > 1):\n                self.random_config_checkbox.setChecked(False)\n        except Exception as e:\n            error(f\"更新随机配置状态失败: {e}\")\n\n    # 移除 update_account_info 方法，因为不再需要它\n\n    def load_config_to_ui(self):\n        \"\"\"将配置加载到UI控件\"\"\"\n        try:\n            # 更新Cursor路径显示\n            self.update_cursor_path_display()\n            info(\"配置已加载到界面\")\n        except Exception as e:\n            error(f\"加载配置失败: {e}\")\n\n    def update_cursor_path_display(self):\n        \"\"\"更新Cursor路径显示\"\"\"\n        try:\n            cursor_path = self.config.get_cursor_manual_path()\n            if cursor_path:\n                # 显示手动设置的路径，截断过长的路径\n                if len(cursor_path) > 50:\n                    display_path = \"...\" + cursor_path[-47:]\n                else:\n                    display_path = cursor_path\n                self.cursor_path_label.setText(display_path)\n                self.cursor_path_label.setToolTip(f\"手动设置: {cursor_path}\")\n                self.cursor_path_label.setStyleSheet(\"color: #2e7d32; font-size: 10pt;\")\n            else:\n                self.cursor_path_label.setText(\"自动检测\")\n                self.cursor_path_label.setToolTip(\"将自动检测Cursor安装位置\")\n                self.cursor_path_label.setStyleSheet(\"color: #666666; font-size: 10pt;\")\n        except Exception as e:\n            error(f\"更新Cursor路径显示失败: {e}\")\n\n    def browse_cursor_path(self):\n        \"\"\"浏览选择Cursor.exe路径\"\"\"\n        try:\n            # 打开文件选择对话框\n            file_path, _ = QFileDialog.getOpenFileName(\n                self,\n                \"选择Cursor.exe文件\",\n                \"\",\n                \"可执行文件 (*.exe);;所有文件 (*.*)\"\n            )\n\n            if file_path:\n                # 验证选择的文件\n                if not file_path.lower().endswith(\"cursor.exe\"):\n                    msg_box = create_styled_message_box(\n                        self,\n                        QMessageBox.Warning,\n                        \"警告\",\n                        \"请选择Cursor.exe文件\"\n                    )\n                    msg_box.exec()\n                    return\n\n                # 设置路径\n                if self.config.set_cursor_manual_path(file_path):\n                    self.update_cursor_path_display()\n                    info(f\"已设置Cursor路径: {file_path}\")\n                    msg_box = create_styled_message_box(\n                        self,\n                        QMessageBox.Information,\n                        \"成功\",\n                        f\"已设置Cursor路径:\\n{file_path}\"\n                    )\n                    msg_box.exec()\n                else:\n                    msg_box = create_styled_message_box(\n                        self,\n                        QMessageBox.Critical,\n                        \"错误\",\n                        \"设置Cursor路径失败\"\n                    )\n                    msg_box.exec()\n        except Exception as e:\n            error(f\"浏览Cursor路径时出错: {e}\")\n            msg_box = create_styled_message_box(\n                self,\n                QMessageBox.Critical,\n                \"错误\",\n                f\"浏览Cursor路径时出错: {e}\"\n            )\n            msg_box.exec()\n\n    def clear_cursor_path(self):\n        \"\"\"清除手动设置的Cursor路径\"\"\"\n        try:\n            # 确认对话框\n            msg_box = create_styled_message_box(\n                self,\n                QMessageBox.Question,\n                \"确认\",\n                \"确定要清除手动设置的Cursor路径吗？\\n清除后将使用自动检测。\",\n                QMessageBox.Yes | QMessageBox.No,\n                QMessageBox.No\n            )\n\n            if msg_box.exec() == QMessageBox.Yes:\n                if self.config.set_cursor_manual_path(\"\"):\n                    self.update_cursor_path_display()\n                    info(\"已清除Cursor手动路径设置\")\n                    msg_box = create_styled_message_box(\n                        self,\n                        QMessageBox.Information,\n                        \"成功\",\n                        \"已清除Cursor路径设置，将使用自动检测\"\n                    )\n                    msg_box.exec()\n                else:\n                    msg_box = create_styled_message_box(\n                        self,\n                        QMessageBox.Critical,\n                        \"错误\",\n                        \"清除Cursor路径设置失败\"\n                    )\n                    msg_box.exec()\n        except Exception as e:\n            error(f\"清除Cursor路径时出错: {e}\")\n            msg_box = create_styled_message_box(\n                self,\n                QMessageBox.Critical,\n                \"错误\",\n                f\"清除Cursor路径时出错: {e}\"\n            )\n            msg_box.exec()\n\n    def save_config(self):\n        \"\"\"保存当前配置\"\"\"\n        try:\n            # 保存到文件\n            if self.config.save_config():\n                info(\"配置已保存\")\n                app_utils.update_status(\"配置已保存\")\n\n                # 重新加载配置列表\n                self.load_config_list()\n\n                info(\"配置已立即生效\")\n            else:\n                error(\"保存配置失败\")\n                app_utils.update_status(\"保存配置失败\", 3000)\n        except Exception as e:\n            error(f\"保存配置时发生错误: {e}\")\n            app_utils.update_status(f\"保存配置时发生错误: {e}\", 3000)\n\n    def reset_id(self):\n        \"\"\"启动重置ID工具\"\"\"\n        try:\n            # 导入重置工具模块\n            from combined_reset_gui import ResetThread\n\n            # 禁用所有按钮\n            self.reset_id_btn.setEnabled(False)\n            self.register_btn.setEnabled(False)\n            self.full_process_btn.setEnabled(False)\n            self.reset_id_btn.setText(\"重置中...\")\n            app_utils.update_status(\"正在执行重置ID操作...\")\n\n            # 清空日志\n            self.log_text.clear()\n\n            # 创建并启动线程\n            self.reset_thread = ResetThread(\"all\")\n            self.reset_thread.update_log.connect(lambda msg: info(f\"【重置ID】{msg}\"))\n            self.reset_thread.update_progress.connect(lambda value: app_utils.update_status(f\"重置ID进度: {value}%\"))\n            self.reset_thread.finished_signal.connect(self.reset_id_finished)\n            self.reset_thread.start()\n\n            info(\"已启动重置ID操作\")\n        except Exception as e:\n            error(f\"启动重置ID操作时发生错误: {e}\")\n            msg_box = create_styled_message_box(self, QMessageBox.Critical, \"错误\", f\"启动重置ID操作时发生错误: {e}\")\n            msg_box.exec_()\n            # 恢复按钮状态\n            self.reset_id_btn.setEnabled(True)\n            self.register_btn.setEnabled(True)\n            self.full_process_btn.setEnabled(True)\n            self.reset_id_btn.setText(\"重置ID码\")\n\n    def reset_id_finished(self, success, message):\n        \"\"\"重置ID操作完成的回调\"\"\"\n        # 恢复按钮状态\n        self.reset_id_btn.setEnabled(True)\n        self.register_btn.setEnabled(True)\n        self.full_process_btn.setEnabled(True)\n        self.reset_id_btn.setText(\"重置ID码\")\n\n        # 显示结果\n        if success:\n            info(f\"【重置ID】成功: {message}\")\n            app_utils.update_status(\"重置ID成功完成\")\n            msg_box = create_styled_message_box(self, QMessageBox.Information, \"成功\", f\"重置ID成功: {message}\")\n            msg_box.exec_()\n        else:\n            error(f\"【重置ID】失败: {message}\")\n            app_utils.update_status(\"重置ID失败\", 3000)\n            msg_box = create_styled_message_box(self, QMessageBox.Warning, \"失败\", f\"重置ID失败: {message}\")\n            msg_box.exec_()\n\n    def start_registration(self):\n        \"\"\"开始注册过程\"\"\"\n        # 获取循环次数\n        try:\n            loop_count = int(self.loop_count_input.text())\n            if loop_count < 1:\n                loop_count = 1\n            elif loop_count > 100:\n                loop_count = 100\n        except ValueError:\n            loop_count = 1\n            self.loop_count_input.setText(\"1\")\n\n        # 禁用注册按钮，防止重复点击\n        self.register_btn.setEnabled(False)\n        if loop_count > 1:\n            self.register_btn.setText(f\"注册中({loop_count}次)...\")\n        else:\n            self.register_btn.setText(\"注册中...\")\n        app_utils.update_status(f\"正在进行注册，计划循环{loop_count}次...\")\n\n        # 创建线程执行注册过程\n        threading.Thread(target=self._run_registration, daemon=True).start()\n\n    def start_full_process(self):\n        \"\"\"启动完整流程（重置ID、注册、切换账号）\"\"\"\n        # 禁用所有按钮\n        self.full_process_btn.setEnabled(False)\n        self.register_btn.setEnabled(False)\n        self.reset_id_btn.setEnabled(False)\n        self.full_process_btn.setText(\"处理中...\")\n        app_utils.update_status(\"正在执行完整流程...\")\n\n        # 创建线程执行完整流程\n        threading.Thread(target=self._run_full_process, daemon=True).start()\n\n    def _run_full_process(self):\n        \"\"\"在线程中运行完整流程\"\"\"\n        browser_manager = None\n        try:\n            # 不再需要更新浏览器配置，因为已经在配置中设置了\n\n            # 保存配置\n            self.config.save_config()\n            info(\"配置已保存并立即生效\")\n\n            # 步骤1: 重置ID\n            info(\"【完整流程】步骤1: 重置ID...\")\n            from combined_reset_gui import ResetThread\n\n            # 创建重置线程并等待完成\n            reset_thread = ResetThread(\"all\")\n            reset_thread.update_log.connect(lambda msg: info(f\"【重置ID】{msg}\"))\n\n            # 使用一个变量来存储重置结果（作为备用）\n            reset_message = [\"\"]\n\n            # 连接信号，接收重置结果信息\n            def handle_reset_finished(success_status, message):\n                # 使用success_status参数，避免IDE警告\n                if success_status:\n                    reset_message[0] = message\n                else:\n                    reset_message[0] = f\"失败: {message}\"\n\n            reset_thread.finished_signal.connect(handle_reset_finished)\n            reset_thread.start()\n            reset_thread.wait()  # 等待重置完成\n\n            # 检查重置结果\n            if not reset_thread.success:\n                error(f\"【完整流程】重置ID失败，终止流程: {reset_message[0]}\")\n                QApplication.postEvent(self, RestoreFullProcessButtonsEvent())\n                return\n\n            info(\"【完整流程】重置ID成功，等待3秒后继续...\")\n            time.sleep(3)  # 等待一段时间确保系统稳定\n\n            # 步骤2: 快速注册\n            info(\"【完整流程】步骤2: 快速注册...\")\n            success, account_data, browser_manager = quick_signup_process(self.config)\n\n            if not success:\n                error(\"【完整流程】快速注册失败，终止流程\")\n                if \"error\" in account_data:\n                    error(f\"错误信息: {account_data['error']}\")\n                # 确保在 return 前关闭浏览器\n                if browser_manager is not None:\n                    try:\n                        browser_manager.quit()\n                        info(\"浏览器已关闭\")\n                    except Exception as e:\n                        warning(f\"关闭浏览器时出错: {e}\")\n                return\n\n            # 注册成功，输出账号信息\n            info(\"【完整流程】快速注册成功！\")\n            email = account_data.get('email', '未知')\n            token = account_data.get('token', '')\n            info(f\"邮箱: {email}\")\n            if token:\n                token_preview = token[:20] + \"...\" if len(token) > 20 else token\n                info(f\"Token: {token_preview}（已截断）\")\n\n            # 通知账号管理页面刷新账号列表\n            QApplication.postEvent(QApplication.instance().mainWindow, RefreshAccountsEvent())\n            info(\"【完整流程】等待3秒后继续...\")\n            time.sleep(3)\n\n            # 步骤3: 切换到新注册的账号\n            info(\"【完整流程】步骤3: 切换到新注册的账号...\")\n            if email != '未知' and token:\n                try:\n                    # 先退出Cursor\n                    from exit_cursor import ExitCursor\n                    info(f\"正在退出Cursor...\")\n                    ExitCursor()\n\n                    # 等待一段时间确保Cursor已关闭\n                    time.sleep(1)\n\n                    # 更新认证信息\n                    info(f\"正在更新认证信息...\")\n\n                    # 获取数据库路径\n                    system = sys.platform\n                    db_path = None\n\n                    if system == \"win32\":  # Windows\n                        appdata = os.getenv(\"APPDATA\")\n                        if appdata:\n                            db_path = Path(appdata) / \"Cursor\" / \"User\" / \"globalStorage\" / \"state.vscdb\"\n                    elif system == \"darwin\":  # macOS\n                        db_path = Path.home() / \"Library\" / \"Application Support\" / \"Cursor\" / \"User\" / \"globalStorage\" / \"state.vscdb\"\n                    elif system == \"linux\":  # Linux\n                        db_path = Path.home() / \".config\" / \"Cursor\" / \"User\" / \"globalStorage\" / \"state.vscdb\"\n\n                    if not db_path or not db_path.exists():\n                        raise FileNotFoundError(f\"找不到Cursor数据库文件\")\n\n                    # 连接数据库并更新认证信息\n                    import sqlite3\n                    conn = sqlite3.connect(str(db_path))\n                    cursor = conn.cursor()\n\n                    # 需要更新的键值对\n                    updates = [\n                        (\"cursorAuth/cachedSignUpType\", \"Auth_0\"),\n                        (\"cursorAuth/cachedEmail\", email),\n                        (\"cursorAuth/accessToken\", token),\n                        (\"cursorAuth/refreshToken\", token)\n                    ]\n\n                    for key, value in updates:\n                        # 检查键是否存在\n                        cursor.execute(\"SELECT COUNT(*) FROM itemTable WHERE key = ?\", (key,))\n                        if cursor.fetchone()[0] == 0:\n                            # 插入新记录\n                            cursor.execute(\"INSERT INTO itemTable (key, value) VALUES (?, ?)\", (key, value))\n                        else:\n                            # 更新现有记录\n                            cursor.execute(\"UPDATE itemTable SET value = ? WHERE key = ?\", (value, key))\n\n                    # 提交更改并关闭连接\n                    conn.commit()\n                    conn.close()\n\n                    # 同时也更新auth.json文件以保持一致性\n                    from app_utils import switch_cursor_account\n                    switch_cursor_account(email, token)\n\n                    # 启动Cursor\n                    info(f\"正在启动Cursor...\")\n                    if system == \"win32\":\n                        os.startfile(\"cursor://\")\n                    elif system == \"darwin\":\n                        subprocess.Popen([\"open\", \"cursor://\"])\n                    elif system == \"linux\":\n                        subprocess.Popen([\"xdg-open\", \"cursor://\"])\n\n                    info(f\"【完整流程】成功切换到账号: {email}\")\n                except Exception as e:\n                    error(f\"【完整流程】切换账号时发生错误: {e}\")\n            else:\n                error(\"【完整流程】缺少账号信息，无法切换账号\")\n\n            # 完整流程结束\n            info(\"【完整流程】全部完成!\")\n\n        except Exception as e:\n            error(f\"【完整流程】发生错误: {e}\")\n        finally:\n            # 确保浏览器被关闭\n            if browser_manager is not None:\n                try:\n                    browser_manager.quit()\n                    info(\"浏览器已关闭\")\n                except Exception as e:\n                    warning(f\"关闭浏览器时出错: {e}\")\n\n            # 恢复按钮状态\n            QApplication.postEvent(self, RestoreFullProcessButtonsEvent())\n\n    def _run_registration(self):\n        \"\"\"在线程中运行注册过程\"\"\"\n        browser_manager = None\n        try:\n            # 获取循环注册次数\n            try:\n                loop_count = int(self.loop_count_input.text())\n                if loop_count < 1:\n                    loop_count = 1\n                elif loop_count > 100:\n                    loop_count = 100\n            except ValueError:\n                loop_count = 1\n\n            # 不再需要更新浏览器配置，因为已经在配置中设置了\n\n            # 保存配置\n            self.config.save_config()\n            info(\"配置已保存并立即生效\")\n\n            info(f\"开始Cursor快速注册流程，循环次数: {loop_count}\")\n\n            # 获取是否使用随机配置\n            use_random_config = self.random_config_checkbox.isChecked() and self.random_config_checkbox.isEnabled()\n\n            # 如果使用随机配置，获取所有配置\n            config_list = []\n            if use_random_config:\n                config_list = self.config.get_config_list()\n                info(f\"启用随机配置，共有 {len(config_list)} 个配置可用\")\n\n            # 保存当前配置索引，以便在循环结束后恢复\n            original_config_index = self.config.get_current_config_index()\n\n            # 统计信息\n            start_time = time.time()\n            success_count = 0\n            fail_count = 0\n\n            # 循环注册\n            for i in range(loop_count):\n                if loop_count > 1:\n                    info(f\"\\n=== 开始第 {i+1}/{loop_count} 次注册 ===\")\n                    # 更新状态栏\n                    app_utils.update_status(f\"正在进行第 {i+1}/{loop_count} 次注册...\")\n\n                # 如果使用随机配置，随机选择一个配置\n                if use_random_config and len(config_list) > 1:\n                    # 随机选择一个配置索引，但不选择当前配置\n                    available_indices = list(range(len(config_list)))\n                    if len(available_indices) > 1:  # 如果有多个配置可选\n                        current_index = self.config.get_current_config_index()\n                        if current_index in available_indices:\n                            available_indices.remove(current_index)\n\n                    # 随机选择一个配置索引\n                    random_index = random.choice(available_indices)\n\n                    # 切换到随机配置\n                    self.config.switch_config(random_index)\n                    info(f\"随机选择配置 {random_index+1}\")\n\n                # 每次循环都需要关闭之前的浏览器\n                if browser_manager is not None:\n                    try:\n                        browser_manager.quit()\n                        info(\"浏览器已关闭\")\n                    except Exception as e:\n                        warning(f\"关闭浏览器时出错: {e}\")\n                    browser_manager = None\n\n                # 执行注册\n                success, account_data, browser_manager = quick_signup_process(self.config)\n\n                if success:\n                    success_count += 1\n                    info(f\"Cursor账号注册成功！({success_count}/{i+1})\")\n                    if \"email\" in account_data:\n                        info(f\"邮箱: {account_data['email']}\")\n                    if \"token\" in account_data:\n                        token_preview = account_data['token'][:20] + \"...\" if len(account_data['token']) > 20 else account_data['token']\n                        info(f\"Token: {token_preview}（已截断）\")\n                        info(f\"Token有效期: {account_data.get('token_info', {}).get('expiry_formatted', '未知')}\")\n\n                    # 通知账号管理页面刷新账号列表\n                    QApplication.postEvent(QApplication.instance().mainWindow, RefreshAccountsEvent())\n                else:\n                    fail_count += 1\n                    error(f\"Cursor账号注册失败！({fail_count}/{i+1})\")\n                    if \"error\" in account_data:\n                        error(f\"错误信息: {account_data['error']}\")\n\n                # 如果不是最后一次循环，等待一段时间再继续\n                if i < loop_count - 1:\n                    wait_time = random.randint(3, 6)\n                    info(f\"等待 {wait_time} 秒后继续下一次注册...\")\n                    time.sleep(wait_time)\n\n            # 统计结果\n            end_time = time.time()\n            elapsed_time = end_time - start_time\n            minutes, seconds = divmod(int(elapsed_time), 60)\n            hours, minutes = divmod(minutes, 60)\n\n            info(\"\\n=== 注册统计 ===\")\n            info(f\"总注册次数: {loop_count}\")\n            info(f\"成功次数: {success_count}\")\n            info(f\"失败次数: {fail_count}\")\n            info(f\"总用时: {hours}小时{minutes}分{seconds}秒\")\n            if loop_count > 0:\n                avg_time = elapsed_time / loop_count\n                avg_minutes, avg_seconds = divmod(int(avg_time), 60)\n                info(f\"平均每次用时: {avg_minutes}分{avg_seconds}秒\")\n\n            # 如果使用了随机配置，恢复原始配置\n            if use_random_config and self.config.get_current_config_index() != original_config_index:\n                self.config.switch_config(original_config_index)\n                info(f\"已恢复到原始配置 {original_config_index+1}\")\n\n        except Exception as e:\n            error(f\"注册过程发生错误: {e}\")\n            import traceback\n            error(traceback.format_exc())\n        finally:\n            # 确保浏览器被关闭\n            if browser_manager is not None:\n                try:\n                    browser_manager.quit()\n                    info(\"浏览器已关闭\")\n                except Exception as e:\n                    warning(f\"关闭浏览器时出错: {e}\")\n\n            # 恢复按钮状态 - 确保在主线程中执行\n            QApplication.postEvent(QApplication.instance().mainWindow, RestoreButtonEvent())\n\n    def clear_log(self):\n        \"\"\"清空日志区域\"\"\"\n        self.log_text.clear()\n        info(\"日志已清空\")\n\n# 添加新的事件类型\nclass RestoreFullProcessButtonsEvent(QEvent):\n    \"\"\"恢复完整流程所有按钮状态事件\"\"\"\n\n    EVENT_TYPE = QEvent.Type(QEvent.registerEventType())\n\n    def __init__(self):\n        \"\"\"初始化恢复完整流程按钮状态事件\"\"\"\n        super().__init__(self.EVENT_TYPE)\n\n\nclass MainWindow(QMainWindow):\n    \"\"\"主窗口类\"\"\"\n\n    def __init__(self):\n        \"\"\"初始化主窗口\"\"\"\n        super().__init__()\n\n        # 设置全局引用\n        app_utils.set_main_window(self)\n        QApplication.instance().mainWindow = self\n\n        # 设置窗口属性\n        self.setWindowTitle(\"Management Tool\")\n        self.setMinimumSize(800, 600)\n\n        # 设置全局样式表\n        self.setStyleSheet(\"\"\"\n            QMainWindow {\n                background-color: #f5f5f5;\n                color: #333333;\n            }\n            QWidget {\n                color: #333333;\n            }\n            QTabWidget::pane {\n                border: 1px solid #cccccc;\n                background: white;\n                border-radius: 4px;\n            }\n            QTabBar::tab {\n                background: #e0e0e0;\n                border: 1px solid #cccccc;\n                padding: 6px 12px;\n                border-top-left-radius: 4px;\n                border-top-right-radius: 4px;\n                margin-right: 2px;\n                color: #333333;\n            }\n            QTabBar::tab:selected {\n                background: white;\n                border-bottom-color: white;\n                color: #333333;\n            }\n            QGroupBox {\n                border: 1px solid #cccccc;\n                border-radius: 4px;\n                margin-top: 12px;\n                font-weight: bold;\n                background-color: white;\n                color: #333333;\n            }\n            QGroupBox::title {\n                subcontrol-origin: margin;\n                left: 10px;\n                padding: 0 3px;\n                color: #333333;\n            }\n            QPushButton {\n                background-color: #4285f4;\n                color: white;\n                border: none;\n                border-radius: 4px;\n                padding: 6px 10px;\n                font-weight: bold;\n            }\n            QPushButton:hover {\n                background-color: #3275e4;\n            }\n            QPushButton:pressed {\n                background-color: #2265d4;\n            }\n            QPushButton:disabled {\n                background-color: #cccccc;\n                color: #666666;\n            }\n            QLineEdit {\n                border: 1px solid #cccccc;\n                border-radius: 3px;\n                padding: 4px;\n                background-color: white;\n                color: #333333;\n            }\n            QTextEdit {\n                border: 1px solid #cccccc;\n                border-radius: 3px;\n                background-color: white;\n                color: #333333;\n            }\n            QTableView {\n                border: 1px solid #cccccc;\n                border-radius: 3px;\n                background-color: white;\n                alternate-background-color: #f5f5f5;\n                color: #333333;\n                gridline-color: #e0e0e0;\n            }\n            QTableView::item {\n                border-bottom: 1px solid #f0f0f0;\n                color: #333333;\n                padding: 4px;\n            }\n            QHeaderView::section {\n                background-color: #e0e0e0;\n                padding: 4px;\n                border: 1px solid #cccccc;\n                font-weight: bold;\n                color: #333333;\n            }\n            QLabel {\n                color: #333333;\n            }\n            QCheckBox {\n                color: #333333;\n            }\n            QCheckBox::indicator {\n                width: 16px;\n                height: 16px;\n                border: 2px solid #bbbbbb;\n                border-radius: 3px;\n                background-color: white;\n            }\n            QCheckBox::indicator:checked {\n                border: 2px solid #4CAF50;\n                border-radius: 3px;\n                background-color: #4CAF50;\n                image: url(resources/check.png);\n            }\n            QCheckBox::indicator:hover {\n                border: 2px solid #4285f4;\n            }\n            QComboBox {\n                border: 1px solid #cccccc;\n                border-radius: 3px;\n                padding: 4px;\n                background-color: white;\n                color: #333333;\n            }\n            QComboBox QAbstractItemView {\n                background-color: white;\n                color: #333333;\n                selection-background-color: #4285f4;\n                selection-color: white;\n                border: 1px solid #cccccc;\n            }\n            QMenu {\n                background-color: white;\n                color: #333333;\n                border: 1px solid #cccccc;\n            }\n            QMenu::item {\n                padding: 6px 24px;\n                color: #333333;\n            }\n            QMenu::item:selected {\n                background-color: #4285f4;\n                color: white;\n            }\n            QStatusBar {\n                background-color: #f5f5f5;\n                color: #333333;\n                border-top: 1px solid #cccccc;\n            }\n            QToolTip {\n                background-color: #ffffcc;\n                color: #333333;\n                border: 1px solid #e0e0e0;\n            }\n            QMessageBox {\n                background-color: white;\n                color: #333333;\n            }\n            QMessageBox QLabel {\n                color: #333333;\n                background-color: transparent;\n            }\n            QMessageBox QPushButton {\n                color: white;\n                background-color: #4285f4;\n                border: none;\n                border-radius: 4px;\n                padding: 6px 12px;\n                font-weight: 600;\n                font-size: 12px;\n            }\n            QMessageBox QPushButton:hover {\n                background-color: #3275e4;\n            }\n        \"\"\")\n\n        # 创建中央部件\n        self.central_widget = QWidget()\n        self.setCentralWidget(self.central_widget)\n\n        # 创建主布局\n        self.main_layout = QVBoxLayout(self.central_widget)\n        self.main_layout.setContentsMargins(10, 10, 10, 10)\n        self.main_layout.setSpacing(6)\n\n        # 创建标签页控件\n        self.tab_widget = QTabWidget()\n        self.main_layout.addWidget(self.tab_widget)\n\n        # 创建注册标签页（改名为\"Cursor主页\"）\n        self.registration_widget = RegistrationWidget()\n        self.tab_widget.addTab(self.registration_widget, \"Cursor主页\")\n\n        # 创建账号管理标签页（改名为\"Cursor账号管理\"）\n        self.account_manager_widget = AccountManagerWidget(self)\n        self.tab_widget.addTab(self.account_manager_widget, \"Cursor账号管理\")\n\n        # 创建Windsurf主页标签页\n        self.windsurf_registration_widget = WindsurfRegistrationWidget(self)\n        self.tab_widget.addTab(self.windsurf_registration_widget, \"Windsurf主页\")\n\n        # 创建Windsurf账号管理标签页\n        self.windsurf_account_manager_widget = WindsurfAccountManagerWidget(self)\n        self.tab_widget.addTab(self.windsurf_account_manager_widget, \"Windsurf账号管理\")\n\n        # 创建Augment工具标签页\n        self.augment_tools_widget = AugmentToolsWidget(self)\n        self.tab_widget.addTab(self.augment_tools_widget, \"Augment工具\")\n\n        # 创建关于标签页\n        self.about_widget = self.create_about_widget()\n        self.tab_widget.addTab(self.about_widget, \"关于\")\n\n        # 状态栏\n        self.statusBar = QStatusBar()\n        self.setStatusBar(self.statusBar)\n        self.statusBar.showMessage(\"就绪\")\n\n        # 设置日志回调\n        self.log_handler = LogHandler(self.registration_widget.log_text)\n        set_print_callback(self.handle_log)\n\n        # 显示初始信息\n        info(\"Management Tool已启动\")\n\n    def handle_log(self, log_type: str, message: str):\n        \"\"\"处理日志回调\n\n        Args:\n            log_type: 日志类型\n            message: 日志消息\n        \"\"\"\n        # 将日志处理委托给日志处理器\n        QApplication.postEvent(self, LogEvent(log_type, message))\n\n    def create_about_widget(self):\n        \"\"\"创建关于标签页\"\"\"\n        widget = QWidget()\n        main_layout = QVBoxLayout(widget)\n        main_layout.setContentsMargins(15, 15, 15, 15)\n        main_layout.setSpacing(8)\n\n        # 软件标题\n        title_label = QLabel(\"AI注册工具集\")\n        title_label.setStyleSheet(\"font-size: 22px; font-weight: bold; color: #333; margin-bottom: 5px;\")\n        title_label.setAlignment(Qt.AlignCenter)\n        main_layout.addWidget(title_label)\n\n        # 版本信息\n        version_label = QLabel(\"版本 4.0\")\n        version_label.setStyleSheet(\"font-size: 13px; color: #666; margin-bottom: 5px;\")\n        version_label.setAlignment(Qt.AlignCenter)\n        main_layout.addWidget(version_label)\n\n        # 更新时间\n        update_label = QLabel(\"更新时间：2025/6/16\")\n        update_label.setStyleSheet(\"font-size: 11px; color: #888; margin-bottom: 10px;\")\n        update_label.setAlignment(Qt.AlignCenter)\n        main_layout.addWidget(update_label)\n\n        # 软件功能介绍\n        features_frame = QGroupBox(\"🚀 工具集功能\")\n        features_layout = QVBoxLayout(features_frame)\n        features_layout.setContentsMargins(10, 8, 10, 8)\n        features_layout.setSpacing(5)\n\n        features_text = QLabel(\"\"\"🎯 Cursor 快速注册：一键自动化注册流程，从邮箱生成到账号激活全自动完成\n👥 Cursor 账号管理：安全存储和批量管理多个账号，支持登录验证和状态检查\n🌊 Windsurf 快速注册：全自动Windsurf账号注册，支持人机验证和邮箱验证码\n🏄 Windsurf 账号管理：专业的Windsurf账号管理，支持Token管理和批量操作\n🧹 Windsurf 一键重置：自动关闭进程并清理用户数据，重置机器ID获得新环境\n🔧 Cursor 重置工具：彻底清除设备标识信息，重置机器码获得新的试用期\n⚡ Augment 重置工具：一键重置VSCode扩展，支持邮箱生成和验证码获取\n📧 智能邮箱服务：支持自动生成和手动输入，自动添加域名配置\n🛡️ 隐私保护技术：使用密码学安全算法，支持60+种设备ID字段重置\"\"\")\n        features_text.setStyleSheet(\"font-size: 11px; color: #555; line-height: 1.3; padding: 5px;\")\n        features_text.setWordWrap(True)\n        features_layout.addWidget(features_text)\n\n        main_layout.addWidget(features_frame)\n\n        # 技术特性\n        tech_frame = QGroupBox(\"💻 技术特性\")\n        tech_layout = QVBoxLayout(tech_frame)\n        tech_layout.setContentsMargins(10, 8, 10, 8)\n        tech_layout.setSpacing(5)\n\n        tech_text = QLabel(\"\"\"🔒 安全性：密码学安全随机数生成器 • 本地数据加密存储 • 自动备份重要文件\n🎯 智能化：自动检测软件路径 • 智能错误处理重试 • 实时日志状态反馈\n🔧 兼容性：支持Windows 10/11 • 兼容所有Cursor版本 • 支持VSCode和Augment扩展\n⚡ 高效性：多标签页界面设计 • 批量操作支持 • 内存优化运行流畅\"\"\")\n        tech_text.setStyleSheet(\"font-size: 11px; color: #555; line-height: 1.3; padding: 5px;\")\n        tech_text.setWordWrap(True)\n        tech_layout.addWidget(tech_text)\n\n        main_layout.addWidget(tech_frame)\n\n        # 使用指南\n        guide_frame = QGroupBox(\"📖 使用指南\")\n        guide_layout = QVBoxLayout(guide_frame)\n        guide_layout.setContentsMargins(10, 8, 10, 8)\n        guide_layout.setSpacing(5)\n\n        guide_text = QLabel(\"\"\"🚀 快速开始：选择对应标签页使用功能 • Cursor主页注册账号 • Windsurf主页注册Windsurf • 账号管理查看信息 • Augment工具重置扩展\n⚠️ 注意事项：确保网络连接正常 • 重置前关闭相关软件 • 操作后重启软件生效 • 定期备份账号信息\n🔧 配置建议：配置多个邮箱服务提高成功率 • 使用手动输入自定义邮箱前缀 • 定期清理日志节省空间\"\"\")\n        guide_text.setStyleSheet(\"font-size: 11px; color: #555; line-height: 1.3; padding: 5px;\")\n        guide_text.setWordWrap(True)\n        guide_layout.addWidget(guide_text)\n\n        main_layout.addWidget(guide_frame)\n\n        # 开发信息\n        dev_frame = QGroupBox(\"开发信息\")\n        dev_layout = QVBoxLayout(dev_frame)\n        dev_layout.setContentsMargins(10, 8, 10, 8)\n        dev_layout.setSpacing(5)\n\n        dev_text = QLabel(\"\"\"👨‍💻 开发者：XIAOFU  📅 更新时间：2025年6月16日  🔧 技术栈：Python + PySide6\n⚠️ 使用须知：确保网络连接正常 • 注册过程中勿关闭软件 • 重置操作自动备份 • 操作后重启软件生效 • 仅用于合法学习研究\n📞 技术支持：查看日志信息 • 自动错误检测修复 • 详细操作指导帮助\"\"\")\n        dev_text.setStyleSheet(\"font-size: 11px; color: #555; line-height: 1.3; padding: 5px;\")\n        dev_text.setWordWrap(True)\n        dev_layout.addWidget(dev_text)\n\n        main_layout.addWidget(dev_frame)\n\n        # 添加弹性空间\n        main_layout.addStretch()\n\n        return widget\n\n    def event(self, event):\n        \"\"\"事件处理\n\n        处理自定义事件\n        \"\"\"\n        if isinstance(event, LogEvent):\n            self.log_handler.handle_log(event.log_type, event.message)\n            return True\n        elif isinstance(event, RefreshAccountsEvent):\n            self.account_manager_widget.load_accounts()\n            # 同时刷新Windsurf账号管理\n            self.windsurf_account_manager_widget.load_accounts()\n            return True\n        elif isinstance(event, RestoreButtonEvent):\n            # 恢复注册按钮状态\n            info(\"恢复注册按钮状态\")\n            self.registration_widget.register_btn.setEnabled(True)\n            self.registration_widget.register_btn.setText(\"快速注册\")\n            app_utils.update_status(\"注册完成\")\n            return True\n        elif isinstance(event, RestoreWindsurfButtonEvent):\n            # 恢复Windsurf注册按钮状态\n            info(\"恢复Windsurf注册按钮状态\")\n            self.windsurf_registration_widget.register_btn.setEnabled(True)\n            self.windsurf_registration_widget.register_btn.setText(\"快速注册\")\n            app_utils.update_status(\"Windsurf注册完成\")\n            return True\n        elif isinstance(event, RestoreWindsurfResetButtonEvent):\n            # 恢复Windsurf重置按钮状态\n            info(\"恢复Windsurf重置按钮状态\")\n            self.windsurf_registration_widget.reset_btn.setEnabled(True)\n            self.windsurf_registration_widget.reset_btn.setText(\"一键清理重置\")\n            app_utils.update_status(\"Windsurf重置完成\")\n            return True\n        elif isinstance(event, RestoreFullProcessButtonsEvent):\n            # 恢复所有按钮状态\n            info(\"恢复所有按钮状态\")\n            self.registration_widget.full_process_btn.setEnabled(True)\n            self.registration_widget.register_btn.setEnabled(True)\n            self.registration_widget.reset_id_btn.setEnabled(True)\n            self.registration_widget.full_process_btn.setText(\"完整流程\")\n            app_utils.update_status(\"完整流程已完成\")\n            return True\n        return super().event(event)\n\n\n# 自定义事件类型\nclass LogEvent(QEvent):\n    \"\"\"日志事件，用于在线程间安全地传递日志消息\"\"\"\n\n    EVENT_TYPE = QEvent.Type(QEvent.registerEventType())\n\n    def __init__(self, log_type: str, message: str):\n        \"\"\"初始化日志事件\n\n        Args:\n            log_type: 日志类型\n            message: 日志消息\n        \"\"\"\n        super().__init__(self.EVENT_TYPE)\n        self.log_type = log_type\n        self.message = message\n\nclass RestoreButtonEvent(QEvent):\n    \"\"\"恢复按钮状态事件\"\"\"\n\n    EVENT_TYPE = QEvent.Type(QEvent.registerEventType())\n\n    def __init__(self):\n        \"\"\"初始化恢复按钮状态事件\"\"\"\n        super().__init__(self.EVENT_TYPE)\n\nclass RefreshAccountsEvent(QEvent):\n    \"\"\"刷新账号列表事件\"\"\"\n\n    EVENT_TYPE = QEvent.Type(QEvent.registerEventType())\n\n    def __init__(self):\n        \"\"\"初始化刷新账号列表事件\"\"\"\n        super().__init__(self.EVENT_TYPE)\n\n\ndef main():\n    \"\"\"主函数\"\"\"\n    # 创建应用程序\n    app = QApplication(sys.argv)\n\n    # 设置应用程序样式\n    app.setStyle(\"Fusion\")\n\n    # 创建并显示主窗口\n    window = MainWindow()\n    window.show()\n\n    # 运行应用程序事件循环\n    sys.exit(app.exec())\n\n\nif __name__ == \"__main__\":\n    main()", "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nCursor工具主界面\n\n提供图形用户界面，包含邮箱配置、快速注册功能、账号管理和日志显示区域。\n使用标签页布局组织不同功能模块。\n\n模块化重构版本 v4.0 - 保持向后兼容\n\"\"\"\n\nimport os\nimport sys\nimport time\nimport threading\nimport random\nimport subprocess\nfrom pathlib import Path\nfrom typing import Optional, List, Dict, Any\n\nfrom PySide6.QtCore import Qt, QSize, Signal, Slot, QTimer, QEvent\nfrom PySide6.QtGui import QIcon, QFont, QTextCursor, QAction, QColor, QIntValidator\nfrom PySide6.QtWidgets import (\n    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,\n    QPushButton, QLabel, QLineEdit, QTextEdit, QGroupBox,\n    QFormLayout, QComboBox, QCheckBox, QMessageBox, QFileDialog,\n    QTabWidget, QSplitter, QToolBar, QStatusBar, QDialog\n)\n\n# 尝试导入新的模块化组件\ntry:\n    # 添加src目录到Python路径\n    src_path = Path(__file__).parent / \"src\"\n    if str(src_path) not in sys.path:\n        sys.path.insert(0, str(src_path))\n\n    # 导入新的模块化组件\n    from src.ui.main_window import MainWindow as NewMainWindow, create_application\n    USE_NEW_UI = True\n    print(\"使用新的模块化UI界面\")\nexcept ImportError as e:\n    print(f\"新模块化组件导入失败，使用原始界面: {e}\")\n    USE_NEW_UI = False\n\n# 导入原始项目模块\nfrom config import Config\nfrom custom_print import set_print_callback, info, error, warning, debug\nfrom cursor_quick_register import quick_signup_process\nimport app_utils\nfrom account_manager import AccountManagerWidget\nfrom augment_tools_widget import AugmentToolsWidget\nfrom windsurf_registration_widget import WindsurfRegistrationWidget, RestoreWindsurfButtonEvent, RestoreWindsurfResetButtonEvent\nfrom windsurf_account_manager import WindsurfAccountManagerWidget\n\n# 辅助函数：创建带样式的消息框\ndef create_styled_message_box(parent, icon, title, text, buttons=QMessageBox.Ok, default_button=QMessageBox.Ok):\n    \"\"\"创建带样式的消息框\n\n    Args:\n        parent: 父窗口\n        icon: 消息框图标类型\n        title: 标题\n        text: 消息内容\n        buttons: 按钮组合\n        default_button: 默认按钮\n\n    Returns:\n        消息框对象\n    \"\"\"\n    msg_box = QMessageBox(icon, title, text, buttons, parent)\n    msg_box.setDefaultButton(default_button)\n    msg_box.setStyleSheet(\"\"\"\n        QMessageBox {\n            background-color: white;\n            color: #333333;\n        }\n        QMessageBox QLabel {\n            color: #333333;\n            background-color: transparent;\n        }\n        QPushButton {\n            color: white;\n            background-color: #4285f4;\n            border: none;\n            border-radius: 4px;\n            padding: 6px 12px;\n            font-weight: 600;\n            font-size: 12px;\n        }\n        QPushButton:hover {\n            background-color: #3275e4;\n        }\n    \"\"\")\n    return msg_box\n\nclass LogHandler:\n    \"\"\"日志处理器，用于将日志输出到GUI\"\"\"\n\n    def __init__(self, text_edit: QTextEdit):\n        \"\"\"初始化日志处理器\n\n        Args:\n            text_edit: 用于显示日志的QTextEdit控件\n        \"\"\"\n        self.text_edit = text_edit\n\n    def handle_log(self, log_type: str, message: str):\n        \"\"\"处理日志消息\n\n        Args:\n            log_type: 日志类型，如'info', 'error', 'warning', 'debug'\n            message: 日志消息内容\n        \"\"\"\n        # 根据日志类型设置不同颜色，确保颜色足够深，便于查看\n        color_map = {\n            'info': '#000000',    # 黑色\n            'error': '#FF0000',   # 红色\n            'warning': '#FF6600', # 橙色\n            'debug': '#0000FF'    # 蓝色\n        }\n        color = color_map.get(log_type, '#000000')\n\n        # 获取当前时间\n        timestamp = time.strftime('%H:%M:%S')\n\n        # 格式化日志消息，确保使用HTML格式\n        formatted_message = f\"<span style='color:{color}; font-weight:normal;'>[{timestamp}] [{log_type.upper()}] {message}</span>\"\n\n        # 添加到文本框并滚动到底部\n        self.text_edit.append(formatted_message)\n        self.text_edit.moveCursor(QTextCursor.End)\n\n\nclass RegistrationWidget(QWidget):\n    \"\"\"注册功能标签页\"\"\"\n\n    def __init__(self, parent=None):\n        \"\"\"初始化注册标签页\"\"\"\n        super().__init__(parent)\n\n        # 加载配置\n        self.config = Config()\n\n        # 创建主布局\n        self.main_layout = QVBoxLayout(self)\n        self.main_layout.setContentsMargins(8, 8, 8, 8)\n        self.main_layout.setSpacing(6)\n\n        # 创建配置区域\n        self.create_config_section()\n\n        # 创建按钮区域\n        self.create_button_section()\n\n        # 创建日志区域\n        self.create_log_section()\n\n        # 加载配置到UI\n        self.load_config_to_ui()\n\n    def create_config_section(self):\n        \"\"\"创建配置区域\"\"\"\n        config_group = QGroupBox(\"邮箱配置\")\n        config_layout = QFormLayout()\n        config_layout.setSpacing(4)\n        config_layout.setContentsMargins(8, 8, 8, 8)\n\n        # 配置选择和管理\n        config_selection_layout = QHBoxLayout()\n        config_selection_layout.setSpacing(6)\n\n        # 配置选择下拉框\n        self.config_combo = QComboBox()\n        self.config_combo.currentIndexChanged.connect(self.on_config_selected)\n        config_selection_layout.addWidget(self.config_combo, 2)\n\n        # 配置管理按钮\n        manage_config_btn = QPushButton(\"管理配置\")\n        manage_config_btn.clicked.connect(self.manage_configs)\n        config_selection_layout.addWidget(manage_config_btn)\n\n        config_layout.addRow(\"当前配置:\", config_selection_layout)\n\n        # 不显示任何额外信息，保持界面简洁\n\n        # 循环注册次数\n        loop_layout = QHBoxLayout()\n        loop_layout.setSpacing(6)\n\n        self.loop_count_input = QLineEdit(\"1\")\n        self.loop_count_input.setFixedWidth(60)\n        # 只允许输入数字\n        self.loop_count_input.setValidator(QIntValidator(1, 100))\n        loop_layout.addWidget(self.loop_count_input)\n\n        loop_label = QLabel(\"次 (最多100次)\")\n        loop_layout.addWidget(loop_label)\n\n        # 添加随机配置选项\n        self.random_config_checkbox = QCheckBox(\"随机配置\")\n        self.random_config_checkbox.setToolTip(\"启用后，每次循环将随机选择一个配置进行注册\")\n        # 只有当循环次数大于1时才启用\n        self.random_config_checkbox.setEnabled(False)\n        # 连接循环次数变化信号\n        self.loop_count_input.textChanged.connect(self.update_random_config_state)\n        loop_layout.addWidget(self.random_config_checkbox)\n\n        loop_layout.addStretch()\n\n        config_layout.addRow(\"循环注册:\", loop_layout)\n\n        # Cursor路径设置\n        cursor_path_layout = QHBoxLayout()\n        cursor_path_layout.setSpacing(6)\n\n        # 显示当前Cursor路径\n        self.cursor_path_label = QLabel(\"自动检测\")\n        self.cursor_path_label.setStyleSheet(\"color: #666666; font-size: 10pt;\")\n        self.cursor_path_label.setWordWrap(True)\n        cursor_path_layout.addWidget(self.cursor_path_label, 2)\n\n        # 浏览按钮\n        browse_cursor_btn = QPushButton(\"浏览\")\n        browse_cursor_btn.setFixedWidth(60)\n        browse_cursor_btn.clicked.connect(self.browse_cursor_path)\n        cursor_path_layout.addWidget(browse_cursor_btn)\n\n        # 清除按钮\n        clear_cursor_btn = QPushButton(\"清除\")\n        clear_cursor_btn.setFixedWidth(60)\n        clear_cursor_btn.clicked.connect(self.clear_cursor_path)\n        cursor_path_layout.addWidget(clear_cursor_btn)\n\n        config_layout.addRow(\"Cursor路径:\", cursor_path_layout)\n\n        # 不再需要浏览器配置勾选框，因为已经在配置中设置了\n\n        config_group.setLayout(config_layout)\n        self.main_layout.addWidget(config_group)\n\n        # 加载配置列表\n        self.load_config_list()\n\n    def create_button_section(self):\n        \"\"\"创建按钮区域\"\"\"\n        button_layout = QHBoxLayout()\n        button_layout.setSpacing(8)\n\n        # 重置ID按钮\n        self.reset_id_btn = QPushButton(\"重置ID码\")\n        self.reset_id_btn.setMinimumHeight(36)\n        self.reset_id_btn.clicked.connect(self.reset_id)\n        button_layout.addWidget(self.reset_id_btn)\n\n        # 快速注册按钮\n        self.register_btn = QPushButton(\"快速注册\")\n        self.register_btn.setMinimumHeight(36)\n        self.register_btn.clicked.connect(self.start_registration)\n        button_layout.addWidget(self.register_btn)\n\n        # 完整流程按钮\n        self.full_process_btn = QPushButton(\"完整流程\")\n        self.full_process_btn.setMinimumHeight(36)\n        self.full_process_btn.clicked.connect(self.start_full_process)\n        button_layout.addWidget(self.full_process_btn)\n\n        self.main_layout.addLayout(button_layout)\n\n    def create_log_section(self):\n        \"\"\"创建日志区域\"\"\"\n        log_group = QGroupBox(\"日志\")\n        log_layout = QVBoxLayout()\n        log_layout.setContentsMargins(8, 8, 8, 8)\n        log_layout.setSpacing(4)\n\n        # 日志文本框\n        self.log_text = QTextEdit()\n        self.log_text.setReadOnly(True)\n        self.log_text.setLineWrapMode(QTextEdit.WidgetWidth)\n        self.log_text.setStyleSheet(\"\"\"\n            font-family: Consolas, monospace;\n            font-size: 10pt;\n            color: black;\n            background-color: white;\n            border: 1px solid #cccccc;\n            padding: 4px;\n        \"\"\")\n        log_layout.addWidget(self.log_text)\n\n        # 在初始化时放入一条测试信息\n        self.log_text.append(\"<span style='color:#0000FF; font-weight:normal;'>[测试] 日志区域已初始化，文本应该可见</span>\")\n\n        # 清空日志按钮\n        clear_log_btn = QPushButton(\"清空日志\")\n        clear_log_btn.clicked.connect(self.clear_log)\n        log_layout.addWidget(clear_log_btn)\n\n        log_group.setLayout(log_layout)\n        self.main_layout.addWidget(log_group, 1)  # 1表示拉伸因子，让日志区域占据更多空间\n\n    def load_config_list(self):\n        \"\"\"加载配置列表到下拉框\"\"\"\n        try:\n            # 清空下拉框\n            self.config_combo.clear()\n\n            # 获取配置列表\n            config_list = self.config.get_config_list()\n\n            # 添加到下拉框\n            for i, config_data in enumerate(config_list):\n                # 获取配置名称\n                temp_mail = config_data.get(\"TEMP_MAIL\", \"\")\n                domain = config_data.get(\"DOMAIN\", \"\")\n\n                # 创建显示文本\n                display_text = f\"配置 {i+1}: {temp_mail} - {domain}\"\n\n                # 添加到下拉框\n                self.config_combo.addItem(display_text, i)\n\n            # 选择当前配置\n            current_index = self.config.get_current_config_index()\n            if current_index < self.config_combo.count():\n                self.config_combo.setCurrentIndex(current_index)\n\n            info(f\"已加载 {self.config_combo.count()} 个配置\")\n        except Exception as e:\n            error(f\"加载配置列表失败: {e}\")\n\n    def on_config_selected(self, index):\n        \"\"\"配置选择事件处理\n\n        Args:\n            index: 选中的配置索引\n        \"\"\"\n        if index < 0:\n            return\n\n        try:\n            # 切换到选中的配置\n            if self.config.switch_config(index):\n                info(f\"已切换到配置 {index}\")\n\n                # 更新UI\n                self.load_config_to_ui()\n\n                app_utils.update_status(f\"已切换到配置 {index+1}\")\n            else:\n                error(f\"切换配置失败\")\n        except Exception as e:\n            error(f\"切换配置失败: {e}\")\n\n    def manage_configs(self):\n        \"\"\"打开配置管理对话框\"\"\"\n        try:\n            # 导入配置管理对话框\n            from config_manager_dialog import ConfigManagerDialog\n\n            # 创建对话框\n            dialog = ConfigManagerDialog(self.config, self)\n\n            # 连接配置变更信号\n            dialog.config_changed.connect(self.on_config_changed)\n\n            # 显示对话框\n            dialog.exec()\n        except Exception as e:\n            error(f\"打开配置管理对话框失败: {e}\")\n            msg_box = create_styled_message_box(self, QMessageBox.Critical, \"错误\", f\"打开配置管理对话框失败: {e}\")\n            msg_box.exec()\n\n    def on_config_changed(self):\n        \"\"\"配置变更事件处理\"\"\"\n        try:\n            # 重新加载配置列表\n            self.load_config_list()\n\n            # 更新UI\n            self.load_config_to_ui()\n\n            # 更新随机配置复选框状态\n            self.update_random_config_state()\n\n            info(\"配置已更新\")\n        except Exception as e:\n            error(f\"更新配置失败: {e}\")\n\n    def update_random_config_state(self):\n        \"\"\"更新随机配置复选框状态\"\"\"\n        try:\n            # 获取循环次数\n            try:\n                loop_count = int(self.loop_count_input.text())\n            except ValueError:\n                loop_count = 1\n\n            # 获取配置数量\n            config_count = self.config.get_config_count()\n\n            # 只有当循环次数大于1且配置数量大于1时才启用随机配置选项\n            self.random_config_checkbox.setEnabled(loop_count > 1 and config_count > 1)\n\n            # 如果不满足条件，取消选中\n            if not (loop_count > 1 and config_count > 1):\n                self.random_config_checkbox.setChecked(False)\n        except Exception as e:\n            error(f\"更新随机配置状态失败: {e}\")\n\n    # 移除 update_account_info 方法，因为不再需要它\n\n    def load_config_to_ui(self):\n        \"\"\"将配置加载到UI控件\"\"\"\n        try:\n            # 更新Cursor路径显示\n            self.update_cursor_path_display()\n            info(\"配置已加载到界面\")\n        except Exception as e:\n            error(f\"加载配置失败: {e}\")\n\n    def update_cursor_path_display(self):\n        \"\"\"更新Cursor路径显示\"\"\"\n        try:\n            cursor_path = self.config.get_cursor_manual_path()\n            if cursor_path:\n                # 显示手动设置的路径，截断过长的路径\n                if len(cursor_path) > 50:\n                    display_path = \"...\" + cursor_path[-47:]\n                else:\n                    display_path = cursor_path\n                self.cursor_path_label.setText(display_path)\n                self.cursor_path_label.setToolTip(f\"手动设置: {cursor_path}\")\n                self.cursor_path_label.setStyleSheet(\"color: #2e7d32; font-size: 10pt;\")\n            else:\n                self.cursor_path_label.setText(\"自动检测\")\n                self.cursor_path_label.setToolTip(\"将自动检测Cursor安装位置\")\n                self.cursor_path_label.setStyleSheet(\"color: #666666; font-size: 10pt;\")\n        except Exception as e:\n            error(f\"更新Cursor路径显示失败: {e}\")\n\n    def browse_cursor_path(self):\n        \"\"\"浏览选择Cursor.exe路径\"\"\"\n        try:\n            # 打开文件选择对话框\n            file_path, _ = QFileDialog.getOpenFileName(\n                self,\n                \"选择Cursor.exe文件\",\n                \"\",\n                \"可执行文件 (*.exe);;所有文件 (*.*)\"\n            )\n\n            if file_path:\n                # 验证选择的文件\n                if not file_path.lower().endswith(\"cursor.exe\"):\n                    msg_box = create_styled_message_box(\n                        self,\n                        QMessageBox.Warning,\n                        \"警告\",\n                        \"请选择Cursor.exe文件\"\n                    )\n                    msg_box.exec()\n                    return\n\n                # 设置路径\n                if self.config.set_cursor_manual_path(file_path):\n                    self.update_cursor_path_display()\n                    info(f\"已设置Cursor路径: {file_path}\")\n                    msg_box = create_styled_message_box(\n                        self,\n                        QMessageBox.Information,\n                        \"成功\",\n                        f\"已设置Cursor路径:\\n{file_path}\"\n                    )\n                    msg_box.exec()\n                else:\n                    msg_box = create_styled_message_box(\n                        self,\n                        QMessageBox.Critical,\n                        \"错误\",\n                        \"设置Cursor路径失败\"\n                    )\n                    msg_box.exec()\n        except Exception as e:\n            error(f\"浏览Cursor路径时出错: {e}\")\n            msg_box = create_styled_message_box(\n                self,\n                QMessageBox.Critical,\n                \"错误\",\n                f\"浏览Cursor路径时出错: {e}\"\n            )\n            msg_box.exec()\n\n    def clear_cursor_path(self):\n        \"\"\"清除手动设置的Cursor路径\"\"\"\n        try:\n            # 确认对话框\n            msg_box = create_styled_message_box(\n                self,\n                QMessageBox.Question,\n                \"确认\",\n                \"确定要清除手动设置的Cursor路径吗？\\n清除后将使用自动检测。\",\n                QMessageBox.Yes | QMessageBox.No,\n                QMessageBox.No\n            )\n\n            if msg_box.exec() == QMessageBox.Yes:\n                if self.config.set_cursor_manual_path(\"\"):\n                    self.update_cursor_path_display()\n                    info(\"已清除Cursor手动路径设置\")\n                    msg_box = create_styled_message_box(\n                        self,\n                        QMessageBox.Information,\n                        \"成功\",\n                        \"已清除Cursor路径设置，将使用自动检测\"\n                    )\n                    msg_box.exec()\n                else:\n                    msg_box = create_styled_message_box(\n                        self,\n                        QMessageBox.Critical,\n                        \"错误\",\n                        \"清除Cursor路径设置失败\"\n                    )\n                    msg_box.exec()\n        except Exception as e:\n            error(f\"清除Cursor路径时出错: {e}\")\n            msg_box = create_styled_message_box(\n                self,\n                QMessageBox.Critical,\n                \"错误\",\n                f\"清除Cursor路径时出错: {e}\"\n            )\n            msg_box.exec()\n\n    def save_config(self):\n        \"\"\"保存当前配置\"\"\"\n        try:\n            # 保存到文件\n            if self.config.save_config():\n                info(\"配置已保存\")\n                app_utils.update_status(\"配置已保存\")\n\n                # 重新加载配置列表\n                self.load_config_list()\n\n                info(\"配置已立即生效\")\n            else:\n                error(\"保存配置失败\")\n                app_utils.update_status(\"保存配置失败\", 3000)\n        except Exception as e:\n            error(f\"保存配置时发生错误: {e}\")\n            app_utils.update_status(f\"保存配置时发生错误: {e}\", 3000)\n\n    def reset_id(self):\n        \"\"\"启动重置ID工具\"\"\"\n        try:\n            # 导入重置工具模块\n            from combined_reset_gui import ResetThread\n\n            # 禁用所有按钮\n            self.reset_id_btn.setEnabled(False)\n            self.register_btn.setEnabled(False)\n            self.full_process_btn.setEnabled(False)\n            self.reset_id_btn.setText(\"重置中...\")\n            app_utils.update_status(\"正在执行重置ID操作...\")\n\n            # 清空日志\n            self.log_text.clear()\n\n            # 创建并启动线程\n            self.reset_thread = ResetThread(\"all\")\n            self.reset_thread.update_log.connect(lambda msg: info(f\"【重置ID】{msg}\"))\n            self.reset_thread.update_progress.connect(lambda value: app_utils.update_status(f\"重置ID进度: {value}%\"))\n            self.reset_thread.finished_signal.connect(self.reset_id_finished)\n            self.reset_thread.start()\n\n            info(\"已启动重置ID操作\")\n        except Exception as e:\n            error(f\"启动重置ID操作时发生错误: {e}\")\n            msg_box = create_styled_message_box(self, QMessageBox.Critical, \"错误\", f\"启动重置ID操作时发生错误: {e}\")\n            msg_box.exec_()\n            # 恢复按钮状态\n            self.reset_id_btn.setEnabled(True)\n            self.register_btn.setEnabled(True)\n            self.full_process_btn.setEnabled(True)\n            self.reset_id_btn.setText(\"重置ID码\")\n\n    def reset_id_finished(self, success, message):\n        \"\"\"重置ID操作完成的回调\"\"\"\n        # 恢复按钮状态\n        self.reset_id_btn.setEnabled(True)\n        self.register_btn.setEnabled(True)\n        self.full_process_btn.setEnabled(True)\n        self.reset_id_btn.setText(\"重置ID码\")\n\n        # 显示结果\n        if success:\n            info(f\"【重置ID】成功: {message}\")\n            app_utils.update_status(\"重置ID成功完成\")\n            msg_box = create_styled_message_box(self, QMessageBox.Information, \"成功\", f\"重置ID成功: {message}\")\n            msg_box.exec_()\n        else:\n            error(f\"【重置ID】失败: {message}\")\n            app_utils.update_status(\"重置ID失败\", 3000)\n            msg_box = create_styled_message_box(self, QMessageBox.Warning, \"失败\", f\"重置ID失败: {message}\")\n            msg_box.exec_()\n\n    def start_registration(self):\n        \"\"\"开始注册过程\"\"\"\n        # 获取循环次数\n        try:\n            loop_count = int(self.loop_count_input.text())\n            if loop_count < 1:\n                loop_count = 1\n            elif loop_count > 100:\n                loop_count = 100\n        except ValueError:\n            loop_count = 1\n            self.loop_count_input.setText(\"1\")\n\n        # 禁用注册按钮，防止重复点击\n        self.register_btn.setEnabled(False)\n        if loop_count > 1:\n            self.register_btn.setText(f\"注册中({loop_count}次)...\")\n        else:\n            self.register_btn.setText(\"注册中...\")\n        app_utils.update_status(f\"正在进行注册，计划循环{loop_count}次...\")\n\n        # 创建线程执行注册过程\n        threading.Thread(target=self._run_registration, daemon=True).start()\n\n    def start_full_process(self):\n        \"\"\"启动完整流程（重置ID、注册、切换账号）\"\"\"\n        # 禁用所有按钮\n        self.full_process_btn.setEnabled(False)\n        self.register_btn.setEnabled(False)\n        self.reset_id_btn.setEnabled(False)\n        self.full_process_btn.setText(\"处理中...\")\n        app_utils.update_status(\"正在执行完整流程...\")\n\n        # 创建线程执行完整流程\n        threading.Thread(target=self._run_full_process, daemon=True).start()\n\n    def _run_full_process(self):\n        \"\"\"在线程中运行完整流程\"\"\"\n        browser_manager = None\n        try:\n            # 不再需要更新浏览器配置，因为已经在配置中设置了\n\n            # 保存配置\n            self.config.save_config()\n            info(\"配置已保存并立即生效\")\n\n            # 步骤1: 重置ID\n            info(\"【完整流程】步骤1: 重置ID...\")\n            from combined_reset_gui import ResetThread\n\n            # 创建重置线程并等待完成\n            reset_thread = ResetThread(\"all\")\n            reset_thread.update_log.connect(lambda msg: info(f\"【重置ID】{msg}\"))\n\n            # 使用一个变量来存储重置结果（作为备用）\n            reset_message = [\"\"]\n\n            # 连接信号，接收重置结果信息\n            def handle_reset_finished(success_status, message):\n                # 使用success_status参数，避免IDE警告\n                if success_status:\n                    reset_message[0] = message\n                else:\n                    reset_message[0] = f\"失败: {message}\"\n\n            reset_thread.finished_signal.connect(handle_reset_finished)\n            reset_thread.start()\n            reset_thread.wait()  # 等待重置完成\n\n            # 检查重置结果\n            if not reset_thread.success:\n                error(f\"【完整流程】重置ID失败，终止流程: {reset_message[0]}\")\n                QApplication.postEvent(self, RestoreFullProcessButtonsEvent())\n                return\n\n            info(\"【完整流程】重置ID成功，等待3秒后继续...\")\n            time.sleep(3)  # 等待一段时间确保系统稳定\n\n            # 步骤2: 快速注册\n            info(\"【完整流程】步骤2: 快速注册...\")\n            success, account_data, browser_manager = quick_signup_process(self.config)\n\n            if not success:\n                error(\"【完整流程】快速注册失败，终止流程\")\n                if \"error\" in account_data:\n                    error(f\"错误信息: {account_data['error']}\")\n                # 确保在 return 前关闭浏览器\n                if browser_manager is not None:\n                    try:\n                        browser_manager.quit()\n                        info(\"浏览器已关闭\")\n                    except Exception as e:\n                        warning(f\"关闭浏览器时出错: {e}\")\n                return\n\n            # 注册成功，输出账号信息\n            info(\"【完整流程】快速注册成功！\")\n            email = account_data.get('email', '未知')\n            token = account_data.get('token', '')\n            info(f\"邮箱: {email}\")\n            if token:\n                token_preview = token[:20] + \"...\" if len(token) > 20 else token\n                info(f\"Token: {token_preview}（已截断）\")\n\n            # 通知账号管理页面刷新账号列表\n            QApplication.postEvent(QApplication.instance().mainWindow, RefreshAccountsEvent())\n            info(\"【完整流程】等待3秒后继续...\")\n            time.sleep(3)\n\n            # 步骤3: 切换到新注册的账号\n            info(\"【完整流程】步骤3: 切换到新注册的账号...\")\n            if email != '未知' and token:\n                try:\n                    # 先退出Cursor\n                    from exit_cursor import ExitCursor\n                    info(f\"正在退出Cursor...\")\n                    ExitCursor()\n\n                    # 等待一段时间确保Cursor已关闭\n                    time.sleep(1)\n\n                    # 更新认证信息\n                    info(f\"正在更新认证信息...\")\n\n                    # 获取数据库路径\n                    system = sys.platform\n                    db_path = None\n\n                    if system == \"win32\":  # Windows\n                        appdata = os.getenv(\"APPDATA\")\n                        if appdata:\n                            db_path = Path(appdata) / \"Cursor\" / \"User\" / \"globalStorage\" / \"state.vscdb\"\n                    elif system == \"darwin\":  # macOS\n                        db_path = Path.home() / \"Library\" / \"Application Support\" / \"Cursor\" / \"User\" / \"globalStorage\" / \"state.vscdb\"\n                    elif system == \"linux\":  # Linux\n                        db_path = Path.home() / \".config\" / \"Cursor\" / \"User\" / \"globalStorage\" / \"state.vscdb\"\n\n                    if not db_path or not db_path.exists():\n                        raise FileNotFoundError(f\"找不到Cursor数据库文件\")\n\n                    # 连接数据库并更新认证信息\n                    import sqlite3\n                    conn = sqlite3.connect(str(db_path))\n                    cursor = conn.cursor()\n\n                    # 需要更新的键值对\n                    updates = [\n                        (\"cursorAuth/cachedSignUpType\", \"Auth_0\"),\n                        (\"cursorAuth/cachedEmail\", email),\n                        (\"cursorAuth/accessToken\", token),\n                        (\"cursorAuth/refreshToken\", token)\n                    ]\n\n                    for key, value in updates:\n                        # 检查键是否存在\n                        cursor.execute(\"SELECT COUNT(*) FROM itemTable WHERE key = ?\", (key,))\n                        if cursor.fetchone()[0] == 0:\n                            # 插入新记录\n                            cursor.execute(\"INSERT INTO itemTable (key, value) VALUES (?, ?)\", (key, value))\n                        else:\n                            # 更新现有记录\n                            cursor.execute(\"UPDATE itemTable SET value = ? WHERE key = ?\", (value, key))\n\n                    # 提交更改并关闭连接\n                    conn.commit()\n                    conn.close()\n\n                    # 同时也更新auth.json文件以保持一致性\n                    from app_utils import switch_cursor_account\n                    switch_cursor_account(email, token)\n\n                    # 启动Cursor\n                    info(f\"正在启动Cursor...\")\n                    if system == \"win32\":\n                        os.startfile(\"cursor://\")\n                    elif system == \"darwin\":\n                        subprocess.Popen([\"open\", \"cursor://\"])\n                    elif system == \"linux\":\n                        subprocess.Popen([\"xdg-open\", \"cursor://\"])\n\n                    info(f\"【完整流程】成功切换到账号: {email}\")\n                except Exception as e:\n                    error(f\"【完整流程】切换账号时发生错误: {e}\")\n            else:\n                error(\"【完整流程】缺少账号信息，无法切换账号\")\n\n            # 完整流程结束\n            info(\"【完整流程】全部完成!\")\n\n        except Exception as e:\n            error(f\"【完整流程】发生错误: {e}\")\n        finally:\n            # 确保浏览器被关闭\n            if browser_manager is not None:\n                try:\n                    browser_manager.quit()\n                    info(\"浏览器已关闭\")\n                except Exception as e:\n                    warning(f\"关闭浏览器时出错: {e}\")\n\n            # 恢复按钮状态\n            QApplication.postEvent(self, RestoreFullProcessButtonsEvent())\n\n    def _run_registration(self):\n        \"\"\"在线程中运行注册过程\"\"\"\n        browser_manager = None\n        try:\n            # 获取循环注册次数\n            try:\n                loop_count = int(self.loop_count_input.text())\n                if loop_count < 1:\n                    loop_count = 1\n                elif loop_count > 100:\n                    loop_count = 100\n            except ValueError:\n                loop_count = 1\n\n            # 不再需要更新浏览器配置，因为已经在配置中设置了\n\n            # 保存配置\n            self.config.save_config()\n            info(\"配置已保存并立即生效\")\n\n            info(f\"开始Cursor快速注册流程，循环次数: {loop_count}\")\n\n            # 获取是否使用随机配置\n            use_random_config = self.random_config_checkbox.isChecked() and self.random_config_checkbox.isEnabled()\n\n            # 如果使用随机配置，获取所有配置\n            config_list = []\n            if use_random_config:\n                config_list = self.config.get_config_list()\n                info(f\"启用随机配置，共有 {len(config_list)} 个配置可用\")\n\n            # 保存当前配置索引，以便在循环结束后恢复\n            original_config_index = self.config.get_current_config_index()\n\n            # 统计信息\n            start_time = time.time()\n            success_count = 0\n            fail_count = 0\n\n            # 循环注册\n            for i in range(loop_count):\n                if loop_count > 1:\n                    info(f\"\\n=== 开始第 {i+1}/{loop_count} 次注册 ===\")\n                    # 更新状态栏\n                    app_utils.update_status(f\"正在进行第 {i+1}/{loop_count} 次注册...\")\n\n                # 如果使用随机配置，随机选择一个配置\n                if use_random_config and len(config_list) > 1:\n                    # 随机选择一个配置索引，但不选择当前配置\n                    available_indices = list(range(len(config_list)))\n                    if len(available_indices) > 1:  # 如果有多个配置可选\n                        current_index = self.config.get_current_config_index()\n                        if current_index in available_indices:\n                            available_indices.remove(current_index)\n\n                    # 随机选择一个配置索引\n                    random_index = random.choice(available_indices)\n\n                    # 切换到随机配置\n                    self.config.switch_config(random_index)\n                    info(f\"随机选择配置 {random_index+1}\")\n\n                # 每次循环都需要关闭之前的浏览器\n                if browser_manager is not None:\n                    try:\n                        browser_manager.quit()\n                        info(\"浏览器已关闭\")\n                    except Exception as e:\n                        warning(f\"关闭浏览器时出错: {e}\")\n                    browser_manager = None\n\n                # 执行注册\n                success, account_data, browser_manager = quick_signup_process(self.config)\n\n                if success:\n                    success_count += 1\n                    info(f\"Cursor账号注册成功！({success_count}/{i+1})\")\n                    if \"email\" in account_data:\n                        info(f\"邮箱: {account_data['email']}\")\n                    if \"token\" in account_data:\n                        token_preview = account_data['token'][:20] + \"...\" if len(account_data['token']) > 20 else account_data['token']\n                        info(f\"Token: {token_preview}（已截断）\")\n                        info(f\"Token有效期: {account_data.get('token_info', {}).get('expiry_formatted', '未知')}\")\n\n                    # 通知账号管理页面刷新账号列表\n                    QApplication.postEvent(QApplication.instance().mainWindow, RefreshAccountsEvent())\n                else:\n                    fail_count += 1\n                    error(f\"Cursor账号注册失败！({fail_count}/{i+1})\")\n                    if \"error\" in account_data:\n                        error(f\"错误信息: {account_data['error']}\")\n\n                # 如果不是最后一次循环，等待一段时间再继续\n                if i < loop_count - 1:\n                    wait_time = random.randint(3, 6)\n                    info(f\"等待 {wait_time} 秒后继续下一次注册...\")\n                    time.sleep(wait_time)\n\n            # 统计结果\n            end_time = time.time()\n            elapsed_time = end_time - start_time\n            minutes, seconds = divmod(int(elapsed_time), 60)\n            hours, minutes = divmod(minutes, 60)\n\n            info(\"\\n=== 注册统计 ===\")\n            info(f\"总注册次数: {loop_count}\")\n            info(f\"成功次数: {success_count}\")\n            info(f\"失败次数: {fail_count}\")\n            info(f\"总用时: {hours}小时{minutes}分{seconds}秒\")\n            if loop_count > 0:\n                avg_time = elapsed_time / loop_count\n                avg_minutes, avg_seconds = divmod(int(avg_time), 60)\n                info(f\"平均每次用时: {avg_minutes}分{avg_seconds}秒\")\n\n            # 如果使用了随机配置，恢复原始配置\n            if use_random_config and self.config.get_current_config_index() != original_config_index:\n                self.config.switch_config(original_config_index)\n                info(f\"已恢复到原始配置 {original_config_index+1}\")\n\n        except Exception as e:\n            error(f\"注册过程发生错误: {e}\")\n            import traceback\n            error(traceback.format_exc())\n        finally:\n            # 确保浏览器被关闭\n            if browser_manager is not None:\n                try:\n                    browser_manager.quit()\n                    info(\"浏览器已关闭\")\n                except Exception as e:\n                    warning(f\"关闭浏览器时出错: {e}\")\n\n            # 恢复按钮状态 - 确保在主线程中执行\n            QApplication.postEvent(QApplication.instance().mainWindow, RestoreButtonEvent())\n\n    def clear_log(self):\n        \"\"\"清空日志区域\"\"\"\n        self.log_text.clear()\n        info(\"日志已清空\")\n\n# 添加新的事件类型\nclass RestoreFullProcessButtonsEvent(QEvent):\n    \"\"\"恢复完整流程所有按钮状态事件\"\"\"\n\n    EVENT_TYPE = QEvent.Type(QEvent.registerEventType())\n\n    def __init__(self):\n        \"\"\"初始化恢复完整流程按钮状态事件\"\"\"\n        super().__init__(self.EVENT_TYPE)\n\n\nclass MainWindow(QMainWindow):\n    \"\"\"主窗口类\"\"\"\n\n    def __init__(self):\n        \"\"\"初始化主窗口\"\"\"\n        super().__init__()\n\n        # 设置全局引用\n        app_utils.set_main_window(self)\n        QApplication.instance().mainWindow = self\n\n        # 设置窗口属性\n        self.setWindowTitle(\"Management Tool\")\n        self.setMinimumSize(800, 600)\n\n        # 设置全局样式表\n        self.setStyleSheet(\"\"\"\n            QMainWindow {\n                background-color: #f5f5f5;\n                color: #333333;\n            }\n            QWidget {\n                color: #333333;\n            }\n            QTabWidget::pane {\n                border: 1px solid #cccccc;\n                background: white;\n                border-radius: 4px;\n            }\n            QTabBar::tab {\n                background: #e0e0e0;\n                border: 1px solid #cccccc;\n                padding: 6px 12px;\n                border-top-left-radius: 4px;\n                border-top-right-radius: 4px;\n                margin-right: 2px;\n                color: #333333;\n            }\n            QTabBar::tab:selected {\n                background: white;\n                border-bottom-color: white;\n                color: #333333;\n            }\n            QGroupBox {\n                border: 1px solid #cccccc;\n                border-radius: 4px;\n                margin-top: 12px;\n                font-weight: bold;\n                background-color: white;\n                color: #333333;\n            }\n            QGroupBox::title {\n                subcontrol-origin: margin;\n                left: 10px;\n                padding: 0 3px;\n                color: #333333;\n            }\n            QPushButton {\n                background-color: #4285f4;\n                color: white;\n                border: none;\n                border-radius: 4px;\n                padding: 6px 10px;\n                font-weight: bold;\n            }\n            QPushButton:hover {\n                background-color: #3275e4;\n            }\n            QPushButton:pressed {\n                background-color: #2265d4;\n            }\n            QPushButton:disabled {\n                background-color: #cccccc;\n                color: #666666;\n            }\n            QLineEdit {\n                border: 1px solid #cccccc;\n                border-radius: 3px;\n                padding: 4px;\n                background-color: white;\n                color: #333333;\n            }\n            QTextEdit {\n                border: 1px solid #cccccc;\n                border-radius: 3px;\n                background-color: white;\n                color: #333333;\n            }\n            QTableView {\n                border: 1px solid #cccccc;\n                border-radius: 3px;\n                background-color: white;\n                alternate-background-color: #f5f5f5;\n                color: #333333;\n                gridline-color: #e0e0e0;\n            }\n            QTableView::item {\n                border-bottom: 1px solid #f0f0f0;\n                color: #333333;\n                padding: 4px;\n            }\n            QHeaderView::section {\n                background-color: #e0e0e0;\n                padding: 4px;\n                border: 1px solid #cccccc;\n                font-weight: bold;\n                color: #333333;\n            }\n            QLabel {\n                color: #333333;\n            }\n            QCheckBox {\n                color: #333333;\n            }\n            QCheckBox::indicator {\n                width: 16px;\n                height: 16px;\n                border: 2px solid #bbbbbb;\n                border-radius: 3px;\n                background-color: white;\n            }\n            QCheckBox::indicator:checked {\n                border: 2px solid #4CAF50;\n                border-radius: 3px;\n                background-color: #4CAF50;\n                image: url(resources/check.png);\n            }\n            QCheckBox::indicator:hover {\n                border: 2px solid #4285f4;\n            }\n            QComboBox {\n                border: 1px solid #cccccc;\n                border-radius: 3px;\n                padding: 4px;\n                background-color: white;\n                color: #333333;\n            }\n            QComboBox QAbstractItemView {\n                background-color: white;\n                color: #333333;\n                selection-background-color: #4285f4;\n                selection-color: white;\n                border: 1px solid #cccccc;\n            }\n            QMenu {\n                background-color: white;\n                color: #333333;\n                border: 1px solid #cccccc;\n            }\n            QMenu::item {\n                padding: 6px 24px;\n                color: #333333;\n            }\n            QMenu::item:selected {\n                background-color: #4285f4;\n                color: white;\n            }\n            QStatusBar {\n                background-color: #f5f5f5;\n                color: #333333;\n                border-top: 1px solid #cccccc;\n            }\n            QToolTip {\n                background-color: #ffffcc;\n                color: #333333;\n                border: 1px solid #e0e0e0;\n            }\n            QMessageBox {\n                background-color: white;\n                color: #333333;\n            }\n            QMessageBox QLabel {\n                color: #333333;\n                background-color: transparent;\n            }\n            QMessageBox QPushButton {\n                color: white;\n                background-color: #4285f4;\n                border: none;\n                border-radius: 4px;\n                padding: 6px 12px;\n                font-weight: 600;\n                font-size: 12px;\n            }\n            QMessageBox QPushButton:hover {\n                background-color: #3275e4;\n            }\n        \"\"\")\n\n        # 创建中央部件\n        self.central_widget = QWidget()\n        self.setCentralWidget(self.central_widget)\n\n        # 创建主布局\n        self.main_layout = QVBoxLayout(self.central_widget)\n        self.main_layout.setContentsMargins(10, 10, 10, 10)\n        self.main_layout.setSpacing(6)\n\n        # 创建标签页控件\n        self.tab_widget = QTabWidget()\n        self.main_layout.addWidget(self.tab_widget)\n\n        # 创建注册标签页（改名为\"Cursor主页\"）\n        self.registration_widget = RegistrationWidget()\n        self.tab_widget.addTab(self.registration_widget, \"Cursor主页\")\n\n        # 创建账号管理标签页（改名为\"Cursor账号管理\"）\n        self.account_manager_widget = AccountManagerWidget(self)\n        self.tab_widget.addTab(self.account_manager_widget, \"Cursor账号管理\")\n\n        # 创建Windsurf主页标签页\n        self.windsurf_registration_widget = WindsurfRegistrationWidget(self)\n        self.tab_widget.addTab(self.windsurf_registration_widget, \"Windsurf主页\")\n\n        # 创建Windsurf账号管理标签页\n        self.windsurf_account_manager_widget = WindsurfAccountManagerWidget(self)\n        self.tab_widget.addTab(self.windsurf_account_manager_widget, \"Windsurf账号管理\")\n\n        # 创建Augment工具标签页\n        self.augment_tools_widget = AugmentToolsWidget(self)\n        self.tab_widget.addTab(self.augment_tools_widget, \"Augment工具\")\n\n        # 创建关于标签页\n        self.about_widget = self.create_about_widget()\n        self.tab_widget.addTab(self.about_widget, \"关于\")\n\n        # 状态栏\n        self.statusBar = QStatusBar()\n        self.setStatusBar(self.statusBar)\n        self.statusBar.showMessage(\"就绪\")\n\n        # 设置日志回调\n        self.log_handler = LogHandler(self.registration_widget.log_text)\n        set_print_callback(self.handle_log)\n\n        # 显示初始信息\n        info(\"Management Tool已启动\")\n\n    def handle_log(self, log_type: str, message: str):\n        \"\"\"处理日志回调\n\n        Args:\n            log_type: 日志类型\n            message: 日志消息\n        \"\"\"\n        # 将日志处理委托给日志处理器\n        QApplication.postEvent(self, LogEvent(log_type, message))\n\n    def create_about_widget(self):\n        \"\"\"创建关于标签页\"\"\"\n        widget = QWidget()\n        main_layout = QVBoxLayout(widget)\n        main_layout.setContentsMargins(15, 15, 15, 15)\n        main_layout.setSpacing(8)\n\n        # 软件标题\n        title_label = QLabel(\"AI注册工具集\")\n        title_label.setStyleSheet(\"font-size: 22px; font-weight: bold; color: #333; margin-bottom: 5px;\")\n        title_label.setAlignment(Qt.AlignCenter)\n        main_layout.addWidget(title_label)\n\n        # 版本信息\n        version_label = QLabel(\"版本 4.0\")\n        version_label.setStyleSheet(\"font-size: 13px; color: #666; margin-bottom: 5px;\")\n        version_label.setAlignment(Qt.AlignCenter)\n        main_layout.addWidget(version_label)\n\n        # 更新时间\n        update_label = QLabel(\"更新时间：2025/6/16\")\n        update_label.setStyleSheet(\"font-size: 11px; color: #888; margin-bottom: 10px;\")\n        update_label.setAlignment(Qt.AlignCenter)\n        main_layout.addWidget(update_label)\n\n        # 软件功能介绍\n        features_frame = QGroupBox(\"🚀 工具集功能\")\n        features_layout = QVBoxLayout(features_frame)\n        features_layout.setContentsMargins(10, 8, 10, 8)\n        features_layout.setSpacing(5)\n\n        features_text = QLabel(\"\"\"🎯 Cursor 快速注册：一键自动化注册流程，从邮箱生成到账号激活全自动完成\n👥 Cursor 账号管理：安全存储和批量管理多个账号，支持登录验证和状态检查\n🌊 Windsurf 快速注册：全自动Windsurf账号注册，支持人机验证和邮箱验证码\n🏄 Windsurf 账号管理：专业的Windsurf账号管理，支持Token管理和批量操作\n🧹 Windsurf 一键重置：自动关闭进程并清理用户数据，重置机器ID获得新环境\n🔧 Cursor 重置工具：彻底清除设备标识信息，重置机器码获得新的试用期\n⚡ Augment 重置工具：一键重置VSCode扩展，支持邮箱生成和验证码获取\n📧 智能邮箱服务：支持自动生成和手动输入，自动添加域名配置\n🛡️ 隐私保护技术：使用密码学安全算法，支持60+种设备ID字段重置\"\"\")\n        features_text.setStyleSheet(\"font-size: 11px; color: #555; line-height: 1.3; padding: 5px;\")\n        features_text.setWordWrap(True)\n        features_layout.addWidget(features_text)\n\n        main_layout.addWidget(features_frame)\n\n        # 技术特性\n        tech_frame = QGroupBox(\"💻 技术特性\")\n        tech_layout = QVBoxLayout(tech_frame)\n        tech_layout.setContentsMargins(10, 8, 10, 8)\n        tech_layout.setSpacing(5)\n\n        tech_text = QLabel(\"\"\"🔒 安全性：密码学安全随机数生成器 • 本地数据加密存储 • 自动备份重要文件\n🎯 智能化：自动检测软件路径 • 智能错误处理重试 • 实时日志状态反馈\n🔧 兼容性：支持Windows 10/11 • 兼容所有Cursor版本 • 支持VSCode和Augment扩展\n⚡ 高效性：多标签页界面设计 • 批量操作支持 • 内存优化运行流畅\"\"\")\n        tech_text.setStyleSheet(\"font-size: 11px; color: #555; line-height: 1.3; padding: 5px;\")\n        tech_text.setWordWrap(True)\n        tech_layout.addWidget(tech_text)\n\n        main_layout.addWidget(tech_frame)\n\n        # 使用指南\n        guide_frame = QGroupBox(\"📖 使用指南\")\n        guide_layout = QVBoxLayout(guide_frame)\n        guide_layout.setContentsMargins(10, 8, 10, 8)\n        guide_layout.setSpacing(5)\n\n        guide_text = QLabel(\"\"\"🚀 快速开始：选择对应标签页使用功能 • Cursor主页注册账号 • Windsurf主页注册Windsurf • 账号管理查看信息 • Augment工具重置扩展\n⚠️ 注意事项：确保网络连接正常 • 重置前关闭相关软件 • 操作后重启软件生效 • 定期备份账号信息\n🔧 配置建议：配置多个邮箱服务提高成功率 • 使用手动输入自定义邮箱前缀 • 定期清理日志节省空间\"\"\")\n        guide_text.setStyleSheet(\"font-size: 11px; color: #555; line-height: 1.3; padding: 5px;\")\n        guide_text.setWordWrap(True)\n        guide_layout.addWidget(guide_text)\n\n        main_layout.addWidget(guide_frame)\n\n        # 开发信息\n        dev_frame = QGroupBox(\"开发信息\")\n        dev_layout = QVBoxLayout(dev_frame)\n        dev_layout.setContentsMargins(10, 8, 10, 8)\n        dev_layout.setSpacing(5)\n\n        dev_text = QLabel(\"\"\"👨‍💻 开发者：XIAOFU  📅 更新时间：2025年6月16日  🔧 技术栈：Python + PySide6\n⚠️ 使用须知：确保网络连接正常 • 注册过程中勿关闭软件 • 重置操作自动备份 • 操作后重启软件生效 • 仅用于合法学习研究\n📞 技术支持：查看日志信息 • 自动错误检测修复 • 详细操作指导帮助\"\"\")\n        dev_text.setStyleSheet(\"font-size: 11px; color: #555; line-height: 1.3; padding: 5px;\")\n        dev_text.setWordWrap(True)\n        dev_layout.addWidget(dev_text)\n\n        main_layout.addWidget(dev_frame)\n\n        # 添加弹性空间\n        main_layout.addStretch()\n\n        return widget\n\n    def event(self, event):\n        \"\"\"事件处理\n\n        处理自定义事件\n        \"\"\"\n        if isinstance(event, LogEvent):\n            self.log_handler.handle_log(event.log_type, event.message)\n            return True\n        elif isinstance(event, RefreshAccountsEvent):\n            self.account_manager_widget.load_accounts()\n            # 同时刷新Windsurf账号管理\n            self.windsurf_account_manager_widget.load_accounts()\n            return True\n        elif isinstance(event, RestoreButtonEvent):\n            # 恢复注册按钮状态\n            info(\"恢复注册按钮状态\")\n            self.registration_widget.register_btn.setEnabled(True)\n            self.registration_widget.register_btn.setText(\"快速注册\")\n            app_utils.update_status(\"注册完成\")\n            return True\n        elif isinstance(event, RestoreWindsurfButtonEvent):\n            # 恢复Windsurf注册按钮状态\n            info(\"恢复Windsurf注册按钮状态\")\n            self.windsurf_registration_widget.register_btn.setEnabled(True)\n            self.windsurf_registration_widget.register_btn.setText(\"快速注册\")\n            app_utils.update_status(\"Windsurf注册完成\")\n            return True\n        elif isinstance(event, RestoreWindsurfResetButtonEvent):\n            # 恢复Windsurf重置按钮状态\n            info(\"恢复Windsurf重置按钮状态\")\n            self.windsurf_registration_widget.reset_btn.setEnabled(True)\n            self.windsurf_registration_widget.reset_btn.setText(\"一键清理重置\")\n            app_utils.update_status(\"Windsurf重置完成\")\n            return True\n        elif isinstance(event, RestoreFullProcessButtonsEvent):\n            # 恢复所有按钮状态\n            info(\"恢复所有按钮状态\")\n            self.registration_widget.full_process_btn.setEnabled(True)\n            self.registration_widget.register_btn.setEnabled(True)\n            self.registration_widget.reset_id_btn.setEnabled(True)\n            self.registration_widget.full_process_btn.setText(\"完整流程\")\n            app_utils.update_status(\"完整流程已完成\")\n            return True\n        return super().event(event)\n\n\n# 自定义事件类型\nclass LogEvent(QEvent):\n    \"\"\"日志事件，用于在线程间安全地传递日志消息\"\"\"\n\n    EVENT_TYPE = QEvent.Type(QEvent.registerEventType())\n\n    def __init__(self, log_type: str, message: str):\n        \"\"\"初始化日志事件\n\n        Args:\n            log_type: 日志类型\n            message: 日志消息\n        \"\"\"\n        super().__init__(self.EVENT_TYPE)\n        self.log_type = log_type\n        self.message = message\n\nclass RestoreButtonEvent(QEvent):\n    \"\"\"恢复按钮状态事件\"\"\"\n\n    EVENT_TYPE = QEvent.Type(QEvent.registerEventType())\n\n    def __init__(self):\n        \"\"\"初始化恢复按钮状态事件\"\"\"\n        super().__init__(self.EVENT_TYPE)\n\nclass RefreshAccountsEvent(QEvent):\n    \"\"\"刷新账号列表事件\"\"\"\n\n    EVENT_TYPE = QEvent.Type(QEvent.registerEventType())\n\n    def __init__(self):\n        \"\"\"初始化刷新账号列表事件\"\"\"\n        super().__init__(self.EVENT_TYPE)\n\n\ndef main():\n    \"\"\"主函数 - 支持新旧界面切换\"\"\"\n    try:\n        if USE_NEW_UI:\n            # 使用新的模块化界面\n            print(\"启动新的模块化界面...\")\n\n            # 创建应用程序\n            app = create_application()\n\n            # 创建主窗口\n            window = NewMainWindow()\n            window.show()\n\n            # 运行应用程序\n            sys.exit(app.exec())\n        else:\n            # 使用原始界面\n            print(\"启动原始界面...\")\n\n            # 创建应用程序\n            app = QApplication(sys.argv)\n\n            # 设置应用程序样式\n            app.setStyle(\"Fusion\")\n\n            # 创建并显示主窗口\n            window = MainWindow()\n            window.show()\n\n            # 运行应用程序事件循环\n            sys.exit(app.exec())\n\n    except Exception as e:\n        print(f\"程序启动失败: {e}\")\n\n        # 如果新界面启动失败，尝试使用原始界面\n        if USE_NEW_UI:\n            print(\"新界面启动失败，尝试使用原始界面...\")\n            try:\n                # 创建应用程序\n                app = QApplication(sys.argv)\n\n                # 设置应用程序样式\n                app.setStyle(\"Fusion\")\n\n                # 创建并显示主窗口\n                window = MainWindow()\n                window.show()\n\n                # 运行应用程序事件循环\n                sys.exit(app.exec())\n            except Exception as e2:\n                print(f\"原始界面也启动失败: {e2}\")\n                sys.exit(1)\n        else:\n            sys.exit(1)\n\n\nif __name__ == \"__main__\":\n    main()"}
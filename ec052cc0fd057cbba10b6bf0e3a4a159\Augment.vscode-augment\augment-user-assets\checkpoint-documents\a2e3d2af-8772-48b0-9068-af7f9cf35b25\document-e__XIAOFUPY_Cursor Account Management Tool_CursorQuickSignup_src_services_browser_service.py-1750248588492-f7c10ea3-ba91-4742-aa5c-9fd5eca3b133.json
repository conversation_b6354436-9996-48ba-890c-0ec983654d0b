{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/services/browser_service.py"}, "originalCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n浏览器服务\n\n提供浏览器自动化操作功能。\n\"\"\"\n\nimport time\nfrom typing import Optional, Dict, Any, List\n\ntry:\n    from selenium import webdriver\n    from selenium.webdriver.common.by import By\n    from selenium.webdriver.support.ui import WebDriverWait\n    from selenium.webdriver.support import expected_conditions as EC\n    from selenium.webdriver.chrome.options import Options as ChromeOptions\n    from selenium.webdriver.firefox.options import Options as FirefoxOptions\n    from selenium.webdriver.edge.options import Options as EdgeOptions\n    from selenium.common.exceptions import TimeoutException, WebDriverException\n    SELENIUM_AVAILABLE = True\nexcept ImportError:\n    SELENIUM_AVAILABLE = False\n    # 创建占位符类\n    class webdriver:\n        class Remote: pass\n        class Chrome: pass\n        class Firefox: pass\n        class Edge: pass\n    class By:\n        CSS_SELECTOR = \"css selector\"\n        ID = \"id\"\n        NAME = \"name\"\n        CLASS_NAME = \"class name\"\n    class WebDriverWait: pass\n    class ChromeOptions: pass\n    class FirefoxOptions: pass\n    class EdgeOptions: pass\n    class TimeoutException(Exception): pass\n    class WebDriverException(Exception): pass\n\nfrom ..core.config import Config\nfrom ..core.logger import get_logger\nfrom ..core.exceptions import BrowserError\nfrom ..core.constants import BROWSER_WAIT_TIMEOUT, BROWSER_IMPLICIT_WAIT, USER_AGENT\nfrom ..utils.webdriver_manager import get_chromedriver_path\n\n\nclass BrowserService:\n    \"\"\"浏览器服务类\"\"\"\n    \n    def __init__(self, config: Config):\n        \"\"\"初始化浏览器服务\n        \n        Args:\n            config: 配置对象\n        \"\"\"\n        self.config = config\n        self.logger = get_logger()\n        self.driver: Optional[webdriver.Remote] = None\n        self.wait: Optional[WebDriverWait] = None\n    \n    def create_driver(self, browser_type: str = \"chrome\", headless: Optional[bool] = None) -> webdriver.Remote:\n        \"\"\"创建浏览器驱动\n\n        Args:\n            browser_type: 浏览器类型（chrome, firefox, edge）\n            headless: 是否无头模式，None时使用配置\n\n        Returns:\n            浏览器驱动对象\n        \"\"\"\n        try:\n            if not SELENIUM_AVAILABLE:\n                raise BrowserError(\"Selenium未安装，请运行: pip install selenium\")\n\n            if headless is None:\n                headless = self.config.is_browser_headless()\n\n            self.logger.info(f\"创建 {browser_type} 浏览器驱动，无头模式: {headless}\")\n\n            if browser_type.lower() == \"chrome\":\n                driver = self._create_chrome_driver(headless)\n            elif browser_type.lower() == \"firefox\":\n                driver = self._create_firefox_driver(headless)\n            elif browser_type.lower() == \"edge\":\n                driver = self._create_edge_driver(headless)\n            else:\n                raise BrowserError(f\"不支持的浏览器类型: {browser_type}\")\n\n            # 设置隐式等待\n            driver.implicitly_wait(BROWSER_IMPLICIT_WAIT)\n\n            # 创建显式等待对象\n            self.driver = driver\n            self.wait = WebDriverWait(driver, BROWSER_WAIT_TIMEOUT)\n\n            self.logger.info(\"浏览器驱动创建成功\")\n            return driver\n\n        except Exception as e:\n            self.logger.error(f\"创建浏览器驱动失败: {e}\")\n            raise BrowserError(f\"创建浏览器驱动失败: {e}\")\n    \n    def _create_chrome_driver(self, headless: bool) -> webdriver.Chrome:\n        \"\"\"创建Chrome驱动\"\"\"\n        options = ChromeOptions()\n        \n        if headless:\n            options.add_argument(\"--headless\")\n        \n        # 基础选项\n        options.add_argument(\"--no-sandbox\")\n        options.add_argument(\"--disable-dev-shm-usage\")\n        options.add_argument(\"--disable-gpu\")\n        options.add_argument(\"--disable-web-security\")\n        options.add_argument(\"--disable-features=VizDisplayCompositor\")\n        options.add_argument(\"--disable-blink-features=AutomationControlled\")\n        options.add_experimental_option(\"excludeSwitches\", [\"enable-automation\"])\n        options.add_experimental_option('useAutomationExtension', False)\n        \n        # 设置用户代理\n        options.add_argument(f\"--user-agent={USER_AGENT}\")\n        \n        # 窗口大小\n        options.add_argument(\"--window-size=1920,1080\")\n        \n        # 禁用图片加载以提高速度\n        prefs = {\n            \"profile.managed_default_content_settings.images\": 2,\n            \"profile.default_content_setting_values.notifications\": 2\n        }\n        options.add_experimental_option(\"prefs\", prefs)\n        \n        return webdriver.Chrome(options=options)\n    \n    def _create_firefox_driver(self, headless: bool) -> webdriver.Firefox:\n        \"\"\"创建Firefox驱动\"\"\"\n        options = FirefoxOptions()\n        \n        if headless:\n            options.add_argument(\"--headless\")\n        \n        # 基础选项\n        options.add_argument(\"--no-sandbox\")\n        options.add_argument(\"--disable-dev-shm-usage\")\n        \n        # 设置用户代理\n        options.set_preference(\"general.useragent.override\", USER_AGENT)\n        \n        # 禁用图片加载\n        options.set_preference(\"permissions.default.image\", 2)\n        \n        return webdriver.Firefox(options=options)\n    \n    def _create_edge_driver(self, headless: bool) -> webdriver.Edge:\n        \"\"\"创建Edge驱动\"\"\"\n        options = EdgeOptions()\n        \n        if headless:\n            options.add_argument(\"--headless\")\n        \n        # 基础选项\n        options.add_argument(\"--no-sandbox\")\n        options.add_argument(\"--disable-dev-shm-usage\")\n        options.add_argument(\"--disable-gpu\")\n        \n        # 设置用户代理\n        options.add_argument(f\"--user-agent={USER_AGENT}\")\n        \n        return webdriver.Edge(options=options)\n    \n    def navigate_to(self, url: str) -> bool:\n        \"\"\"导航到指定URL\n        \n        Args:\n            url: 目标URL\n            \n        Returns:\n            是否成功\n        \"\"\"\n        try:\n            if not self.driver:\n                raise BrowserError(\"浏览器驱动未初始化\")\n            \n            self.logger.info(f\"导航到: {url}\")\n            self.driver.get(url)\n            \n            # 等待页面加载\n            time.sleep(2)\n            \n            return True\n            \n        except Exception as e:\n            self.logger.error(f\"导航失败: {e}\")\n            return False\n    \n    def wait_for_element(self, by: By, value: str, timeout: int = BROWSER_WAIT_TIMEOUT) -> Optional[Any]:\n        \"\"\"等待元素出现\n        \n        Args:\n            by: 定位方式\n            value: 定位值\n            timeout: 超时时间\n            \n        Returns:\n            元素对象，未找到返回None\n        \"\"\"\n        try:\n            if not self.driver:\n                raise BrowserError(\"浏览器驱动未初始化\")\n            \n            wait = WebDriverWait(self.driver, timeout)\n            element = wait.until(EC.presence_of_element_located((by, value)))\n            return element\n            \n        except TimeoutException:\n            self.logger.warning(f\"等待元素超时: {by}={value}\")\n            return None\n        except Exception as e:\n            self.logger.error(f\"等待元素失败: {e}\")\n            return None\n    \n    def wait_for_clickable(self, by: By, value: str, timeout: int = BROWSER_WAIT_TIMEOUT) -> Optional[Any]:\n        \"\"\"等待元素可点击\n        \n        Args:\n            by: 定位方式\n            value: 定位值\n            timeout: 超时时间\n            \n        Returns:\n            元素对象，未找到返回None\n        \"\"\"\n        try:\n            if not self.driver:\n                raise BrowserError(\"浏览器驱动未初始化\")\n            \n            wait = WebDriverWait(self.driver, timeout)\n            element = wait.until(EC.element_to_be_clickable((by, value)))\n            return element\n            \n        except TimeoutException:\n            self.logger.warning(f\"等待元素可点击超时: {by}={value}\")\n            return None\n        except Exception as e:\n            self.logger.error(f\"等待元素可点击失败: {e}\")\n            return None\n    \n    def click_element(self, by: By, value: str, timeout: int = BROWSER_WAIT_TIMEOUT) -> bool:\n        \"\"\"点击元素\n        \n        Args:\n            by: 定位方式\n            value: 定位值\n            timeout: 超时时间\n            \n        Returns:\n            是否成功\n        \"\"\"\n        try:\n            element = self.wait_for_clickable(by, value, timeout)\n            if element:\n                element.click()\n                time.sleep(1)  # 点击后等待\n                return True\n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"点击元素失败: {e}\")\n            return False\n    \n    def input_text(self, by: By, value: str, text: str, timeout: int = BROWSER_WAIT_TIMEOUT) -> bool:\n        \"\"\"输入文本\n        \n        Args:\n            by: 定位方式\n            value: 定位值\n            text: 输入文本\n            timeout: 超时时间\n            \n        Returns:\n            是否成功\n        \"\"\"\n        try:\n            element = self.wait_for_element(by, value, timeout)\n            if element:\n                element.clear()\n                element.send_keys(text)\n                time.sleep(0.5)  # 输入后等待\n                return True\n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"输入文本失败: {e}\")\n            return False\n    \n    def get_page_source(self) -> str:\n        \"\"\"获取页面源码\n        \n        Returns:\n            页面源码\n        \"\"\"\n        try:\n            if not self.driver:\n                raise BrowserError(\"浏览器驱动未初始化\")\n            \n            return self.driver.page_source\n            \n        except Exception as e:\n            self.logger.error(f\"获取页面源码失败: {e}\")\n            return \"\"\n    \n    def get_current_url(self) -> str:\n        \"\"\"获取当前URL\n        \n        Returns:\n            当前URL\n        \"\"\"\n        try:\n            if not self.driver:\n                raise BrowserError(\"浏览器驱动未初始化\")\n            \n            return self.driver.current_url\n            \n        except Exception as e:\n            self.logger.error(f\"获取当前URL失败: {e}\")\n            return \"\"\n    \n    def execute_script(self, script: str) -> Any:\n        \"\"\"执行JavaScript脚本\n        \n        Args:\n            script: JavaScript代码\n            \n        Returns:\n            执行结果\n        \"\"\"\n        try:\n            if not self.driver:\n                raise BrowserError(\"浏览器驱动未初始化\")\n            \n            return self.driver.execute_script(script)\n            \n        except Exception as e:\n            self.logger.error(f\"执行脚本失败: {e}\")\n            return None\n    \n    def quit(self):\n        \"\"\"关闭浏览器\"\"\"\n        try:\n            if self.driver:\n                self.driver.quit()\n                self.driver = None\n                self.wait = None\n                self.logger.info(\"浏览器已关闭\")\n                \n        except Exception as e:\n            self.logger.error(f\"关闭浏览器失败: {e}\")\n    \n    def __enter__(self):\n        \"\"\"上下文管理器入口\"\"\"\n        return self\n    \n    def __exit__(self, exc_type, exc_val, exc_tb):\n        \"\"\"上下文管理器出口\"\"\"\n        self.quit()\n        return False\n", "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n浏览器服务\n\n提供浏览器自动化操作功能。\n\"\"\"\n\nimport time\nfrom typing import Optional, Any\n\ntry:\n    from selenium import webdriver\n    from selenium.webdriver.common.by import By\n    from selenium.webdriver.support.ui import WebDriverWait\n    from selenium.webdriver.support import expected_conditions as EC\n    from selenium.webdriver.chrome.options import Options as ChromeOptions\n    from selenium.webdriver.firefox.options import Options as FirefoxOptions\n    from selenium.webdriver.edge.options import Options as EdgeOptions\n    from selenium.common.exceptions import TimeoutException, WebDriverException\n    SELENIUM_AVAILABLE = True\nexcept ImportError:\n    SELENIUM_AVAILABLE = False\n    # 创建占位符类\n    class webdriver:\n        class Remote: pass\n        class Chrome: pass\n        class Firefox: pass\n        class Edge: pass\n    class By:\n        CSS_SELECTOR = \"css selector\"\n        ID = \"id\"\n        NAME = \"name\"\n        CLASS_NAME = \"class name\"\n    class WebDriverWait: pass\n    class ChromeOptions: pass\n    class FirefoxOptions: pass\n    class EdgeOptions: pass\n    class TimeoutException(Exception): pass\n    class WebDriverException(Exception): pass\n\nfrom ..core.config import Config\nfrom ..core.logger import get_logger\nfrom ..core.exceptions import BrowserError\nfrom ..core.constants import BROWSER_WAIT_TIMEOUT, BROWSER_IMPLICIT_WAIT, USER_AGENT\nfrom ..utils.webdriver_manager import get_chromedriver_path\n\n\nclass BrowserService:\n    \"\"\"浏览器服务类\"\"\"\n    \n    def __init__(self, config: Config):\n        \"\"\"初始化浏览器服务\n        \n        Args:\n            config: 配置对象\n        \"\"\"\n        self.config = config\n        self.logger = get_logger()\n        self.driver: Optional[webdriver.Remote] = None\n        self.wait: Optional[WebDriverWait] = None\n    \n    def create_driver(self, browser_type: str = \"chrome\", headless: Optional[bool] = None) -> webdriver.Remote:\n        \"\"\"创建浏览器驱动\n\n        Args:\n            browser_type: 浏览器类型（chrome, firefox, edge）\n            headless: 是否无头模式，None时使用配置\n\n        Returns:\n            浏览器驱动对象\n        \"\"\"\n        try:\n            if not SELENIUM_AVAILABLE:\n                raise BrowserError(\"Selenium未安装，请运行: pip install selenium\")\n\n            if headless is None:\n                headless = self.config.is_browser_headless()\n\n            self.logger.info(f\"创建 {browser_type} 浏览器驱动，无头模式: {headless}\")\n\n            if browser_type.lower() == \"chrome\":\n                driver = self._create_chrome_driver(headless)\n            elif browser_type.lower() == \"firefox\":\n                driver = self._create_firefox_driver(headless)\n            elif browser_type.lower() == \"edge\":\n                driver = self._create_edge_driver(headless)\n            else:\n                raise BrowserError(f\"不支持的浏览器类型: {browser_type}\")\n\n            # 设置隐式等待\n            driver.implicitly_wait(BROWSER_IMPLICIT_WAIT)\n\n            # 创建显式等待对象\n            self.driver = driver\n            self.wait = WebDriverWait(driver, BROWSER_WAIT_TIMEOUT)\n\n            self.logger.info(\"浏览器驱动创建成功\")\n            return driver\n\n        except Exception as e:\n            self.logger.error(f\"创建浏览器驱动失败: {e}\")\n            raise BrowserError(f\"创建浏览器驱动失败: {e}\")\n    \n    def _create_chrome_driver(self, headless: bool) -> webdriver.Chrome:\n        \"\"\"创建Chrome驱动\"\"\"\n        options = ChromeOptions()\n        \n        if headless:\n            options.add_argument(\"--headless\")\n        \n        # 基础选项\n        options.add_argument(\"--no-sandbox\")\n        options.add_argument(\"--disable-dev-shm-usage\")\n        options.add_argument(\"--disable-gpu\")\n        options.add_argument(\"--disable-web-security\")\n        options.add_argument(\"--disable-features=VizDisplayCompositor\")\n        options.add_argument(\"--disable-blink-features=AutomationControlled\")\n        options.add_experimental_option(\"excludeSwitches\", [\"enable-automation\"])\n        options.add_experimental_option('useAutomationExtension', False)\n        \n        # 设置用户代理\n        options.add_argument(f\"--user-agent={USER_AGENT}\")\n        \n        # 窗口大小\n        options.add_argument(\"--window-size=1920,1080\")\n        \n        # 禁用图片加载以提高速度\n        prefs = {\n            \"profile.managed_default_content_settings.images\": 2,\n            \"profile.default_content_setting_values.notifications\": 2\n        }\n        options.add_experimental_option(\"prefs\", prefs)\n        \n        return webdriver.Chrome(options=options)\n    \n    def _create_firefox_driver(self, headless: bool) -> webdriver.Firefox:\n        \"\"\"创建Firefox驱动\"\"\"\n        options = FirefoxOptions()\n        \n        if headless:\n            options.add_argument(\"--headless\")\n        \n        # 基础选项\n        options.add_argument(\"--no-sandbox\")\n        options.add_argument(\"--disable-dev-shm-usage\")\n        \n        # 设置用户代理\n        options.set_preference(\"general.useragent.override\", USER_AGENT)\n        \n        # 禁用图片加载\n        options.set_preference(\"permissions.default.image\", 2)\n        \n        return webdriver.Firefox(options=options)\n    \n    def _create_edge_driver(self, headless: bool) -> webdriver.Edge:\n        \"\"\"创建Edge驱动\"\"\"\n        options = EdgeOptions()\n        \n        if headless:\n            options.add_argument(\"--headless\")\n        \n        # 基础选项\n        options.add_argument(\"--no-sandbox\")\n        options.add_argument(\"--disable-dev-shm-usage\")\n        options.add_argument(\"--disable-gpu\")\n        \n        # 设置用户代理\n        options.add_argument(f\"--user-agent={USER_AGENT}\")\n        \n        return webdriver.Edge(options=options)\n    \n    def navigate_to(self, url: str) -> bool:\n        \"\"\"导航到指定URL\n        \n        Args:\n            url: 目标URL\n            \n        Returns:\n            是否成功\n        \"\"\"\n        try:\n            if not self.driver:\n                raise BrowserError(\"浏览器驱动未初始化\")\n            \n            self.logger.info(f\"导航到: {url}\")\n            self.driver.get(url)\n            \n            # 等待页面加载\n            time.sleep(2)\n            \n            return True\n            \n        except Exception as e:\n            self.logger.error(f\"导航失败: {e}\")\n            return False\n    \n    def wait_for_element(self, by: By, value: str, timeout: int = BROWSER_WAIT_TIMEOUT) -> Optional[Any]:\n        \"\"\"等待元素出现\n        \n        Args:\n            by: 定位方式\n            value: 定位值\n            timeout: 超时时间\n            \n        Returns:\n            元素对象，未找到返回None\n        \"\"\"\n        try:\n            if not self.driver:\n                raise BrowserError(\"浏览器驱动未初始化\")\n            \n            wait = WebDriverWait(self.driver, timeout)\n            element = wait.until(EC.presence_of_element_located((by, value)))\n            return element\n            \n        except TimeoutException:\n            self.logger.warning(f\"等待元素超时: {by}={value}\")\n            return None\n        except Exception as e:\n            self.logger.error(f\"等待元素失败: {e}\")\n            return None\n    \n    def wait_for_clickable(self, by: By, value: str, timeout: int = BROWSER_WAIT_TIMEOUT) -> Optional[Any]:\n        \"\"\"等待元素可点击\n        \n        Args:\n            by: 定位方式\n            value: 定位值\n            timeout: 超时时间\n            \n        Returns:\n            元素对象，未找到返回None\n        \"\"\"\n        try:\n            if not self.driver:\n                raise BrowserError(\"浏览器驱动未初始化\")\n            \n            wait = WebDriverWait(self.driver, timeout)\n            element = wait.until(EC.element_to_be_clickable((by, value)))\n            return element\n            \n        except TimeoutException:\n            self.logger.warning(f\"等待元素可点击超时: {by}={value}\")\n            return None\n        except Exception as e:\n            self.logger.error(f\"等待元素可点击失败: {e}\")\n            return None\n    \n    def click_element(self, by: By, value: str, timeout: int = BROWSER_WAIT_TIMEOUT) -> bool:\n        \"\"\"点击元素\n        \n        Args:\n            by: 定位方式\n            value: 定位值\n            timeout: 超时时间\n            \n        Returns:\n            是否成功\n        \"\"\"\n        try:\n            element = self.wait_for_clickable(by, value, timeout)\n            if element:\n                element.click()\n                time.sleep(1)  # 点击后等待\n                return True\n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"点击元素失败: {e}\")\n            return False\n    \n    def input_text(self, by: By, value: str, text: str, timeout: int = BROWSER_WAIT_TIMEOUT) -> bool:\n        \"\"\"输入文本\n        \n        Args:\n            by: 定位方式\n            value: 定位值\n            text: 输入文本\n            timeout: 超时时间\n            \n        Returns:\n            是否成功\n        \"\"\"\n        try:\n            element = self.wait_for_element(by, value, timeout)\n            if element:\n                element.clear()\n                element.send_keys(text)\n                time.sleep(0.5)  # 输入后等待\n                return True\n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"输入文本失败: {e}\")\n            return False\n    \n    def get_page_source(self) -> str:\n        \"\"\"获取页面源码\n        \n        Returns:\n            页面源码\n        \"\"\"\n        try:\n            if not self.driver:\n                raise BrowserError(\"浏览器驱动未初始化\")\n            \n            return self.driver.page_source\n            \n        except Exception as e:\n            self.logger.error(f\"获取页面源码失败: {e}\")\n            return \"\"\n    \n    def get_current_url(self) -> str:\n        \"\"\"获取当前URL\n        \n        Returns:\n            当前URL\n        \"\"\"\n        try:\n            if not self.driver:\n                raise BrowserError(\"浏览器驱动未初始化\")\n            \n            return self.driver.current_url\n            \n        except Exception as e:\n            self.logger.error(f\"获取当前URL失败: {e}\")\n            return \"\"\n    \n    def execute_script(self, script: str) -> Any:\n        \"\"\"执行JavaScript脚本\n        \n        Args:\n            script: JavaScript代码\n            \n        Returns:\n            执行结果\n        \"\"\"\n        try:\n            if not self.driver:\n                raise BrowserError(\"浏览器驱动未初始化\")\n            \n            return self.driver.execute_script(script)\n            \n        except Exception as e:\n            self.logger.error(f\"执行脚本失败: {e}\")\n            return None\n    \n    def quit(self):\n        \"\"\"关闭浏览器\"\"\"\n        try:\n            if self.driver:\n                self.driver.quit()\n                self.driver = None\n                self.wait = None\n                self.logger.info(\"浏览器已关闭\")\n                \n        except Exception as e:\n            self.logger.error(f\"关闭浏览器失败: {e}\")\n    \n    def __enter__(self):\n        \"\"\"上下文管理器入口\"\"\"\n        return self\n    \n    def __exit__(self, exc_type, exc_val, exc_tb):\n        \"\"\"上下文管理器出口\"\"\"\n        self.quit()\n        return False\n"}
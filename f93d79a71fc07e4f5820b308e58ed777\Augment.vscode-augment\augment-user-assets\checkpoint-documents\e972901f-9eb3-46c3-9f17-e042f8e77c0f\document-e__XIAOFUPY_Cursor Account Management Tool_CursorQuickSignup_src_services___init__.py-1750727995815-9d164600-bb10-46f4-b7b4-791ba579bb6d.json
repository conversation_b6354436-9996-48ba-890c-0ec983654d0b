{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/services/__init__.py"}, "originalCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n业务服务模块\n\n提供各种业务逻辑的实现。\n\"\"\"\n\nfrom .cursor import CursorRegistrationService, CursorAccountManager, CursorAuthService\nfrom .windsurf import WindsurfRegistrationService, WindsurfAccountManager, WindsurfAuthService\nfrom .augment import AugmentResetService\n\n__all__ = [\n    'CursorRegistrationService',\n    'CursorAccountManager', \n    'CursorAuthService',\n    'WindsurfRegistrationService',\n    'WindsurfAccountManager',\n    'WindsurfAuthService',\n    'AugmentResetService'\n]\n"}
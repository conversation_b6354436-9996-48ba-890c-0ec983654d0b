{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/core/__init__.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n核心模块\n\n提供应用程序的核心功能，包括配置管理、常量定义、异常处理和日志系统。\n\"\"\"\n\nfrom .config import Config\nfrom .constants import *\nfrom .exceptions import *\nfrom .logger import setup_logger, get_logger\n\n__all__ = [\n    'Config',\n    'setup_logger',\n    'get_logger',\n    # 常量\n    'APP_NAME',\n    'APP_VERSION',\n    'DEFAULT_TIMEOUT',\n    # 异常\n    'AppError',\n    'ConfigError',\n    'ServiceError',\n    'ValidationError',\n]\n"}
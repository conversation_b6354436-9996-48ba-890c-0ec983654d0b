{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/services/augment_service.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nAugment服务\n\n提供Augment重置功能。\n\"\"\"\n\nimport os\nimport shutil\nimport sqlite3\nimport uuid\nimport platform\nfrom pathlib import Path\nfrom typing import Optional, List, Dict, Any, Callable\n\nfrom ..core.config import Config\nfrom ..core.logger import get_logger\nfrom ..core.exceptions import ResetError\n\n\nclass AugmentService:\n    \"\"\"Augment服务类\"\"\"\n    \n    def __init__(self, config: Config):\n        \"\"\"初始化Augment服务\n        \n        Args:\n            config: 配置对象\n        \"\"\"\n        self.config = config\n        self.logger = get_logger()\n        self.system = platform.system()\n    \n    def reset_augment(self, log_callback: Optional[Callable[[str], None]] = None) -> bool:\n        \"\"\"重置Augment\n        \n        Args:\n            log_callback: 日志回调函数\n            \n        Returns:\n            是否成功\n        \"\"\"\n        try:\n            self.logger.info(\"开始Augment重置流程\")\n            \n            if log_callback:\n                log_callback(\"🔄 开始Augment重置流程...\")\n            \n            # 1. 关闭VSCode进程\n            if not self._kill_vscode_processes(log_callback):\n                self.logger.warning(\"关闭VSCode进程失败，继续执行\")\n            \n            # 2. 重置VSCode数据库\n            if not self._reset_vscode_database(log_callback):\n                self.logger.warning(\"重置VSCode数据库失败\")\n            \n            # 3. 清理扩展数据\n            if not self._clean_extension_data(log_callback):\n                self.logger.warning(\"清理扩展数据失败\")\n            \n            # 4. 重置机器ID\n            if not self._reset_machine_id(log_callback):\n                self.logger.warning(\"重置机器ID失败\")\n            \n            if log_callback:\n                log_callback(\"✅ Augment重置完成\")\n            \n            self.logger.info(\"Augment重置完成\")\n            return True\n            \n        except Exception as e:\n            error_msg = f\"Augment重置失败: {e}\"\n            self.logger.error(error_msg)\n            if log_callback:\n                log_callback(f\"❌ {error_msg}\")\n            return False\n    \n    def _kill_vscode_processes(self, log_callback: Optional[Callable[[str], None]] = None) -> bool:\n        \"\"\"关闭VSCode进程\"\"\"\n        try:\n            if log_callback:\n                log_callback(\"🔄 关闭VSCode进程...\")\n            \n            if self.system == \"Windows\":\n                os.system(\"taskkill /f /im Code.exe 2>nul\")\n                os.system(\"taskkill /f /im code.exe 2>nul\")\n            else:\n                os.system(\"pkill -f code 2>/dev/null\")\n                os.system(\"pkill -f Code 2>/dev/null\")\n            \n            if log_callback:\n                log_callback(\"✅ VSCode进程已关闭\")\n            \n            return True\n            \n        except Exception as e:\n            self.logger.error(f\"关闭VSCode进程失败: {e}\")\n            return False\n    \n    def _reset_vscode_database(self, log_callback: Optional[Callable[[str], None]] = None) -> bool:\n        \"\"\"重置VSCode数据库\"\"\"\n        try:\n            if log_callback:\n                log_callback(\"🔄 重置VSCode数据库...\")\n            \n            # 查找VSCode数据库文件\n            db_paths = self._find_vscode_database_paths()\n            \n            for db_path in db_paths:\n                if db_path.exists():\n                    try:\n                        # 连接数据库并重置\n                        conn = sqlite3.connect(str(db_path))\n                        cursor = conn.cursor()\n                        \n                        # 生成新的服务机器ID\n                        new_service_machine_id = self._generate_service_machine_id()\n                        \n                        # 更新或插入storage.serviceMachineId\n                        cursor.execute(\"\"\"\n                            INSERT OR REPLACE INTO ItemTable (key, value)\n                            VALUES ('storage.serviceMachineId', ?)\n                        \"\"\", (new_service_machine_id,))\n                        \n                        # 清除遥测时间戳\n                        telemetry_keys = [\n                            'telemetry.firstSessionDate',\n                            'telemetry.lastSessionDate',\n                            'telemetry.currentSessionDate'\n                        ]\n                        \n                        for key in telemetry_keys:\n                            cursor.execute(\"DELETE FROM ItemTable WHERE key = ?\", (key,))\n                        \n                        # 重置新存储标记\n                        cursor.execute(\"\"\"\n                            INSERT OR REPLACE INTO ItemTable (key, value)\n                            VALUES ('__$__isNewStorageMarker', 'true')\n                        \"\"\")\n                        \n                        conn.commit()\n                        conn.close()\n                        \n                        if log_callback:\n                            log_callback(f\"✅ 已重置数据库: {db_path.name}\")\n                        \n                    except Exception as e:\n                        self.logger.error(f\"重置数据库失败 {db_path}: {e}\")\n            \n            return True\n            \n        except Exception as e:\n            self.logger.error(f\"重置VSCode数据库失败: {e}\")\n            return False\n    \n    def _find_vscode_database_paths(self) -> List[Path]:\n        \"\"\"查找VSCode数据库路径\"\"\"\n        paths = []\n        \n        if self.system == \"Windows\":\n            # Windows路径\n            appdata = Path(os.environ.get('APPDATA', ''))\n            possible_paths = [\n                appdata / \"Code\" / \"User\" / \"globalStorage\" / \"state.vscdb\",\n                appdata / \"Code - Insiders\" / \"User\" / \"globalStorage\" / \"state.vscdb\",\n            ]\n        elif self.system == \"Darwin\":\n            # macOS路径\n            home = Path.home()\n            possible_paths = [\n                home / \"Library\" / \"Application Support\" / \"Code\" / \"User\" / \"globalStorage\" / \"state.vscdb\",\n                home / \"Library\" / \"Application Support\" / \"Code - Insiders\" / \"User\" / \"globalStorage\" / \"state.vscdb\",\n            ]\n        else:\n            # Linux路径\n            home = Path.home()\n            possible_paths = [\n                home / \".config\" / \"Code\" / \"User\" / \"globalStorage\" / \"state.vscdb\",\n                home / \".config\" / \"Code - Insiders\" / \"User\" / \"globalStorage\" / \"state.vscdb\",\n            ]\n        \n        for path in possible_paths:\n            if path.exists():\n                paths.append(path)\n        \n        return paths\n    \n    def _generate_service_machine_id(self) -> str:\n        \"\"\"生成服务机器ID\"\"\"\n        import hashlib\n        import secrets\n        \n        # 生成随机数据\n        random_data = secrets.token_bytes(32)\n        \n        # 使用SHA-256生成64位十六进制字符串\n        hash_obj = hashlib.sha256(random_data)\n        return hash_obj.hexdigest()\n    \n    def _clean_extension_data(self, log_callback: Optional[Callable[[str], None]] = None) -> bool:\n        \"\"\"清理扩展数据\"\"\"\n        try:\n            if log_callback:\n                log_callback(\"🔄 清理扩展数据...\")\n            \n            # 查找扩展目录\n            extension_dirs = self._find_extension_dirs()\n            \n            for ext_dir in extension_dirs:\n                if ext_dir.exists():\n                    try:\n                        # 查找Augment相关扩展\n                        augment_dirs = []\n                        for item in ext_dir.iterdir():\n                            if item.is_dir() and 'augment' in item.name.lower():\n                                augment_dirs.append(item)\n                        \n                        # 清理Augment扩展数据\n                        for augment_dir in augment_dirs:\n                            # 清理工作区状态\n                            workspace_state = augment_dir / \"workspaceStorage\"\n                            if workspace_state.exists():\n                                shutil.rmtree(workspace_state, ignore_errors=True)\n                            \n                            # 清理全局状态\n                            global_state = augment_dir / \"globalStorage\"\n                            if global_state.exists():\n                                shutil.rmtree(global_state, ignore_errors=True)\n                        \n                        if log_callback:\n                            log_callback(f\"✅ 已清理扩展目录: {ext_dir.name}\")\n                        \n                    except Exception as e:\n                        self.logger.error(f\"清理扩展目录失败 {ext_dir}: {e}\")\n            \n            return True\n            \n        except Exception as e:\n            self.logger.error(f\"清理扩展数据失败: {e}\")\n            return False\n    \n    def _find_extension_dirs(self) -> List[Path]:\n        \"\"\"查找扩展目录\"\"\"\n        dirs = []\n        \n        if self.system == \"Windows\":\n            appdata = Path(os.environ.get('APPDATA', ''))\n            possible_dirs = [\n                appdata / \"Code\" / \"User\" / \"workspaceStorage\",\n                appdata / \"Code - Insiders\" / \"User\" / \"workspaceStorage\",\n            ]\n        elif self.system == \"Darwin\":\n            home = Path.home()\n            possible_dirs = [\n                home / \"Library\" / \"Application Support\" / \"Code\" / \"User\" / \"workspaceStorage\",\n                home / \"Library\" / \"Application Support\" / \"Code - Insiders\" / \"User\" / \"workspaceStorage\",\n            ]\n        else:\n            home = Path.home()\n            possible_dirs = [\n                home / \".config\" / \"Code\" / \"User\" / \"workspaceStorage\",\n                home / \".config\" / \"Code - Insiders\" / \"User\" / \"workspaceStorage\",\n            ]\n        \n        for dir_path in possible_dirs:\n            if dir_path.exists():\n                dirs.append(dir_path)\n        \n        return dirs\n    \n    def _reset_machine_id(self, log_callback: Optional[Callable[[str], None]] = None) -> bool:\n        \"\"\"重置机器ID\"\"\"\n        try:\n            if log_callback:\n                log_callback(\"🔄 重置机器ID...\")\n            \n            # 查找机器ID文件\n            machine_id_files = self._find_machine_id_files()\n            \n            for file_path in machine_id_files:\n                if file_path.exists():\n                    try:\n                        # 生成新的机器ID\n                        new_machine_id = str(uuid.uuid4())\n                        \n                        # 写入新的机器ID\n                        with open(file_path, 'w', encoding='utf-8') as f:\n                            f.write(new_machine_id)\n                        \n                        if log_callback:\n                            log_callback(f\"✅ 已重置机器ID: {file_path.name}\")\n                        \n                    except Exception as e:\n                        self.logger.error(f\"重置机器ID文件失败 {file_path}: {e}\")\n            \n            return True\n            \n        except Exception as e:\n            self.logger.error(f\"重置机器ID失败: {e}\")\n            return False\n    \n    def _find_machine_id_files(self) -> List[Path]:\n        \"\"\"查找机器ID文件\"\"\"\n        files = []\n        \n        if self.system == \"Windows\":\n            appdata = Path(os.environ.get('APPDATA', ''))\n            possible_files = [\n                appdata / \"Code\" / \"machineid\",\n                appdata / \"Code - Insiders\" / \"machineid\",\n            ]\n        elif self.system == \"Darwin\":\n            home = Path.home()\n            possible_files = [\n                home / \"Library\" / \"Application Support\" / \"Code\" / \"machineid\",\n                home / \"Library\" / \"Application Support\" / \"Code - Insiders\" / \"machineid\",\n            ]\n        else:\n            home = Path.home()\n            possible_files = [\n                home / \".config\" / \"Code\" / \"machineid\",\n                home / \".config\" / \"Code - Insiders\" / \"machineid\",\n            ]\n        \n        for file_path in possible_files:\n            if file_path.exists():\n                files.append(file_path)\n        \n        return files\n"}
{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src\\services\\windsurf\\registration.py"}, "originalCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nWindsurf注册服务\n\n提供Windsurf账号注册的业务逻辑。\n\"\"\"\n\nimport os\nimport sys\nimport time\nimport random\nimport re\nfrom enum import Enum\nfrom typing import Optional, Tuple, Dict, Any\nfrom datetime import datetime\n\nfrom ...core import Config, info, error, warning, debug, get_database\nfrom ...models import WindsurfAccount, RegistrationResponse\nfrom ...utils import BrowserManager, WindsurfEmailHandler\nfrom ...core.exceptions import RegistrationError, BrowserError, EmailError\n\n\n# 常量定义\nSIGN_UP_URL = \"https://windsurf.com/account/register\"\nTOKEN_URL = \"https://windsurf.com/editor/signin?response_type=token&redirect_uri=show-auth-token\"\nONBOARDING_URL = \"https://windsurf.com/account/onboarding\"\nDEFAULT_USER_AGENT = \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"\n\n\nclass VerificationStatus(Enum):\n    \"\"\"验证状态枚举\"\"\"\n    PASSWORD_PAGE = \"Continue\"\n    CAPTCHA_PAGE = \"@data-index=0\"\n    ACCOUNT_SETTINGS = \"Account Settings\"\n\n\nclass TurnstileError(Exception):\n    \"\"\"Turnstile 验证相关异常\"\"\"\n    pass\n\n\nclass WindsurfRegistrationService:\n    \"\"\"Windsurf注册服务类\"\"\"\n    \n    def __init__(self, config: Optional[Config] = None):\n        \"\"\"初始化注册服务\n        \n        Args:\n            config: 配置实例，为None时使用默认配置\n        \"\"\"\n        self.config = config or Config()\n        self.database = get_database()\n        self.browser_manager: Optional[BrowserManager] = None\n        self.email_handler: Optional[WindsurfEmailHandler] = None\n    \n    def register_account(self, config_index: int = 0) -> RegistrationResponse:\n        \"\"\"注册单个账号\n\n        Args:\n            config_index: 配置索引\n\n        Returns:\n            RegistrationResponse: 注册结果\n        \"\"\"\n        try:\n            info(\"开始Windsurf账号注册流程\")\n\n            # 切换到指定配置\n            if not self.config.switch_config(config_index):\n                return RegistrationResponse.error_response(\"切换配置失败\")\n\n            # 初始化浏览器管理器\n            self.browser_manager = BrowserManager()\n\n            # 获取真实User-Agent\n            user_agent = self._get_real_user_agent()\n\n            # 初始化浏览器\n            browser = self.browser_manager.init_browser(user_agent, self.config.browser_headless)\n\n            # 初始化邮箱处理器\n            self.email_handler = WindsurfEmailHandler(self.config)\n\n            # 生成账号信息\n            email = self.email_handler.generate_email()\n            password = self.email_handler.generate_password()\n            first_name = self._generate_random_name()\n            last_name = self._generate_random_name()\n\n            info(f\"生成的邮箱: {email}\")\n            info(f\"生成的姓名: {first_name} {last_name}\")\n\n            # 执行注册流程\n            success, result_info = self._sign_up_account(\n                browser, browser.latest_tab, email, password,\n                first_name, last_name, SIGN_UP_URL\n            )\n\n            if success:\n                # 构建账号信息\n                account_data = {\n                    \"email\": email,\n                    \"password\": password,\n                    \"first_name\": first_name,\n                    \"last_name\": last_name,\n                    \"token\": result_info if isinstance(result_info, str) else None,\n                    \"timestamp\": datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n                }\n\n                # 保存账号到数据库\n                windsurf_account = WindsurfAccount(\n                    email=email,\n                    password=password,\n                    token=account_data.get(\"token\", \"\"),\n                    status=\"active\"\n                )\n\n                self.database.add_windsurf_account(windsurf_account.to_dict())\n\n                return RegistrationResponse.success_response(\n                    email=email,\n                    token=account_data.get(\"token\", \"\"),\n                    user_id=\"\",\n                    account_data=account_data\n                )\n            else:\n                error_msg = result_info or \"注册失败\"\n                return RegistrationResponse.error_response(error_msg)\n\n        except Exception as e:\n            error(f\"Windsurf账号注册失败: {e}\")\n            return RegistrationResponse.error_response(f\"注册失败: {e}\")\n        finally:\n            self._cleanup()\n\n    def _get_real_user_agent(self) -> str:\n        \"\"\"获取真实User-Agent，剔除Headless标记\n\n        Returns:\n            str: 处理后的User-Agent字符串\n        \"\"\"\n        try:\n            info(\"正在获取真实User-Agent...\")\n            temp_manager = BrowserManager()\n            try:\n                temp_browser = temp_manager.init_browser(headless_value=self.config.browser_headless)\n                user_agent = temp_browser.latest_tab.run_js(\"return navigator.userAgent\")\n                info(\"成功获取User-Agent\")\n            except Exception as e:\n                error(f\"获取User-Agent时出错: {e}\")\n                user_agent = DEFAULT_USER_AGENT\n            finally:\n                try:\n                    time.sleep(0.5)\n                    temp_manager.quit()\n                    info(\"临时浏览器资源已释放\")\n                except Exception as e:\n                    error(f\"释放临时浏览器资源时出错: {e}\")\n\n            if not user_agent:\n                user_agent = DEFAULT_USER_AGENT\n            return user_agent.replace(\"HeadlessChrome\", \"Chrome\")\n        except Exception as e:\n            error(f\"获取user agent失败: {e}\")\n            return DEFAULT_USER_AGENT\n\n    def _generate_random_name(self, length: int = 6) -> str:\n        \"\"\"生成随机用户名\n\n        Args:\n            length: 用户名长度\n\n        Returns:\n            str: 随机生成的用户名\n        \"\"\"\n        first_letter = random.choice(\"ABCDEFGHIJKLMNOPQRSTUVWXYZ\")\n        rest_letters = \"\".join(\n            random.choices(\"abcdefghijklmnopqrstuvwxyz\", k=length - 1)\n        )\n        return first_letter + rest_letters\n\n    def _sign_up_account(self, browser, tab, account: str, password: str,\n                        first_name: str, last_name: str, sign_up_url: str) -> Tuple[bool, Optional[str]]:\n        \"\"\"注册Windsurf账号，自动处理表单、验证码、人机验证等流程\n\n        Args:\n            browser: 浏览器对象\n            tab: 浏览器标签页对象\n            account: 邮箱账号\n            password: 密码\n            first_name: 名字\n            last_name: 姓氏\n            sign_up_url: 注册页面URL\n\n        Returns:\n            Tuple[bool, Optional[str]]: (是否成功, Token或错误信息)\n        \"\"\"\n        info(\"=== 开始注册账号流程 ===\")\n        info(f\"正在访问注册页面: {sign_up_url}\")\n        tab.get(sign_up_url)\n        time.sleep(2)\n\n        try:\n            if tab.ele(\"Sign up\"):\n                info(\"成功进入注册页面\")\n                info(\"正在填写个人信息...\")\n\n                # 填写个人信息\n                tab.ele(\"@placeholder=Your first name\").input(first_name)\n                info(f\"已输入名字: {first_name}\")\n\n                tab.ele(\"@placeholder=Your last name\").input(last_name)\n                info(f\"已输入姓氏: {last_name}\")\n\n                tab.ele(\"@placeholder=Your email address\").input(account)\n                info(f\"已输入邮箱: {account}\")\n\n                tab.ele(\"@placeholder=Your password\").input(password)\n                info(f\"已输入密码\")\n\n                # 处理Turnstile验证\n                info(\"=== 开始处理Turnstile验证 ===\")\n                if not self._handle_turnstile(tab):\n                    error(\"Turnstile验证失败\")\n                    return False, \"Turnstile验证失败\"\n\n                # 点击注册按钮\n                info(\"正在点击注册按钮...\")\n                signup_button = tab.ele(\"@type=submit\")\n                if signup_button:\n                    signup_button.click()\n                    info(\"已点击注册按钮\")\n                    time.sleep(3)\n                else:\n                    error(\"未找到注册按钮\")\n                    return False, \"未找到注册按钮\"\n\n                # 处理邮箱验证\n                info(\"=== 开始邮箱验证流程 ===\")\n                verification_success = self._handle_email_verification(tab, account)\n\n                if verification_success:\n                    # 尝试获取Token\n                    info(\"=== 开始获取Token ===\")\n                    token = self._get_windsurf_token(browser, tab)\n\n                    if token:\n                        info(\"Windsurf账号注册成功，已获取Token\")\n                        return True, token\n                    else:\n                        info(\"Windsurf账号注册成功，但未获取到Token\")\n                        return True, None\n                else:\n                    error(\"邮箱验证失败\")\n                    return False, \"邮箱验证失败\"\n            else:\n                error(\"未检测到注册页面元素\")\n                return False, \"未找到注册页面元素\"\n\n        except Exception as e:\n            error(f\"注册过程发生错误: {str(e)}\")\n            import traceback\n            error(traceback.format_exc())\n            return False, \"注册过程发生错误\"\n\n    def _handle_turnstile(self, tab, max_retries: int = 3, retry_interval: tuple = (1, 2)) -> bool:\n        \"\"\"处理 Turnstile 人机验证\n\n        Args:\n            tab: 浏览器标签页对象\n            max_retries: 最大重试次数\n            retry_interval: 重试间隔时间范围(秒)\n\n        Returns:\n            bool: 验证是否成功\n        \"\"\"\n        info(\"正在检测 Turnstile 验证...\")\n        retry_count = 0\n\n        try:\n            while retry_count < max_retries:\n                retry_count += 1\n                debug(f\"第 {retry_count} 次尝试验证\")\n\n                try:\n                    # 查找Turnstile验证框\n                    challenge_check = (\n                        tab.ele(\"@id=cf-turnstile\", timeout=2)\n                        .child()\n                        .shadow_root.ele(\"tag:iframe\")\n                        .ele(\"tag:body\")\n                        .sr(\"tag:input\")\n                    )\n                    if challenge_check:\n                        info(\"检测到 Turnstile 验证框，开始处理...\")\n                        time.sleep(random.uniform(1, 3))\n                        challenge_check.click()\n                        time.sleep(2)\n\n                        # 检查是否验证成功\n                        if self._check_turnstile_success(tab):\n                            info(\"Turnstile 验证通过\")\n                            return True\n\n                except Exception as e:\n                    debug(f\"当前尝试未成功: {e}\")\n\n                # 检查是否已经验证成功\n                if self._check_turnstile_success(tab):\n                    info(\"验证成功\")\n                    return True\n\n                # 随机延时后继续下一次尝试\n                time.sleep(random.uniform(*retry_interval))\n\n            # 超出最大重试次数\n            error(f\"验证失败 - 已达到最大重试次数 {max_retries}\")\n            return False\n\n        except Exception as e:\n            error(f\"Turnstile 验证过程发生异常: {e}\")\n            return False\n\n    def _check_turnstile_success(self, tab) -> bool:\n        \"\"\"检查Turnstile验证是否成功\n\n        Args:\n            tab: 浏览器标签页对象\n\n        Returns:\n            bool: 验证是否成功\n        \"\"\"\n        try:\n            # 检查是否存在提交按钮且可点击\n            submit_button = tab.ele(\"@type=submit\", timeout=1)\n            if submit_button and submit_button.states.is_enabled:\n                return True\n\n            # 检查是否出现错误消息\n            error_messages = [\n                'xpath://div[contains(text(), \"Can\\'t verify the user is human\")]',\n                'xpath://div[contains(text(), \"Error: 600010\")]',\n                'xpath://div[contains(text(), \"Please try again\")]'\n            ]\n\n            for error_xpath in error_messages:\n                if tab.ele(error_xpath, timeout=0.5):\n                    warning(f\"检测到验证错误: {error_xpath}\")\n                    return False\n\n            return False\n        except Exception as e:\n            debug(f\"验证状态检查失败: {e}\")\n            return False\n\n    def _handle_email_verification(self, tab, email: str, timeout: int = 300) -> bool:\n        \"\"\"处理邮箱验证\n\n        Args:\n            tab: 浏览器标签页对象\n            email: 邮箱地址\n            timeout: 超时时间（秒）\n\n        Returns:\n            bool: 验证是否成功\n        \"\"\"\n        try:\n            info(\"正在等待邮箱验证页面...\")\n\n            # 等待邮箱验证页面出现\n            verification_input = tab.ele(\"@data-index=0\", timeout=10)\n            if not verification_input:\n                # 检查是否已经跳转到其他页面（可能验证已完成）\n                if \"onboarding\" in tab.url or \"dashboard\" in tab.url:\n                    info(\"已跳转到后续页面，邮箱验证可能已完成\")\n                    return True\n\n                error(\"未找到邮箱验证输入框\")\n                return False\n\n            info(\"已到达邮箱验证页面，开始获取验证码...\")\n\n            # 获取验证码\n            verification_code = self.email_handler.get_windsurf_verification_code(email, timeout)\n\n            if not verification_code:\n                error(\"获取验证码失败\")\n                return False\n\n            info(f\"成功获取验证码: {verification_code}\")\n\n            # 输入验证码\n            for i, digit in enumerate(verification_code):\n                digit_input = tab.ele(f\"@data-index={i}\")\n                if digit_input:\n                    digit_input.input(digit)\n                    time.sleep(0.2)\n                else:\n                    error(f\"未找到第{i+1}位验证码输入框\")\n                    return False\n\n            info(\"验证码输入完成，等待验证...\")\n            time.sleep(3)\n\n            # 检查是否验证成功\n            if \"onboarding\" in tab.url or \"dashboard\" in tab.url:\n                info(\"邮箱验证成功，已跳转到后续页面\")\n                return True\n            else:\n                warning(\"验证码输入后未检测到成功页面\")\n                return False\n\n        except Exception as e:\n            error(f\"邮箱验证过程出错: {e}\")\n            return False\n\n    def _get_windsurf_token(self, browser, tab) -> Optional[str]:\n        \"\"\"获取Windsurf Token\n\n        Args:\n            browser: 浏览器对象\n            tab: 浏览器标签页对象\n\n        Returns:\n            Optional[str]: Token字符串，失败时返回None\n        \"\"\"\n        try:\n            info(\"正在获取Windsurf Token...\")\n\n            # 跳转到Token获取页面\n            info(f\"正在跳转到Token页面: {TOKEN_URL}\")\n            tab.get(TOKEN_URL)\n            time.sleep(5)\n\n            # 检查是否成功到达Token页面\n            if \"show-auth-token\" in tab.url:\n                info(\"已到达Token页面，开始提取Token...\")\n\n                # 尝试从页面JavaScript获取Token\n                token_js = \"\"\"\n                try {\n                    // 尝试从URL hash获取token\n                    const hash = window.location.hash;\n                    if (hash && hash.includes('access_token=')) {\n                        const match = hash.match(/access_token=([^&]+)/);\n                        if (match) return match[1];\n                    }\n\n                    // 尝试从页面元素获取token\n                    const inputs = document.querySelectorAll('input[type=\"text\"], input[value*=\"eyJ\"]');\n                    for (let input of inputs) {\n                        if (input.value && input.value.length > 40) {\n                            return input.value;\n                        }\n                    }\n\n                    // 尝试从localStorage获取\n                    const token = localStorage.getItem('auth_token') ||\n                                 localStorage.getItem('access_token') ||\n                                 localStorage.getItem('windsurf_token');\n                    if (token) return token;\n\n                    return null;\n                } catch (e) {\n                    return null;\n                }\n                \"\"\"\n\n                token = tab.run_js(token_js)\n\n                if token and len(token) > 40:\n                    info(\"成功获取到Token\")\n                    return token\n                else:\n                    warning(\"未能通过JavaScript获取Token，尝试备用方法...\")\n\n                    # 备用方法：从页面元素获取\n                    elements = tab.eles(\"tag:input\") or tab.eles(\"@type=text\")\n                    if elements:\n                        for element in elements:\n                            value = element.get_attribute(\"value\")\n                            if value and len(value) > 40:\n                                info(f\"从输入框获取到Token\")\n                                return value\n\n                    warning(\"所有Token获取方法都失败了\")\n                    return None\n            else:\n                warning(\"未能到达Token页面\")\n                return None\n\n        except Exception as e:\n            error(f\"获取Token过程出错: {e}\")\n            return None\n\n    def _cleanup(self):\n        \"\"\"清理资源\"\"\"\n        if self.browser_manager:\n            try:\n                self.browser_manager.quit()\n                info(\"浏览器已关闭\")\n            except Exception as e:\n                warning(f\"关闭浏览器时出错: {e}\")\n            finally:\n                self.browser_manager = None\n\n        if self.email_handler:\n            try:\n                self.email_handler.cleanup()\n            except Exception as e:\n                warning(f\"清理邮箱处理器时出错: {e}\")\n            finally:\n                self.email_handler = None\n    \n    def register_multiple_accounts(self, count: int, use_random_config: bool = False) -> Dict[str, Any]:\n        \"\"\"批量注册账号\n        \n        Args:\n            count: 注册数量\n            use_random_config: 是否使用随机配置\n            \n        Returns:\n            Dict[str, Any]: 批量注册结果\n        \"\"\"\n        results = {\n            \"total\": count,\n            \"success\": 0,\n            \"failed\": 0,\n            \"accounts\": [],\n            \"errors\": []\n        }\n        \n        # 保存原始配置索引\n        original_config_index = self.config.get_current_config_index()\n        \n        try:\n            for i in range(count):\n                info(f\"开始第 {i+1}/{count} 次Windsurf注册\")\n                \n                # 选择配置\n                config_index = self._select_config_index(use_random_config, original_config_index)\n                \n                # 注册账号\n                result = self.register_account(config_index)\n                \n                if result.success:\n                    results[\"success\"] += 1\n                    results[\"accounts\"].append({\n                        \"email\": result.email,\n                        \"token\": result.token,\n                        \"config_index\": config_index\n                    })\n                    info(f\"第 {i+1} 次Windsurf注册成功: {result.email}\")\n                else:\n                    results[\"failed\"] += 1\n                    results[\"errors\"].append({\n                        \"attempt\": i + 1,\n                        \"error\": result.message,\n                        \"config_index\": config_index\n                    })\n                    error(f\"第 {i+1} 次Windsurf注册失败: {result.message}\")\n                \n                # 等待间隔（除了最后一次）\n                if i < count - 1:\n                    wait_time = random.randint(5, 10)  # Windsurf可能需要更长等待时间\n                    info(f\"等待 {wait_time} 秒后继续...\")\n                    time.sleep(wait_time)\n            \n        finally:\n            # 恢复原始配置\n            if use_random_config:\n                self.config.switch_config(original_config_index)\n        \n        return results\n    \n    def _init_browser(self):\n        \"\"\"初始化浏览器\"\"\"\n        try:\n            self.browser_manager = BrowserManager()\n            user_agent = self._get_real_user_agent()\n            self.browser_manager.init_browser(user_agent, self.config.browser_headless)\n            info(\"浏览器初始化成功\")\n        except Exception as e:\n            raise BrowserError(f\"浏览器初始化失败: {e}\")\n    \n    def _init_email_handler(self, config_index: int):\n        \"\"\"初始化邮箱处理器\n        \n        Args:\n            config_index: 配置索引\n        \"\"\"\n        try:\n            self.email_handler = EmailHandler(config_index)\n            info(\"邮箱处理器初始化成功\")\n        except Exception as e:\n            raise EmailError(f\"邮箱处理器初始化失败: {e}\")\n    \n    def _generate_account_info(self) -> Dict[str, str]:\n        \"\"\"生成账号信息\n        \n        Returns:\n            Dict[str, str]: 账号信息\n        \"\"\"\n        # 这里应该调用邮箱生成器\n        # 暂时返回模拟数据\n        return {\n            \"email\": f\"windsurf_{int(time.time())}@example.com\",\n            \"password\": \"WindsurfPassword123!\",\n            \"first_name\": \"Windsurf\",\n            \"last_name\": \"User\"\n        }\n    \n    def _perform_registration(self, account_info: Dict[str, str]) -> RegistrationResponse:\n        \"\"\"执行注册流程\n        \n        Args:\n            account_info: 账号信息\n            \n        Returns:\n            RegistrationResponse: 注册结果\n        \"\"\"\n        try:\n            # 这里应该实现具体的Windsurf注册逻辑\n            # 包括：\n            # 1. 访问Windsurf注册页面\n            # 2. 填写注册信息\n            # 3. 处理人机验证\n            # 4. 处理邮箱验证码\n            # 5. 获取Token\n            \n            info(\"模拟Windsurf注册流程...\")\n            time.sleep(3)  # 模拟注册时间\n            \n            return RegistrationResponse.success_response(\n                email=account_info[\"email\"],\n                token=f\"windsurf_token_{int(time.time())}\",\n                user_id=f\"windsurf_user_{int(time.time())}\",\n                account_data=account_info\n            )\n            \n        except Exception as e:\n            return RegistrationResponse.error_response(f\"Windsurf注册流程失败: {e}\")\n    \n    def _save_account(self, result: RegistrationResponse):\n        \"\"\"保存账号到数据库\n        \n        Args:\n            result: 注册结果\n        \"\"\"\n        try:\n            account = WindsurfAccount(\n                email=result.email,\n                token=result.token,\n                user_id=result.user_id,\n                password=result.account_data.get(\"password\", \"\") if result.account_data else \"\",\n                status=\"active\"\n            )\n            \n            self.database.add_windsurf_account(account.to_dict())\n            \n        except Exception as e:\n            warning(f\"保存Windsurf账号到数据库失败: {e}\")\n    \n    def _select_config_index(self, use_random_config: bool, original_index: int) -> int:\n        \"\"\"选择配置索引\n        \n        Args:\n            use_random_config: 是否使用随机配置\n            original_index: 原始配置索引\n            \n        Returns:\n            int: 配置索引\n        \"\"\"\n        if not use_random_config:\n            return original_index\n        \n        config_count = self.config.get_config_count()\n        if config_count <= 1:\n            return original_index\n        \n        # 随机选择一个不同的配置\n        available_indices = list(range(config_count))\n        if original_index in available_indices and len(available_indices) > 1:\n            available_indices.remove(original_index)\n        \n        return random.choice(available_indices)\n    \n    def _get_real_user_agent(self) -> str:\n        \"\"\"获取真实的User-Agent\n        \n        Returns:\n            str: User-Agent字符串\n        \"\"\"\n        # 这里应该实现获取真实User-Agent的逻辑\n        # 暂时返回默认值\n        return \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"\n    \n    def _cleanup(self):\n        \"\"\"清理资源\"\"\"\n        if self.browser_manager:\n            try:\n                self.browser_manager.quit()\n                info(\"浏览器已关闭\")\n            except Exception as e:\n                warning(f\"关闭浏览器时出错: {e}\")\n            finally:\n                self.browser_manager = None\n        \n        self.email_handler = None\n\n\n# 便捷函数\ndef windsurf_quick_signup_process(config: Config, config_index: int = 0) -> Tuple[bool, Dict[str, Any], Optional[BrowserManager]]:\n    \"\"\"Windsurf快速注册流程（兼容旧接口）\n    \n    Args:\n        config: 配置实例\n        config_index: 配置索引\n        \n    Returns:\n        Tuple[bool, Dict[str, Any], Optional[BrowserManager]]: (成功状态, 账号数据, 浏览器管理器)\n    \"\"\"\n    service = WindsurfRegistrationService(config)\n    result = service.register_account(config_index)\n    \n    if result.success:\n        account_data = {\n            \"email\": result.email,\n            \"token\": result.token,\n            \"user_id\": result.user_id\n        }\n        if result.account_data:\n            account_data.update(result.account_data)\n        \n        return True, account_data, service.browser_manager\n    else:\n        return False, {\"error\": result.message}, service.browser_manager\n", "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nWindsurf注册服务\n\n提供Windsurf账号注册的业务逻辑。\n\"\"\"\n\nimport os\nimport sys\nimport time\nimport random\nimport re\nfrom enum import Enum\nfrom typing import Optional, Tuple, Dict, Any\nfrom datetime import datetime\n\nfrom ...core import Config, info, error, warning, debug, get_database\nfrom ...models import WindsurfAccount, RegistrationResponse\nfrom ...utils import BrowserManager, WindsurfEmailHandler\nfrom ...core.exceptions import RegistrationError, BrowserError, EmailError\n\n\n# 常量定义\nSIGN_UP_URL = \"https://windsurf.com/account/register\"\nTOKEN_URL = \"https://windsurf.com/editor/signin?response_type=token&redirect_uri=show-auth-token\"\nONBOARDING_URL = \"https://windsurf.com/account/onboarding\"\nDEFAULT_USER_AGENT = \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"\n\n\nclass VerificationStatus(Enum):\n    \"\"\"验证状态枚举\"\"\"\n    PASSWORD_PAGE = \"Continue\"\n    CAPTCHA_PAGE = \"@data-index=0\"\n    ACCOUNT_SETTINGS = \"Account Settings\"\n\n\nclass TurnstileError(Exception):\n    \"\"\"Turnstile 验证相关异常\"\"\"\n    pass\n\n\nclass WindsurfRegistrationService:\n    \"\"\"Windsurf注册服务类\"\"\"\n    \n    def __init__(self, config: Optional[Config] = None):\n        \"\"\"初始化注册服务\n        \n        Args:\n            config: 配置实例，为None时使用默认配置\n        \"\"\"\n        self.config = config or Config()\n        self.database = get_database()\n        self.browser_manager: Optional[BrowserManager] = None\n        self.email_handler: Optional[WindsurfEmailHandler] = None\n    \n    def register_account(self, config_index: int = 0) -> RegistrationResponse:\n        \"\"\"注册单个账号\n\n        Args:\n            config_index: 配置索引\n\n        Returns:\n            RegistrationResponse: 注册结果\n        \"\"\"\n        try:\n            info(\"开始Windsurf账号注册流程\")\n\n            # 切换到指定配置\n            if not self.config.switch_config(config_index):\n                return RegistrationResponse.error_response(\"切换配置失败\")\n\n            # 初始化浏览器管理器\n            self.browser_manager = BrowserManager()\n\n            # 获取真实User-Agent\n            user_agent = self._get_real_user_agent()\n\n            # 初始化浏览器\n            browser = self.browser_manager.init_browser(user_agent, self.config.browser_headless)\n\n            # 初始化邮箱处理器\n            self.email_handler = WindsurfEmailHandler(self.config)\n\n            # 生成账号信息\n            email = self.email_handler.generate_email()\n            password = self.email_handler.generate_password()\n            first_name = self._generate_random_name()\n            last_name = self._generate_random_name()\n\n            info(f\"生成的邮箱: {email}\")\n            info(f\"生成的姓名: {first_name} {last_name}\")\n\n            # 执行注册流程\n            success, result_info = self._sign_up_account(\n                browser, browser.latest_tab, email, password,\n                first_name, last_name, SIGN_UP_URL\n            )\n\n            if success:\n                # 构建账号信息\n                account_data = {\n                    \"email\": email,\n                    \"password\": password,\n                    \"first_name\": first_name,\n                    \"last_name\": last_name,\n                    \"token\": result_info if isinstance(result_info, str) else None,\n                    \"timestamp\": datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n                }\n\n                # 保存账号到数据库\n                windsurf_account = WindsurfAccount(\n                    email=email,\n                    password=password,\n                    token=account_data.get(\"token\", \"\"),\n                    status=\"active\"\n                )\n\n                self.database.add_windsurf_account(windsurf_account.to_dict())\n\n                return RegistrationResponse.success_response(\n                    email=email,\n                    token=account_data.get(\"token\", \"\"),\n                    user_id=\"\",\n                    account_data=account_data\n                )\n            else:\n                error_msg = result_info or \"注册失败\"\n                return RegistrationResponse.error_response(error_msg)\n\n        except Exception as e:\n            error(f\"Windsurf账号注册失败: {e}\")\n            return RegistrationResponse.error_response(f\"注册失败: {e}\")\n        finally:\n            self._cleanup()\n\n    def _get_real_user_agent(self) -> str:\n        \"\"\"获取真实User-Agent，剔除Headless标记\n\n        Returns:\n            str: 处理后的User-Agent字符串\n        \"\"\"\n        try:\n            info(\"正在获取真实User-Agent...\")\n            temp_manager = BrowserManager()\n            try:\n                temp_browser = temp_manager.init_browser(headless_value=self.config.browser_headless)\n                user_agent = temp_browser.latest_tab.run_js(\"return navigator.userAgent\")\n                info(\"成功获取User-Agent\")\n            except Exception as e:\n                error(f\"获取User-Agent时出错: {e}\")\n                user_agent = DEFAULT_USER_AGENT\n            finally:\n                try:\n                    time.sleep(0.5)\n                    temp_manager.quit()\n                    info(\"临时浏览器资源已释放\")\n                except Exception as e:\n                    error(f\"释放临时浏览器资源时出错: {e}\")\n\n            if not user_agent:\n                user_agent = DEFAULT_USER_AGENT\n            return user_agent.replace(\"HeadlessChrome\", \"Chrome\")\n        except Exception as e:\n            error(f\"获取user agent失败: {e}\")\n            return DEFAULT_USER_AGENT\n\n    def _generate_random_name(self, length: int = 6) -> str:\n        \"\"\"生成随机用户名\n\n        Args:\n            length: 用户名长度\n\n        Returns:\n            str: 随机生成的用户名\n        \"\"\"\n        first_letter = random.choice(\"ABCDEFGHIJKLMNOPQRSTUVWXYZ\")\n        rest_letters = \"\".join(\n            random.choices(\"abcdefghijklmnopqrstuvwxyz\", k=length - 1)\n        )\n        return first_letter + rest_letters\n\n    def _sign_up_account(self, browser, tab, account: str, password: str,\n                        first_name: str, last_name: str, sign_up_url: str) -> Tuple[bool, Optional[str]]:\n        \"\"\"注册Windsurf账号，自动处理表单、验证码、人机验证等流程\n\n        Args:\n            browser: 浏览器对象\n            tab: 浏览器标签页对象\n            account: 邮箱账号\n            password: 密码\n            first_name: 名字\n            last_name: 姓氏\n            sign_up_url: 注册页面URL\n\n        Returns:\n            Tuple[bool, Optional[str]]: (是否成功, Token或错误信息)\n        \"\"\"\n        info(\"=== 开始注册账号流程 ===\")\n        info(f\"正在访问注册页面: {sign_up_url}\")\n        tab.get(sign_up_url)\n        time.sleep(2)\n\n        try:\n            if tab.ele(\"Sign up\"):\n                info(\"成功进入注册页面\")\n                info(\"正在填写个人信息...\")\n\n                # 填写个人信息\n                tab.ele(\"@placeholder=Your first name\").input(first_name)\n                info(f\"已输入名字: {first_name}\")\n\n                tab.ele(\"@placeholder=Your last name\").input(last_name)\n                info(f\"已输入姓氏: {last_name}\")\n\n                tab.ele(\"@placeholder=Your email address\").input(account)\n                info(f\"已输入邮箱: {account}\")\n\n                tab.ele(\"@placeholder=Your password\").input(password)\n                info(f\"已输入密码\")\n\n                # 处理Turnstile验证\n                info(\"=== 开始处理Turnstile验证 ===\")\n                if not self._handle_turnstile(tab):\n                    error(\"Turnstile验证失败\")\n                    return False, \"Turnstile验证失败\"\n\n                # 点击注册按钮\n                info(\"正在点击注册按钮...\")\n                signup_button = tab.ele(\"@type=submit\")\n                if signup_button:\n                    signup_button.click()\n                    info(\"已点击注册按钮\")\n                    time.sleep(3)\n                else:\n                    error(\"未找到注册按钮\")\n                    return False, \"未找到注册按钮\"\n\n                # 处理邮箱验证\n                info(\"=== 开始邮箱验证流程 ===\")\n                verification_success = self._handle_email_verification(tab, account)\n\n                if verification_success:\n                    # 尝试获取Token\n                    info(\"=== 开始获取Token ===\")\n                    token = self._get_windsurf_token(browser, tab)\n\n                    if token:\n                        info(\"Windsurf账号注册成功，已获取Token\")\n                        return True, token\n                    else:\n                        info(\"Windsurf账号注册成功，但未获取到Token\")\n                        return True, None\n                else:\n                    error(\"邮箱验证失败\")\n                    return False, \"邮箱验证失败\"\n            else:\n                error(\"未检测到注册页面元素\")\n                return False, \"未找到注册页面元素\"\n\n        except Exception as e:\n            error(f\"注册过程发生错误: {str(e)}\")\n            import traceback\n            error(traceback.format_exc())\n            return False, \"注册过程发生错误\"\n\n    def _handle_turnstile(self, tab, max_retries: int = 3, retry_interval: tuple = (1, 2)) -> bool:\n        \"\"\"处理 Turnstile 人机验证\n\n        Args:\n            tab: 浏览器标签页对象\n            max_retries: 最大重试次数\n            retry_interval: 重试间隔时间范围(秒)\n\n        Returns:\n            bool: 验证是否成功\n        \"\"\"\n        info(\"正在检测 Turnstile 验证...\")\n        retry_count = 0\n\n        try:\n            while retry_count < max_retries:\n                retry_count += 1\n                debug(f\"第 {retry_count} 次尝试验证\")\n\n                try:\n                    # 查找Turnstile验证框\n                    challenge_check = (\n                        tab.ele(\"@id=cf-turnstile\", timeout=2)\n                        .child()\n                        .shadow_root.ele(\"tag:iframe\")\n                        .ele(\"tag:body\")\n                        .sr(\"tag:input\")\n                    )\n                    if challenge_check:\n                        info(\"检测到 Turnstile 验证框，开始处理...\")\n                        time.sleep(random.uniform(1, 3))\n                        challenge_check.click()\n                        time.sleep(2)\n\n                        # 检查是否验证成功\n                        if self._check_turnstile_success(tab):\n                            info(\"Turnstile 验证通过\")\n                            return True\n\n                except Exception as e:\n                    debug(f\"当前尝试未成功: {e}\")\n\n                # 检查是否已经验证成功\n                if self._check_turnstile_success(tab):\n                    info(\"验证成功\")\n                    return True\n\n                # 随机延时后继续下一次尝试\n                time.sleep(random.uniform(*retry_interval))\n\n            # 超出最大重试次数\n            error(f\"验证失败 - 已达到最大重试次数 {max_retries}\")\n            return False\n\n        except Exception as e:\n            error(f\"Turnstile 验证过程发生异常: {e}\")\n            return False\n\n    def _check_turnstile_success(self, tab) -> bool:\n        \"\"\"检查Turnstile验证是否成功\n\n        Args:\n            tab: 浏览器标签页对象\n\n        Returns:\n            bool: 验证是否成功\n        \"\"\"\n        try:\n            # 检查是否存在提交按钮且可点击\n            submit_button = tab.ele(\"@type=submit\", timeout=1)\n            if submit_button and submit_button.states.is_enabled:\n                return True\n\n            # 检查是否出现错误消息\n            error_messages = [\n                'xpath://div[contains(text(), \"Can\\'t verify the user is human\")]',\n                'xpath://div[contains(text(), \"Error: 600010\")]',\n                'xpath://div[contains(text(), \"Please try again\")]'\n            ]\n\n            for error_xpath in error_messages:\n                if tab.ele(error_xpath, timeout=0.5):\n                    warning(f\"检测到验证错误: {error_xpath}\")\n                    return False\n\n            return False\n        except Exception as e:\n            debug(f\"验证状态检查失败: {e}\")\n            return False\n\n    def _handle_email_verification(self, tab, email: str, timeout: int = 300) -> bool:\n        \"\"\"处理邮箱验证\n\n        Args:\n            tab: 浏览器标签页对象\n            email: 邮箱地址\n            timeout: 超时时间（秒）\n\n        Returns:\n            bool: 验证是否成功\n        \"\"\"\n        try:\n            info(\"正在等待邮箱验证页面...\")\n\n            # 等待邮箱验证页面出现\n            verification_input = tab.ele(\"@data-index=0\", timeout=10)\n            if not verification_input:\n                # 检查是否已经跳转到其他页面（可能验证已完成）\n                if \"onboarding\" in tab.url or \"dashboard\" in tab.url:\n                    info(\"已跳转到后续页面，邮箱验证可能已完成\")\n                    return True\n\n                error(\"未找到邮箱验证输入框\")\n                return False\n\n            info(\"已到达邮箱验证页面，开始获取验证码...\")\n\n            # 获取验证码\n            verification_code = self.email_handler.get_windsurf_verification_code(email, timeout)\n\n            if not verification_code:\n                error(\"获取验证码失败\")\n                return False\n\n            info(f\"成功获取验证码: {verification_code}\")\n\n            # 输入验证码\n            for i, digit in enumerate(verification_code):\n                digit_input = tab.ele(f\"@data-index={i}\")\n                if digit_input:\n                    digit_input.input(digit)\n                    time.sleep(0.2)\n                else:\n                    error(f\"未找到第{i+1}位验证码输入框\")\n                    return False\n\n            info(\"验证码输入完成，等待验证...\")\n            time.sleep(3)\n\n            # 检查是否验证成功\n            if \"onboarding\" in tab.url or \"dashboard\" in tab.url:\n                info(\"邮箱验证成功，已跳转到后续页面\")\n                return True\n            else:\n                warning(\"验证码输入后未检测到成功页面\")\n                return False\n\n        except Exception as e:\n            error(f\"邮箱验证过程出错: {e}\")\n            return False\n\n    def _get_windsurf_token(self, browser, tab) -> Optional[str]:\n        \"\"\"获取Windsurf Token\n\n        Args:\n            browser: 浏览器对象\n            tab: 浏览器标签页对象\n\n        Returns:\n            Optional[str]: Token字符串，失败时返回None\n        \"\"\"\n        try:\n            info(\"正在获取Windsurf Token...\")\n\n            # 跳转到Token获取页面\n            info(f\"正在跳转到Token页面: {TOKEN_URL}\")\n            tab.get(TOKEN_URL)\n            time.sleep(5)\n\n            # 检查是否成功到达Token页面\n            if \"show-auth-token\" in tab.url:\n                info(\"已到达Token页面，开始提取Token...\")\n\n                # 尝试从页面JavaScript获取Token\n                token_js = \"\"\"\n                try {\n                    // 尝试从URL hash获取token\n                    const hash = window.location.hash;\n                    if (hash && hash.includes('access_token=')) {\n                        const match = hash.match(/access_token=([^&]+)/);\n                        if (match) return match[1];\n                    }\n\n                    // 尝试从页面元素获取token\n                    const inputs = document.querySelectorAll('input[type=\"text\"], input[value*=\"eyJ\"]');\n                    for (let input of inputs) {\n                        if (input.value && input.value.length > 40) {\n                            return input.value;\n                        }\n                    }\n\n                    // 尝试从localStorage获取\n                    const token = localStorage.getItem('auth_token') ||\n                                 localStorage.getItem('access_token') ||\n                                 localStorage.getItem('windsurf_token');\n                    if (token) return token;\n\n                    return null;\n                } catch (e) {\n                    return null;\n                }\n                \"\"\"\n\n                token = tab.run_js(token_js)\n\n                if token and len(token) > 40:\n                    info(\"成功获取到Token\")\n                    return token\n                else:\n                    warning(\"未能通过JavaScript获取Token，尝试备用方法...\")\n\n                    # 备用方法：从页面元素获取\n                    elements = tab.eles(\"tag:input\") or tab.eles(\"@type=text\")\n                    if elements:\n                        for element in elements:\n                            value = element.get_attribute(\"value\")\n                            if value and len(value) > 40:\n                                info(f\"从输入框获取到Token\")\n                                return value\n\n                    warning(\"所有Token获取方法都失败了\")\n                    return None\n            else:\n                warning(\"未能到达Token页面\")\n                return None\n\n        except Exception as e:\n            error(f\"获取Token过程出错: {e}\")\n            return None\n\n    def _cleanup(self):\n        \"\"\"清理资源\"\"\"\n        if self.browser_manager:\n            try:\n                self.browser_manager.quit()\n                info(\"浏览器已关闭\")\n            except Exception as e:\n                warning(f\"关闭浏览器时出错: {e}\")\n            finally:\n                self.browser_manager = None\n\n        if self.email_handler:\n            try:\n                self.email_handler.cleanup()\n            except Exception as e:\n                warning(f\"清理邮箱处理器时出错: {e}\")\n            finally:\n                self.email_handler = None\n    \n    def register_multiple_accounts(self, count: int, use_random_config: bool = False) -> Dict[str, Any]:\n        \"\"\"批量注册账号\n        \n        Args:\n            count: 注册数量\n            use_random_config: 是否使用随机配置\n            \n        Returns:\n            Dict[str, Any]: 批量注册结果\n        \"\"\"\n        results = {\n            \"total\": count,\n            \"success\": 0,\n            \"failed\": 0,\n            \"accounts\": [],\n            \"errors\": []\n        }\n        \n        # 保存原始配置索引\n        original_config_index = self.config.get_current_config_index()\n        \n        try:\n            for i in range(count):\n                info(f\"开始第 {i+1}/{count} 次Windsurf注册\")\n                \n                # 选择配置\n                config_index = self._select_config_index(use_random_config, original_config_index)\n                \n                # 注册账号\n                result = self.register_account(config_index)\n                \n                if result.success:\n                    results[\"success\"] += 1\n                    results[\"accounts\"].append({\n                        \"email\": result.email,\n                        \"token\": result.token,\n                        \"config_index\": config_index\n                    })\n                    info(f\"第 {i+1} 次Windsurf注册成功: {result.email}\")\n                else:\n                    results[\"failed\"] += 1\n                    results[\"errors\"].append({\n                        \"attempt\": i + 1,\n                        \"error\": result.message,\n                        \"config_index\": config_index\n                    })\n                    error(f\"第 {i+1} 次Windsurf注册失败: {result.message}\")\n                \n                # 等待间隔（除了最后一次）\n                if i < count - 1:\n                    wait_time = random.randint(5, 10)  # Windsurf可能需要更长等待时间\n                    info(f\"等待 {wait_time} 秒后继续...\")\n                    time.sleep(wait_time)\n            \n        finally:\n            # 恢复原始配置\n            if use_random_config:\n                self.config.switch_config(original_config_index)\n        \n        return results\n    \n    def _init_browser(self):\n        \"\"\"初始化浏览器\"\"\"\n        try:\n            self.browser_manager = BrowserManager()\n            user_agent = self._get_real_user_agent()\n            self.browser_manager.init_browser(user_agent, self.config.browser_headless)\n            info(\"浏览器初始化成功\")\n        except Exception as e:\n            raise BrowserError(f\"浏览器初始化失败: {e}\")\n    \n    def _init_email_handler(self, config_index: int):\n        \"\"\"初始化邮箱处理器\n        \n        Args:\n            config_index: 配置索引\n        \"\"\"\n        try:\n            self.email_handler = EmailHandler(config_index)\n            info(\"邮箱处理器初始化成功\")\n        except Exception as e:\n            raise EmailError(f\"邮箱处理器初始化失败: {e}\")\n    \n    def _generate_account_info(self) -> Dict[str, str]:\n        \"\"\"生成账号信息\n        \n        Returns:\n            Dict[str, str]: 账号信息\n        \"\"\"\n        # 这里应该调用邮箱生成器\n        # 暂时返回模拟数据\n        return {\n            \"email\": f\"windsurf_{int(time.time())}@example.com\",\n            \"password\": \"WindsurfPassword123!\",\n            \"first_name\": \"Windsurf\",\n            \"last_name\": \"User\"\n        }\n    \n    def _perform_registration(self, account_info: Dict[str, str]) -> RegistrationResponse:\n        \"\"\"执行注册流程\n        \n        Args:\n            account_info: 账号信息\n            \n        Returns:\n            RegistrationResponse: 注册结果\n        \"\"\"\n        try:\n            # 这里应该实现具体的Windsurf注册逻辑\n            # 包括：\n            # 1. 访问Windsurf注册页面\n            # 2. 填写注册信息\n            # 3. 处理人机验证\n            # 4. 处理邮箱验证码\n            # 5. 获取Token\n            \n            info(\"模拟Windsurf注册流程...\")\n            time.sleep(3)  # 模拟注册时间\n            \n            return RegistrationResponse.success_response(\n                email=account_info[\"email\"],\n                token=f\"windsurf_token_{int(time.time())}\",\n                user_id=f\"windsurf_user_{int(time.time())}\",\n                account_data=account_info\n            )\n            \n        except Exception as e:\n            return RegistrationResponse.error_response(f\"Windsurf注册流程失败: {e}\")\n    \n    def _save_account(self, result: RegistrationResponse):\n        \"\"\"保存账号到数据库\n        \n        Args:\n            result: 注册结果\n        \"\"\"\n        try:\n            account = WindsurfAccount(\n                email=result.email,\n                token=result.token,\n                user_id=result.user_id,\n                password=result.account_data.get(\"password\", \"\") if result.account_data else \"\",\n                status=\"active\"\n            )\n            \n            self.database.add_windsurf_account(account.to_dict())\n            \n        except Exception as e:\n            warning(f\"保存Windsurf账号到数据库失败: {e}\")\n    \n    def _select_config_index(self, use_random_config: bool, original_index: int) -> int:\n        \"\"\"选择配置索引\n        \n        Args:\n            use_random_config: 是否使用随机配置\n            original_index: 原始配置索引\n            \n        Returns:\n            int: 配置索引\n        \"\"\"\n        if not use_random_config:\n            return original_index\n        \n        config_count = self.config.get_config_count()\n        if config_count <= 1:\n            return original_index\n        \n        # 随机选择一个不同的配置\n        available_indices = list(range(config_count))\n        if original_index in available_indices and len(available_indices) > 1:\n            available_indices.remove(original_index)\n        \n        return random.choice(available_indices)\n    \n    def _get_real_user_agent(self) -> str:\n        \"\"\"获取真实的User-Agent\n        \n        Returns:\n            str: User-Agent字符串\n        \"\"\"\n        # 这里应该实现获取真实User-Agent的逻辑\n        # 暂时返回默认值\n        return \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"\n    \n    def _cleanup(self):\n        \"\"\"清理资源\"\"\"\n        if self.browser_manager:\n            try:\n                self.browser_manager.quit()\n                info(\"浏览器已关闭\")\n            except Exception as e:\n                warning(f\"关闭浏览器时出错: {e}\")\n            finally:\n                self.browser_manager = None\n        \n        self.email_handler = None\n\n\n# 便捷函数\ndef windsurf_quick_signup_process(config: Config, config_index: int = 0) -> Tuple[bool, Dict[str, Any], Optional[BrowserManager]]:\n    \"\"\"Windsurf快速注册流程（兼容旧接口）\n    \n    Args:\n        config: 配置实例\n        config_index: 配置索引\n        \n    Returns:\n        Tuple[bool, Dict[str, Any], Optional[BrowserManager]]: (成功状态, 账号数据, 浏览器管理器)\n    \"\"\"\n    service = WindsurfRegistrationService(config)\n    result = service.register_account(config_index)\n    \n    if result.success:\n        account_data = {\n            \"email\": result.email,\n            \"token\": result.token,\n            \"user_id\": result.user_id\n        }\n        if result.account_data:\n            account_data.update(result.account_data)\n        \n        return True, account_data, service.browser_manager\n    else:\n        return False, {\"error\": result.message}, service.browser_manager\n"}
{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/utils/browser.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n浏览器管理工具\n\n提供浏览器的初始化、配置和管理功能。\n\"\"\"\n\nimport os\nimport sys\nimport traceback\nfrom pathlib import Path\nfrom typing import Optional, Dict, Any\n\nfrom DrissionPage import Chromium, ChromiumOptions\n\nfrom ..core import info, error, warning, debug\nfrom ..core.exceptions import BrowserError, BrowserConfigError, BrowserInitError\n\n\nclass BrowserManager:\n    \"\"\"浏览器管理器\"\"\"\n    \n    def __init__(self):\n        \"\"\"初始化浏览器管理器\"\"\"\n        self.browser: Optional[Chromium] = None\n        self._is_browser_released = False\n    \n    def init_browser(self, user_agent: Optional[str] = None, headless_value: Optional[bool] = None) -> Chromium:\n        \"\"\"初始化浏览器实例\n        \n        Args:\n            user_agent: 自定义用户代理字符串\n            headless_value: 是否使用无头模式，为None时使用环境变量配置\n            \n        Returns:\n            Chromium: 初始化好的浏览器实例\n            \n        Raises:\n            BrowserInitError: 浏览器初始化失败\n            BrowserConfigError: 浏览器配置错误\n        \"\"\"\n        # 检查操作系统\n        if sys.platform not in [\"win32\", \"darwin\"]:\n            raise NotImplementedError(f\"不支持的操作系统: {sys.platform}\")\n        \n        # 检查浏览器是否已经初始化\n        if self.browser and not self._is_browser_released:\n            debug(\"浏览器已经初始化，将返回现有实例\")\n            return self.browser\n            \n        # 重置状态标志\n        self._is_browser_released = False\n        \n        try:\n            debug(\"开始初始化浏览器配置...\")\n            co = self._get_browser_options(user_agent, headless_value)\n            \n            debug(\"正在创建浏览器实例...\")\n            self.browser = Chromium(co)\n            \n            # 验证浏览器是否正常启动\n            if not self.browser:\n                self._is_browser_released = True\n                raise BrowserInitError(\"浏览器实例创建失败\")\n            \n            # 尝试访问一个测试页面来验证浏览器是否正常运行\n            try:\n                debug(\"正在验证浏览器运行状态...\")\n                self.browser.latest_tab.get(\"about:blank\")\n            except Exception as e:\n                error(f\"浏览器运行状态验证失败: {str(e)}\")\n                # 添加更详细的错误信息\n                error(f\"浏览器验证错误详情: {traceback.format_exc()}\")\n                if self.browser and not self._is_browser_released:\n                    try:\n                        self.browser.quit()\n                        self._is_browser_released = True\n                    except Exception as quit_error:\n                        error(f\"关闭浏览器时发生错误: {str(quit_error)}\")\n                raise BrowserInitError(f\"浏览器运行状态验证失败: {str(e)}\")\n\n            # 记录浏览器启动信息\n            info(f\"浏览器初始化成功\")\n            \n            return self.browser\n            \n        except BrowserInitError:\n            raise\n        except Exception as e:\n            error(f\"浏览器初始化失败: {str(e)}\")\n            error(f\"详细错误信息: {traceback.format_exc()}\")\n            self._is_browser_released = True\n            raise BrowserInitError(f\"浏览器初始化失败: {str(e)}\")\n    \n    def _get_browser_options(self, user_agent: Optional[str] = None, \n                           headless_value: Optional[bool] = None) -> ChromiumOptions:\n        \"\"\"获取浏览器配置选项\n        \n        Args:\n            user_agent: 自定义用户代理字符串\n            headless_value: 是否使用无头模式\n            \n        Returns:\n            ChromiumOptions: 浏览器配置选项\n            \n        Raises:\n            BrowserConfigError: 浏览器配置过程中出错\n        \"\"\"\n        try:\n            debug(\"正在配置浏览器选项...\")\n            co = ChromiumOptions()\n            \n            # 加载扩展\n            try:\n                extension_path = self._get_extension_path()\n                co.add_extension(extension_path)\n                debug(f\"成功加载浏览器扩展: {extension_path}\")\n            except FileNotFoundError as e:\n                warning(f\"警告: 浏览器扩展加载失败: {e}\")\n            except Exception as e:\n                error(f\"加载浏览器扩展时发生错误: {str(e)}\")\n                raise BrowserConfigError(f\"浏览器扩展配置失败: {str(e)}\")\n\n            # 基本浏览器配置\n            co.set_pref(\"credentials_enable_service\", False)\n            co.set_argument(\"--hide-crash-restore-bubble\")\n            \n            # 代理设置\n            proxy = os.environ.get(\"BROWSER_PROXY\")\n            if proxy:\n                try:\n                    co.set_proxy(proxy)\n                    debug(f\"已设置代理: {proxy}\")\n                except Exception as e:\n                    warning(f\"设置代理失败: {str(e)}\")\n            \n            # 用户代理设置\n            if user_agent:\n                try:\n                    co.set_user_agent(user_agent)\n                    debug(f\"已设置用户代理: {user_agent[:50]}...\")\n                except Exception as e:\n                    warning(f\"设置用户代理失败: {str(e)}\")\n            \n            # 无头模式设置\n            if headless_value is not None:\n                try:\n                    if headless_value:\n                        co.headless()\n                        debug(\"已启用无头模式\")\n                    else:\n                        debug(\"已禁用无头模式\")\n                except Exception as e:\n                    warning(f\"设置无头模式失败: {str(e)}\")\n            else:\n                # 从环境变量读取\n                headless_env = os.environ.get(\"BROWSER_HEADLESS\", \"True\")\n                if headless_env.lower() == \"true\":\n                    co.headless()\n                    debug(\"已从环境变量启用无头模式\")\n            \n            # 其他性能优化配置\n            co.set_argument(\"--no-sandbox\")\n            co.set_argument(\"--disable-dev-shm-usage\")\n            co.set_argument(\"--disable-gpu\")\n            co.set_argument(\"--disable-web-security\")\n            co.set_argument(\"--disable-features=VizDisplayCompositor\")\n            co.set_argument(\"--disable-blink-features=AutomationControlled\")\n            co.set_pref(\"useAutomationExtension\", False)\n            co.set_argument(\"--exclude-switches=enable-automation\")\n            \n            debug(\"浏览器配置完成\")\n            return co\n            \n        except BrowserConfigError:\n            raise\n        except Exception as e:\n            error(f\"配置浏览器选项失败: {str(e)}\")\n            raise BrowserConfigError(f\"浏览器配置失败: {str(e)}\")\n    \n    def _get_extension_path(self) -> str:\n        \"\"\"获取浏览器扩展路径\n        \n        Returns:\n            str: 扩展路径\n            \n        Raises:\n            FileNotFoundError: 扩展文件未找到\n        \"\"\"\n        # 可能的扩展路径\n        possible_paths = [\n            Path(\"resources/extensions\"),\n            Path(\"extensions\"),\n            Path(\"data/extensions\"),\n            Path(\"../resources/extensions\")\n        ]\n        \n        for path in possible_paths:\n            if path.exists() and path.is_dir():\n                # 查找扩展文件\n                extension_files = list(path.glob(\"*.crx\")) + list(path.glob(\"*.zip\"))\n                if extension_files:\n                    return str(extension_files[0])\n                \n                # 查找扩展目录\n                extension_dirs = [d for d in path.iterdir() if d.is_dir()]\n                if extension_dirs:\n                    return str(extension_dirs[0])\n        \n        raise FileNotFoundError(\"未找到浏览器扩展文件\")\n    \n    def get_real_user_agent(self) -> str:\n        \"\"\"获取真实的User-Agent字符串\n        \n        Returns:\n            str: User-Agent字符串\n        \"\"\"\n        try:\n            # 这里可以实现从真实浏览器获取User-Agent的逻辑\n            # 暂时返回一个常用的User-Agent\n            user_agents = [\n                \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\",\n                \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\n                \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\",\n                \"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"\n            ]\n            \n            import random\n            return random.choice(user_agents)\n            \n        except Exception as e:\n            warning(f\"获取User-Agent失败: {e}\")\n            return \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"\n    \n    def quit(self):\n        \"\"\"关闭浏览器\"\"\"\n        if self.browser and not self._is_browser_released:\n            try:\n                self.browser.quit()\n                debug(\"浏览器已关闭\")\n            except Exception as e:\n                warning(f\"关闭浏览器时出错: {e}\")\n            finally:\n                self._is_browser_released = True\n                self.browser = None\n    \n    def is_alive(self) -> bool:\n        \"\"\"检查浏览器是否还在运行\n        \n        Returns:\n            bool: 浏览器是否在运行\n        \"\"\"\n        try:\n            if not self.browser or self._is_browser_released:\n                return False\n            \n            # 尝试获取当前页面标题来检查浏览器状态\n            self.browser.latest_tab.title\n            return True\n            \n        except Exception:\n            return False\n    \n    def restart(self, user_agent: Optional[str] = None, headless_value: Optional[bool] = None) -> Chromium:\n        \"\"\"重启浏览器\n        \n        Args:\n            user_agent: 自定义用户代理字符串\n            headless_value: 是否使用无头模式\n            \n        Returns:\n            Chromium: 重启后的浏览器实例\n        \"\"\"\n        info(\"正在重启浏览器...\")\n        \n        # 关闭现有浏览器\n        self.quit()\n        \n        # 重新初始化\n        return self.init_browser(user_agent, headless_value)\n    \n    def __del__(self):\n        \"\"\"析构函数，确保浏览器被正确关闭\"\"\"\n        try:\n            self.quit()\n        except Exception:\n            pass\n\n\n# 便捷函数\ndef get_real_user_agent() -> str:\n    \"\"\"获取真实User-Agent（兼容旧接口）\n    \n    Returns:\n        str: User-Agent字符串\n    \"\"\"\n    manager = BrowserManager()\n    return manager.get_real_user_agent()\n"}
{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/models/__init__.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n数据模型模块\n\n定义应用程序中使用的数据模型类。\n\"\"\"\n\nfrom .account import Account, CursorAccount, WindsurfAccount\nfrom .email import EmailMessage, EmailVerificationCode\nfrom .config_model import ConfigModel\n\n__all__ = [\n    'Account',\n    'CursorAccount', \n    'WindsurfAccount',\n    'EmailMessage',\n    'EmailVerificationCode',\n    'ConfigModel',\n]\n"}
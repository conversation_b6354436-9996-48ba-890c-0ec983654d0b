{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src\\services\\cursor_service.py"}, "originalCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nCursor服务\n\n提供Cursor账号注册、登录、状态检查等功能。\n\"\"\"\n\nimport time\nimport json\nimport requests\nimport asyncio\nimport aiohttp\nfrom typing import Optional, Dict, Any, Tuple\nfrom selenium.webdriver.common.by import By\n\nfrom ..core.config import Config\nfrom ..core.logger import get_logger\nfrom ..core.exceptions import ServiceError, RegistrationError, AuthenticationError\nfrom ..core.constants import CURSOR_API_BASE_URL, CURSOR_DEFAULT_USER_ID, DEFAULT_TIMEOUT\nfrom ..models.account import CursorAccount, MembershipInfo, UsageInfo\nfrom .browser_service import BrowserService\nfrom .email_service import EmailService\n\n\nclass CursorService:\n    \"\"\"Cursor服务类\"\"\"\n    \n    def __init__(self, config: Config):\n        \"\"\"初始化Cursor服务\n        \n        Args:\n            config: 配置对象\n        \"\"\"\n        self.config = config\n        self.logger = get_logger()\n        self.browser_service = BrowserService(config)\n        self.email_service = EmailService(config)\n        self.session = requests.Session()\n        self.session.headers.update({\n            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n        })\n    \n    def register_account(self) -> Tuple[bool, Dict[str, Any]]:\n        \"\"\"注册Cursor账号\n        \n        Returns:\n            (是否成功, 账号信息或错误信息)\n        \"\"\"\n        try:\n            self.logger.info(\"开始Cursor账号注册流程\")\n            \n            # 生成临时邮箱\n            email = self.email_service.generate_temp_email()\n            \n            # 创建浏览器\n            driver = self.browser_service.create_driver()\n            \n            try:\n                # 导航到注册页面\n                if not self.browser_service.navigate_to(\"https://cursor.com/sign-up\"):\n                    raise RegistrationError(\"无法访问注册页面\")\n                \n                # 输入邮箱\n                if not self.browser_service.input_text(By.CSS_SELECTOR, 'input[type=\"email\"]', email):\n                    raise RegistrationError(\"无法输入邮箱\")\n                \n                # 点击注册按钮\n                if not self.browser_service.click_element(By.CSS_SELECTOR, 'button[type=\"submit\"]'):\n                    raise RegistrationError(\"无法点击注册按钮\")\n                \n                # 等待验证码邮件\n                self.logger.info(\"等待验证码邮件...\")\n                verification_code = self.email_service.get_cursor_verification_code(email)\n                \n                if not verification_code:\n                    raise RegistrationError(\"未收到验证码\")\n                \n                # 输入验证码\n                if not self.browser_service.input_text(By.CSS_SELECTOR, 'input[placeholder*=\"code\"]', verification_code):\n                    raise RegistrationError(\"无法输入验证码\")\n                \n                # 提交验证码\n                if not self.browser_service.click_element(By.CSS_SELECTOR, 'button[type=\"submit\"]'):\n                    raise RegistrationError(\"无法提交验证码\")\n                \n                # 等待跳转到主页面\n                time.sleep(5)\n                \n                # 尝试获取Token\n                token = self._extract_token_from_page()\n                \n                if not token:\n                    # 尝试从localStorage获取\n                    token = self._extract_token_from_storage()\n                \n                # 创建账号对象\n                account = CursorAccount(\n                    email=email,\n                    token=token or \"\",\n                    status=\"active\" if token else \"unknown\"\n                )\n                \n                self.logger.info(f\"Cursor账号注册成功: {email}\")\n                \n                return True, {\n                    \"email\": email,\n                    \"token\": token,\n                    \"account\": account\n                }\n                \n            finally:\n                self.browser_service.quit()\n                \n        except Exception as e:\n            self.logger.error(f\"Cursor账号注册失败: {e}\")\n            return False, {\"error\": str(e)}\n    \n    def _extract_token_from_page(self) -> Optional[str]:\n        \"\"\"从页面中提取Token\"\"\"\n        try:\n            # 尝试从页面源码中提取Token\n            page_source = self.browser_service.get_page_source()\n            \n            # 查找Token的常见模式\n            import re\n            token_patterns = [\n                r'\"accessToken\":\\s*\"([^\"]+)\"',\n                r'\"token\":\\s*\"([^\"]+)\"',\n                r'accessToken:\\s*\"([^\"]+)\"',\n                r'token:\\s*\"([^\"]+)\"'\n            ]\n            \n            for pattern in token_patterns:\n                matches = re.findall(pattern, page_source)\n                if matches:\n                    token = matches[0]\n                    if len(token) > 20:  # Token通常比较长\n                        self.logger.info(\"从页面源码中提取到Token\")\n                        return token\n            \n            return None\n            \n        except Exception as e:\n            self.logger.error(f\"从页面提取Token失败: {e}\")\n            return None\n    \n    def _extract_token_from_storage(self) -> Optional[str]:\n        \"\"\"从浏览器存储中提取Token\"\"\"\n        try:\n            # 尝试从localStorage获取Token\n            script = \"\"\"\n            return localStorage.getItem('accessToken') || \n                   localStorage.getItem('token') || \n                   localStorage.getItem('authToken') ||\n                   sessionStorage.getItem('accessToken') ||\n                   sessionStorage.getItem('token');\n            \"\"\"\n            \n            token = self.browser_service.execute_script(script)\n            \n            if token and len(token) > 20:\n                self.logger.info(\"从浏览器存储中提取到Token\")\n                return token\n            \n            return None\n            \n        except Exception as e:\n            self.logger.error(f\"从浏览器存储提取Token失败: {e}\")\n            return None\n    \n    async def check_account_status(self, token: str) -> Dict[str, Any]:\n        \"\"\"检查账号状态\n        \n        Args:\n            token: 账号Token\n            \n        Returns:\n            账号状态信息\n        \"\"\"\n        try:\n            self.logger.info(\"检查Cursor账号状态\")\n            \n            headers = {\n                'Authorization': f'Bearer {token}',\n                'Content-Type': 'application/json'\n            }\n            \n            # 检查账号基本信息\n            account_url = f\"{CURSOR_API_BASE_URL}/auth/me\"\n            \n            async with aiohttp.ClientSession() as session:\n                async with session.get(account_url, headers=headers, timeout=DEFAULT_TIMEOUT) as response:\n                    if response.status == 200:\n                        account_data = await response.json()\n                        \n                        # 获取会员信息\n                        membership_info = await self._get_membership_info(session, headers)\n                        \n                        # 获取使用量信息\n                        usage_info = await self._get_usage_info(session, headers)\n                        \n                        return {\n                            'is_valid': True,\n                            'account_data': account_data,\n                            'membership_info': membership_info,\n                            'usage_info': usage_info\n                        }\n                    else:\n                        return {\n                            'is_valid': False,\n                            'error': f'HTTP {response.status}'\n                        }\n                        \n        except Exception as e:\n            self.logger.error(f\"检查账号状态失败: {e}\")\n            return {\n                'is_valid': False,\n                'error': str(e)\n            }\n    \n    async def _get_membership_info(self, session, headers: Dict[str, str]) -> Optional[MembershipInfo]:\n        \"\"\"获取会员信息\"\"\"\n        try:\n            membership_url = f\"{CURSOR_API_BASE_URL}/auth/membership\"\n            \n            async with session.get(membership_url, headers=headers, timeout=DEFAULT_TIMEOUT) as response:\n                if response.status == 200:\n                    data = await response.json()\n                    \n                    return MembershipInfo(\n                        membership_type=data.get('type', 'free'),\n                        trial_days_remaining=data.get('trial_days_remaining'),\n                        is_trial=data.get('is_trial', False),\n                        expiry_date=data.get('expiry_date')\n                    )\n                    \n        except Exception as e:\n            self.logger.error(f\"获取会员信息失败: {e}\")\n        \n        return None\n    \n    async def _get_usage_info(self, session, headers: Dict[str, str]) -> Optional[UsageInfo]:\n        \"\"\"获取使用量信息\"\"\"\n        try:\n            usage_url = f\"{CURSOR_API_BASE_URL}/auth/usage\"\n            \n            async with session.get(usage_url, headers=headers, timeout=DEFAULT_TIMEOUT) as response:\n                if response.status == 200:\n                    data = await response.json()\n                    \n                    return UsageInfo(\n                        gpt4_used=data.get('gpt4_used', 0),\n                        gpt4_limit=data.get('gpt4_limit'),\n                        gpt4_start_of_month=data.get('gpt4_start_of_month'),\n                        other_models=data.get('other_models', {})\n                    )\n                    \n        except Exception as e:\n            self.logger.error(f\"获取使用量信息失败: {e}\")\n        \n        return None\n    \n    def check_account_sync(self, token: str) -> Dict[str, Any]:\n        \"\"\"同步检查账号状态（非异步版本）\n        \n        Args:\n            token: 账号Token\n            \n        Returns:\n            账号状态信息\n        \"\"\"\n        try:\n            # 创建新的事件循环\n            loop = asyncio.new_event_loop()\n            asyncio.set_event_loop(loop)\n            \n            try:\n                result = loop.run_until_complete(self.check_account_status(token))\n                return result\n            finally:\n                loop.close()\n                \n        except Exception as e:\n            self.logger.error(f\"同步检查账号状态失败: {e}\")\n            return {\n                'is_valid': False,\n                'error': str(e)\n            }\n", "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nCursor服务\n\n提供Cursor账号注册、登录、状态检查等功能。\n\"\"\"\n\nimport time\nimport json\nimport requests\nimport asyncio\nimport aiohttp\nfrom typing import Optional, Dict, Any, Tuple\nfrom selenium.webdriver.common.by import By\n\nfrom ..core.config import Config\nfrom ..core.logger import get_logger\nfrom ..core.exceptions import ServiceError, RegistrationError, AuthenticationError\nfrom ..core.constants import CURSOR_API_BASE_URL, CURSOR_DEFAULT_USER_ID, DEFAULT_TIMEOUT\nfrom ..models.account import CursorAccount, MembershipInfo, UsageInfo\nfrom .browser_service import BrowserService\nfrom .email_service import EmailService\n\n\nclass CursorService:\n    \"\"\"Cursor服务类\"\"\"\n    \n    def __init__(self, config: Config):\n        \"\"\"初始化Cursor服务\n        \n        Args:\n            config: 配置对象\n        \"\"\"\n        self.config = config\n        self.logger = get_logger()\n        self.browser_service = BrowserService(config)\n        self.email_service = EmailService(config)\n        self.session = requests.Session()\n        self.session.headers.update({\n            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n        })\n    \n    def register_account(self) -> Tuple[bool, Dict[str, Any]]:\n        \"\"\"注册Cursor账号\n        \n        Returns:\n            (是否成功, 账号信息或错误信息)\n        \"\"\"\n        try:\n            self.logger.info(\"开始Cursor账号注册流程\")\n            \n            # 生成临时邮箱\n            email = self.email_service.generate_temp_email()\n            \n            # 创建浏览器\n            driver = self.browser_service.create_driver()\n            \n            try:\n                # 导航到注册页面\n                if not self.browser_service.navigate_to(\"https://cursor.com/sign-up\"):\n                    raise RegistrationError(\"无法访问注册页面\")\n                \n                # 输入邮箱\n                if not self.browser_service.input_text(By.CSS_SELECTOR, 'input[type=\"email\"]', email):\n                    raise RegistrationError(\"无法输入邮箱\")\n                \n                # 点击注册按钮\n                if not self.browser_service.click_element(By.CSS_SELECTOR, 'button[type=\"submit\"]'):\n                    raise RegistrationError(\"无法点击注册按钮\")\n                \n                # 等待验证码邮件\n                self.logger.info(\"等待验证码邮件...\")\n                verification_code = self.email_service.get_cursor_verification_code(email)\n                \n                if not verification_code:\n                    raise RegistrationError(\"未收到验证码\")\n                \n                # 输入验证码\n                if not self.browser_service.input_text(By.CSS_SELECTOR, 'input[placeholder*=\"code\"]', verification_code):\n                    raise RegistrationError(\"无法输入验证码\")\n                \n                # 提交验证码\n                if not self.browser_service.click_element(By.CSS_SELECTOR, 'button[type=\"submit\"]'):\n                    raise RegistrationError(\"无法提交验证码\")\n                \n                # 等待跳转到主页面\n                time.sleep(5)\n                \n                # 尝试获取Token\n                token = self._extract_token_from_page()\n                \n                if not token:\n                    # 尝试从localStorage获取\n                    token = self._extract_token_from_storage()\n                \n                # 创建账号对象\n                account = CursorAccount(\n                    email=email,\n                    token=token or \"\",\n                    status=\"active\" if token else \"unknown\"\n                )\n                \n                self.logger.info(f\"Cursor账号注册成功: {email}\")\n                \n                return True, {\n                    \"email\": email,\n                    \"token\": token,\n                    \"account\": account\n                }\n                \n            finally:\n                self.browser_service.quit()\n                \n        except Exception as e:\n            self.logger.error(f\"Cursor账号注册失败: {e}\")\n            return False, {\"error\": str(e)}\n    \n    def _extract_token_from_page(self) -> Optional[str]:\n        \"\"\"从页面中提取Token\"\"\"\n        try:\n            # 尝试从页面源码中提取Token\n            page_source = self.browser_service.get_page_source()\n            \n            # 查找Token的常见模式\n            import re\n            token_patterns = [\n                r'\"accessToken\":\\s*\"([^\"]+)\"',\n                r'\"token\":\\s*\"([^\"]+)\"',\n                r'accessToken:\\s*\"([^\"]+)\"',\n                r'token:\\s*\"([^\"]+)\"'\n            ]\n            \n            for pattern in token_patterns:\n                matches = re.findall(pattern, page_source)\n                if matches:\n                    token = matches[0]\n                    if len(token) > 20:  # Token通常比较长\n                        self.logger.info(\"从页面源码中提取到Token\")\n                        return token\n            \n            return None\n            \n        except Exception as e:\n            self.logger.error(f\"从页面提取Token失败: {e}\")\n            return None\n    \n    def _extract_token_from_storage(self) -> Optional[str]:\n        \"\"\"从浏览器存储中提取Token\"\"\"\n        try:\n            # 尝试从localStorage获取Token\n            script = \"\"\"\n            return localStorage.getItem('accessToken') || \n                   localStorage.getItem('token') || \n                   localStorage.getItem('authToken') ||\n                   sessionStorage.getItem('accessToken') ||\n                   sessionStorage.getItem('token');\n            \"\"\"\n            \n            token = self.browser_service.execute_script(script)\n            \n            if token and len(token) > 20:\n                self.logger.info(\"从浏览器存储中提取到Token\")\n                return token\n            \n            return None\n            \n        except Exception as e:\n            self.logger.error(f\"从浏览器存储提取Token失败: {e}\")\n            return None\n    \n    async def check_account_status(self, token: str) -> Dict[str, Any]:\n        \"\"\"检查账号状态\n        \n        Args:\n            token: 账号Token\n            \n        Returns:\n            账号状态信息\n        \"\"\"\n        try:\n            self.logger.info(\"检查Cursor账号状态\")\n            \n            headers = {\n                'Authorization': f'Bearer {token}',\n                'Content-Type': 'application/json'\n            }\n            \n            # 检查账号基本信息\n            account_url = f\"{CURSOR_API_BASE_URL}/auth/me\"\n            \n            async with aiohttp.ClientSession() as session:\n                async with session.get(account_url, headers=headers, timeout=DEFAULT_TIMEOUT) as response:\n                    if response.status == 200:\n                        account_data = await response.json()\n                        \n                        # 获取会员信息\n                        membership_info = await self._get_membership_info(session, headers)\n                        \n                        # 获取使用量信息\n                        usage_info = await self._get_usage_info(session, headers)\n                        \n                        return {\n                            'is_valid': True,\n                            'account_data': account_data,\n                            'membership_info': membership_info,\n                            'usage_info': usage_info\n                        }\n                    else:\n                        return {\n                            'is_valid': False,\n                            'error': f'HTTP {response.status}'\n                        }\n                        \n        except Exception as e:\n            self.logger.error(f\"检查账号状态失败: {e}\")\n            return {\n                'is_valid': False,\n                'error': str(e)\n            }\n    \n    async def _get_membership_info(self, session, headers: Dict[str, str]) -> Optional[MembershipInfo]:\n        \"\"\"获取会员信息\"\"\"\n        try:\n            membership_url = f\"{CURSOR_API_BASE_URL}/auth/membership\"\n            \n            async with session.get(membership_url, headers=headers, timeout=DEFAULT_TIMEOUT) as response:\n                if response.status == 200:\n                    data = await response.json()\n                    \n                    return MembershipInfo(\n                        membership_type=data.get('type', 'free'),\n                        trial_days_remaining=data.get('trial_days_remaining'),\n                        is_trial=data.get('is_trial', False),\n                        expiry_date=data.get('expiry_date')\n                    )\n                    \n        except Exception as e:\n            self.logger.error(f\"获取会员信息失败: {e}\")\n        \n        return None\n    \n    async def _get_usage_info(self, session, headers: Dict[str, str]) -> Optional[UsageInfo]:\n        \"\"\"获取使用量信息\"\"\"\n        try:\n            usage_url = f\"{CURSOR_API_BASE_URL}/auth/usage\"\n            \n            async with session.get(usage_url, headers=headers, timeout=DEFAULT_TIMEOUT) as response:\n                if response.status == 200:\n                    data = await response.json()\n                    \n                    return UsageInfo(\n                        gpt4_used=data.get('gpt4_used', 0),\n                        gpt4_limit=data.get('gpt4_limit'),\n                        gpt4_start_of_month=data.get('gpt4_start_of_month'),\n                        other_models=data.get('other_models', {})\n                    )\n                    \n        except Exception as e:\n            self.logger.error(f\"获取使用量信息失败: {e}\")\n        \n        return None\n    \n    def check_account_sync(self, token: str) -> Dict[str, Any]:\n        \"\"\"同步检查账号状态（非异步版本）\n        \n        Args:\n            token: 账号Token\n            \n        Returns:\n            账号状态信息\n        \"\"\"\n        try:\n            # 创建新的事件循环\n            loop = asyncio.new_event_loop()\n            asyncio.set_event_loop(loop)\n            \n            try:\n                result = loop.run_until_complete(self.check_account_status(token))\n                return result\n            finally:\n                loop.close()\n                \n        except Exception as e:\n            self.logger.error(f\"同步检查账号状态失败: {e}\")\n            return {\n                'is_valid': False,\n                'error': str(e)\n            }\n"}
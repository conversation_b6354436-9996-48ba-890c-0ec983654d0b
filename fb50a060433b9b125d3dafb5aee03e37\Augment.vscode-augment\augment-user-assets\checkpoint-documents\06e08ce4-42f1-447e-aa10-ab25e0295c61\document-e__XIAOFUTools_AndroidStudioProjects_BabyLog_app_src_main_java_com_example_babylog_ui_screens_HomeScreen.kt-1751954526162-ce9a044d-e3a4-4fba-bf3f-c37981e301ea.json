{"path": {"rootPath": "e:\\XIAOFUTools\\AndroidStudioProjects\\BabyLog", "relPath": "app\\src\\main\\java\\com\\example\\babylog\\ui\\screens\\HomeScreen.kt"}, "originalCode": "package com.example.babylog.ui.screens\n\nimport androidx.compose.foundation.background\nimport androidx.compose.foundation.layout.*\nimport androidx.compose.foundation.lazy.LazyColumn\nimport androidx.compose.foundation.lazy.LazyRow\nimport androidx.compose.foundation.lazy.items\nimport androidx.compose.foundation.shape.RoundedCornerShape\nimport androidx.compose.material.icons.Icons\nimport androidx.compose.material.icons.filled.*\nimport androidx.compose.material3.*\nimport androidx.compose.runtime.*\nimport androidx.compose.ui.Alignment\nimport androidx.compose.ui.Modifier\nimport androidx.compose.ui.draw.clip\nimport androidx.compose.ui.graphics.Color\nimport androidx.compose.ui.platform.LocalContext\nimport androidx.compose.ui.text.font.FontWeight\nimport androidx.compose.ui.unit.dp\nimport androidx.navigation.NavController\nimport com.example.babylog.data.database.BabyLogDatabase\nimport com.example.babylog.data.entity.Baby\nimport com.example.babylog.data.repository.BabyRepository\nimport com.example.babylog.navigation.Screen\nimport com.example.babylog.ui.theme.*\nimport java.text.SimpleDateFormat\nimport java.util.*\n\n@OptIn(ExperimentalMaterial3Api::class)\n@Composable\nfun HomeScreen(navController: NavController) {\n    val context = LocalContext.current\n    val database = BabyLogDatabase.getDatabase(context)\n    val repository = BabyRepository(\n        database.babyDao(),\n        database.healthRecordDao(),\n        database.milestoneDao(),\n        database.feedingRecordDao(),\n        database.photoDao()\n    )\n    \n    val babies by repository.getAllBabies().collectAsState(initial = emptyList())\n    \n    Column(\n        modifier = Modifier\n            .fillMaxSize()\n            .padding(16.dp)\n    ) {\n        // Header\n        Row(\n            modifier = Modifier.fillMaxWidth(),\n            horizontalArrangement = Arrangement.SpaceBetween,\n            verticalAlignment = Alignment.CenterVertically\n        ) {\n            Text(\n                text = \"宝贝日记\",\n                style = MaterialTheme.typography.headlineMedium,\n                fontWeight = FontWeight.Bold\n            )\n            \n            Row {\n                IconButton(onClick = { navController.navigate(Screen.Settings.route) }) {\n                    Icon(Icons.Default.Settings, contentDescription = \"设置\")\n                }\n                IconButton(onClick = { navController.navigate(Screen.BabyProfile.route) }) {\n                    Icon(Icons.Default.Person, contentDescription = \"宝宝档案\")\n                }\n            }\n        }\n        \n        Spacer(modifier = Modifier.height(16.dp))\n        \n        if (babies.isEmpty()) {\n            // Empty state\n            Card(\n                modifier = Modifier.fillMaxWidth(),\n                onClick = { navController.navigate(\"${Screen.AddBaby.route}?babyId=\") }\n            ) {\n                Column(\n                    modifier = Modifier\n                        .fillMaxWidth()\n                        .padding(24.dp),\n                    horizontalAlignment = Alignment.CenterHorizontally\n                ) {\n                    Icon(\n                        Icons.Default.Add,\n                        contentDescription = null,\n                        modifier = Modifier.size(48.dp),\n                        tint = MaterialTheme.colorScheme.primary\n                    )\n                    Spacer(modifier = Modifier.height(16.dp))\n                    Text(\n                        text = \"添加第一个宝宝档案\",\n                        style = MaterialTheme.typography.titleMedium\n                    )\n                    Text(\n                        text = \"开始记录宝宝的成长历程\",\n                        style = MaterialTheme.typography.bodyMedium,\n                        color = MaterialTheme.colorScheme.onSurfaceVariant\n                    )\n                }\n            }\n        } else {\n            // Baby list\n            LazyColumn(\n                verticalArrangement = Arrangement.spacedBy(8.dp)\n            ) {\n                items(babies) { baby ->\n                    BabyCard(\n                        baby = baby,\n                        onClick = { \n                            // Navigate to baby details or set as current baby\n                        }\n                    )\n                }\n                \n                item {\n                    Card(\n                        modifier = Modifier.fillMaxWidth(),\n                        onClick = { navController.navigate(\"${Screen.AddBaby.route}?babyId=\") }\n                    ) {\n                        Row(\n                            modifier = Modifier\n                                .fillMaxWidth()\n                                .padding(16.dp),\n                            verticalAlignment = Alignment.CenterVertically\n                        ) {\n                            Icon(\n                                Icons.Default.Add,\n                                contentDescription = null,\n                                tint = MaterialTheme.colorScheme.primary\n                            )\n                            Spacer(modifier = Modifier.width(16.dp))\n                            Text(\n                                text = \"添加新宝宝\",\n                                style = MaterialTheme.typography.titleMedium\n                            )\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n\n@Composable\nfun BabyCard(\n    baby: Baby,\n    onClick: () -> Unit\n) {\n    val dateFormat = SimpleDateFormat(\"yyyy年MM月dd日\", Locale.getDefault())\n    val ageInDays = ((Date().time - baby.birthDate.time) / (1000 * 60 * 60 * 24)).toInt()\n    val ageText = when {\n        ageInDays < 30 -> \"${ageInDays}天\"\n        ageInDays < 365 -> \"${ageInDays / 30}个月\"\n        else -> \"${ageInDays / 365}岁${(ageInDays % 365) / 30}个月\"\n    }\n    \n    Card(\n        modifier = Modifier.fillMaxWidth(),\n        onClick = onClick\n    ) {\n        Column(\n            modifier = Modifier\n                .fillMaxWidth()\n                .padding(16.dp)\n        ) {\n            Row(\n                modifier = Modifier.fillMaxWidth(),\n                horizontalArrangement = Arrangement.SpaceBetween,\n                verticalAlignment = Alignment.CenterVertically\n            ) {\n                Column {\n                    Text(\n                        text = baby.name,\n                        style = MaterialTheme.typography.titleLarge,\n                        fontWeight = FontWeight.Bold\n                    )\n                    Text(\n                        text = \"出生日期: ${dateFormat.format(baby.birthDate)}\",\n                        style = MaterialTheme.typography.bodyMedium,\n                        color = MaterialTheme.colorScheme.onSurfaceVariant\n                    )\n                    Text(\n                        text = \"年龄: $ageText\",\n                        style = MaterialTheme.typography.bodyMedium,\n                        color = MaterialTheme.colorScheme.onSurfaceVariant\n                    )\n                }\n                \n                Icon(\n                    if (baby.gender == \"male\") Icons.Default.Person else Icons.Default.Person,\n                    contentDescription = baby.gender,\n                    modifier = Modifier.size(32.dp),\n                    tint = if (baby.gender == \"male\")\n                        MaterialTheme.colorScheme.primary\n                    else\n                        MaterialTheme.colorScheme.secondary\n                )\n            }\n        }\n    }\n}\n", "modifiedCode": "package com.example.babylog.ui.screens\n\nimport androidx.compose.foundation.background\nimport androidx.compose.foundation.layout.*\nimport androidx.compose.foundation.lazy.LazyColumn\nimport androidx.compose.foundation.lazy.LazyRow\nimport androidx.compose.foundation.lazy.items\nimport androidx.compose.foundation.shape.RoundedCornerShape\nimport androidx.compose.material.icons.Icons\nimport androidx.compose.material.icons.filled.*\nimport androidx.compose.material3.*\nimport androidx.compose.runtime.*\nimport androidx.compose.ui.Alignment\nimport androidx.compose.ui.Modifier\nimport androidx.compose.ui.draw.clip\nimport androidx.compose.ui.graphics.Color\nimport androidx.compose.ui.platform.LocalContext\nimport androidx.compose.ui.text.font.FontWeight\nimport androidx.compose.ui.unit.dp\nimport androidx.navigation.NavController\nimport com.example.babylog.data.database.BabyLogDatabase\nimport com.example.babylog.data.entity.Baby\nimport com.example.babylog.data.repository.BabyRepository\nimport com.example.babylog.navigation.Screen\nimport com.example.babylog.ui.theme.*\nimport java.text.SimpleDateFormat\nimport java.util.*\n\n@OptIn(ExperimentalMaterial3Api::class)\n@Composable\nfun HomeScreen(navController: NavController) {\n    val context = LocalContext.current\n    val database = BabyLogDatabase.getDatabase(context)\n    val repository = BabyRepository(\n        database.babyDao(),\n        database.healthRecordDao(),\n        database.milestoneDao(),\n        database.feedingRecordDao(),\n        database.photoDao()\n    )\n    \n    val babies by repository.getAllBabies().collectAsState(initial = emptyList())\n    \n    Column(\n        modifier = Modifier\n            .fillMaxSize()\n            .padding(16.dp)\n    ) {\n        // Header\n        Row(\n            modifier = Modifier.fillMaxWidth(),\n            horizontalArrangement = Arrangement.SpaceBetween,\n            verticalAlignment = Alignment.CenterVertically\n        ) {\n            Text(\n                text = \"宝贝日记\",\n                style = MaterialTheme.typography.headlineMedium,\n                fontWeight = FontWeight.Bold\n            )\n            \n            Row {\n                IconButton(onClick = { navController.navigate(Screen.Settings.route) }) {\n                    Icon(Icons.Default.Settings, contentDescription = \"设置\")\n                }\n                IconButton(onClick = { navController.navigate(Screen.BabyProfile.route) }) {\n                    Icon(Icons.Default.Person, contentDescription = \"宝宝档案\")\n                }\n            }\n        }\n        \n        Spacer(modifier = Modifier.height(16.dp))\n        \n        if (babies.isEmpty()) {\n            // Empty state\n            Card(\n                modifier = Modifier.fillMaxWidth(),\n                onClick = { navController.navigate(\"${Screen.AddBaby.route}?babyId=\") }\n            ) {\n                Column(\n                    modifier = Modifier\n                        .fillMaxWidth()\n                        .padding(24.dp),\n                    horizontalAlignment = Alignment.CenterHorizontally\n                ) {\n                    Icon(\n                        Icons.Default.Add,\n                        contentDescription = null,\n                        modifier = Modifier.size(48.dp),\n                        tint = MaterialTheme.colorScheme.primary\n                    )\n                    Spacer(modifier = Modifier.height(16.dp))\n                    Text(\n                        text = \"添加第一个宝宝档案\",\n                        style = MaterialTheme.typography.titleMedium\n                    )\n                    Text(\n                        text = \"开始记录宝宝的成长历程\",\n                        style = MaterialTheme.typography.bodyMedium,\n                        color = MaterialTheme.colorScheme.onSurfaceVariant\n                    )\n                }\n            }\n        } else {\n            // Baby list\n            LazyColumn(\n                verticalArrangement = Arrangement.spacedBy(8.dp)\n            ) {\n                items(babies) { baby ->\n                    BabyCard(\n                        baby = baby,\n                        onClick = { \n                            // Navigate to baby details or set as current baby\n                        }\n                    )\n                }\n                \n                item {\n                    Card(\n                        modifier = Modifier.fillMaxWidth(),\n                        onClick = { navController.navigate(\"${Screen.AddBaby.route}?babyId=\") }\n                    ) {\n                        Row(\n                            modifier = Modifier\n                                .fillMaxWidth()\n                                .padding(16.dp),\n                            verticalAlignment = Alignment.CenterVertically\n                        ) {\n                            Icon(\n                                Icons.Default.Add,\n                                contentDescription = null,\n                                tint = MaterialTheme.colorScheme.primary\n                            )\n                            Spacer(modifier = Modifier.width(16.dp))\n                            Text(\n                                text = \"添加新宝宝\",\n                                style = MaterialTheme.typography.titleMedium\n                            )\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n\n@Composable\nfun BabyCard(\n    baby: Baby,\n    onClick: () -> Unit\n) {\n    val dateFormat = SimpleDateFormat(\"yyyy年MM月dd日\", Locale.getDefault())\n    val ageInDays = ((Date().time - baby.birthDate.time) / (1000 * 60 * 60 * 24)).toInt()\n    val ageText = when {\n        ageInDays < 30 -> \"${ageInDays}天\"\n        ageInDays < 365 -> \"${ageInDays / 30}个月\"\n        else -> \"${ageInDays / 365}岁${(ageInDays % 365) / 30}个月\"\n    }\n    \n    Card(\n        modifier = Modifier.fillMaxWidth(),\n        onClick = onClick\n    ) {\n        Column(\n            modifier = Modifier\n                .fillMaxWidth()\n                .padding(16.dp)\n        ) {\n            Row(\n                modifier = Modifier.fillMaxWidth(),\n                horizontalArrangement = Arrangement.SpaceBetween,\n                verticalAlignment = Alignment.CenterVertically\n            ) {\n                Column {\n                    Text(\n                        text = baby.name,\n                        style = MaterialTheme.typography.titleLarge,\n                        fontWeight = FontWeight.Bold\n                    )\n                    Text(\n                        text = \"出生日期: ${dateFormat.format(baby.birthDate)}\",\n                        style = MaterialTheme.typography.bodyMedium,\n                        color = MaterialTheme.colorScheme.onSurfaceVariant\n                    )\n                    Text(\n                        text = \"年龄: $ageText\",\n                        style = MaterialTheme.typography.bodyMedium,\n                        color = MaterialTheme.colorScheme.onSurfaceVariant\n                    )\n                }\n                \n                Icon(\n                    if (baby.gender == \"male\") Icons.Default.Person else Icons.Default.Person,\n                    contentDescription = baby.gender,\n                    modifier = Modifier.size(32.dp),\n                    tint = if (baby.gender == \"male\")\n                        MaterialTheme.colorScheme.primary\n                    else\n                        MaterialTheme.colorScheme.secondary\n                )\n            }\n        }\n    }\n}\n"}
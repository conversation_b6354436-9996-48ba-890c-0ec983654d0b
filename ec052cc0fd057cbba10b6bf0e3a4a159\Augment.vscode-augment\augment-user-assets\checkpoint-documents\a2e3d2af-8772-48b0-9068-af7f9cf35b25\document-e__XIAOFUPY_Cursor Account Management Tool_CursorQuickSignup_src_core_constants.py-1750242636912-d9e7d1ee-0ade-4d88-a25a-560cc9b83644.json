{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/core/constants.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n常量定义模块\n\n定义应用程序中使用的所有常量。\n\"\"\"\n\n# 应用程序信息\nAPP_NAME = \"Cursor账号管理工具\"\nAPP_VERSION = \"4.0\"\nAPP_AUTHOR = \"XIAOFU\"\n\n# 网络配置\nDEFAULT_TIMEOUT = 30.0\nUSER_AGENT = \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36\"\n\n# Cursor相关常量\nCURSOR_API_BASE_URL = \"https://www.cursor.com/api\"\nCURSOR_DEFAULT_USER_ID = \"user_01OOOOOOOOOOOOOOOOOOOOOOOO\"\n\n# Windsurf相关常量\nWINDSURF_SIGN_UP_URL = \"https://windsurf.com/account/register\"\nWINDSURF_TOKEN_URL = \"https://windsurf.com/editor/signin?response_type=token&redirect_uri=show-auth-token\"\nWINDSURF_ONBOARDING_URL = \"https://windsurf.com/account/onboarding\"\n\n# 临时邮箱API\nTEMP_MAIL_API_BASE = \"https://tempmail.plus/api\"\n\n# 邮件相关常量\nDEFAULT_CURSOR_SENDER = \"<EMAIL>\"\nDEFAULT_CURSOR_SUBJECT = \"Verify your email address\"\nDEFAULT_WINDSURF_SENDER = \"<EMAIL>\"\nDEFAULT_WINDSURF_SUBJECT = \"Verify your email\"\n\n# 文件路径常量\nDATA_FOLDER_NAME = \"data\"\nLOGS_FOLDER_NAME = \"logs\"\nCONFIG_FILE_NAME = \"config.json\"\nCURSOR_ACCOUNT_FILE_NAME = \"cursor_acc.json\"\nWINDSURF_ACCOUNT_FILE_NAME = \"windsurf_acc.json\"\nLOG_FILE_NAME = \"logs.log\"\n\n# 日志配置\nLOG_ROTATION = \"midnight\"\nLOG_INTERVAL = 1\nLOG_BACKUP_COUNT = 9\n\n# UI相关常量\nWINDOW_MIN_WIDTH = 800\nWINDOW_MIN_HEIGHT = 600\nICON_SIZE = 16\n\n# 浏览器配置\nBROWSER_WAIT_TIMEOUT = 30\nBROWSER_IMPLICIT_WAIT = 10\n\n# 重置相关常量\nRESET_BACKUP_FOLDER = \"backup\"\nRESET_TEMP_FOLDER = \"temp\"\n\n# 验证码相关常量\nVERIFICATION_CODE_LENGTH = 6\nVERIFICATION_CODE_TIMEOUT = 300  # 5分钟\n\n# 账号状态\nACCOUNT_STATUS_ACTIVE = \"active\"\nACCOUNT_STATUS_INACTIVE = \"inactive\"\nACCOUNT_STATUS_EXPIRED = \"expired\"\nACCOUNT_STATUS_UNKNOWN = \"unknown\"\n\n# 会员类型\nMEMBERSHIP_FREE = \"free\"\nMEMBERSHIP_PRO = \"pro\"\nMEMBERSHIP_BUSINESS = \"business\"\n\n# 错误代码\nERROR_CODE_NETWORK = 1001\nERROR_CODE_AUTH = 1002\nERROR_CODE_VALIDATION = 1003\nERROR_CODE_FILE_IO = 1004\nERROR_CODE_BROWSER = 1005\nERROR_CODE_EMAIL = 1006\nERROR_CODE_RESET = 1007\n"}
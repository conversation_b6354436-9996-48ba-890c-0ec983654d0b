{"path": {"rootPath": "e:\\XIAOFUTools\\AndroidStudioProjects\\BabyLog", "relPath": "app/src/main/java/com/example/babylog/ui/screens/HomeScreen.kt"}, "originalCode": "package com.example.babylog.ui.screens\n\nimport androidx.compose.foundation.background\nimport androidx.compose.foundation.layout.*\nimport androidx.compose.foundation.lazy.LazyColumn\nimport androidx.compose.foundation.lazy.LazyRow\nimport androidx.compose.foundation.lazy.items\nimport androidx.compose.foundation.shape.RoundedCornerShape\nimport androidx.compose.material.icons.Icons\nimport androidx.compose.material.icons.filled.*\nimport androidx.compose.material3.*\nimport androidx.compose.runtime.*\nimport androidx.compose.ui.Alignment\nimport androidx.compose.ui.Modifier\nimport androidx.compose.ui.draw.clip\nimport androidx.compose.ui.graphics.Color\nimport androidx.compose.ui.platform.LocalContext\nimport androidx.compose.ui.text.font.FontWeight\nimport androidx.compose.ui.unit.dp\nimport androidx.navigation.NavController\nimport com.example.babylog.data.database.BabyLogDatabase\nimport com.example.babylog.data.entity.Baby\nimport com.example.babylog.data.repository.BabyRepository\nimport com.example.babylog.navigation.Screen\nimport com.example.babylog.ui.theme.*\nimport java.text.SimpleDateFormat\nimport java.util.*\n\n@OptIn(ExperimentalMaterial3Api::class)\n@Composable\nfun HomeScreen(navController: NavController) {\n    val context = LocalContext.current\n    val database = BabyLogDatabase.getDatabase(context)\n    val repository = BabyRepository(\n        database.babyDao(),\n        database.healthRecordDao(),\n        database.milestoneDao(),\n        database.feedingRecordDao(),\n        database.photoDao()\n    )\n\n    val babies by repository.getAllBabies().collectAsState(initial = emptyList())\n    var showAddDialog by remember { mutableStateOf(false) }\n\n    Box(\n        modifier = Modifier\n            .fillMaxSize()\n            .background(NeumorphismColors.background)\n    ) {\n        LazyColumn(\n            modifier = Modifier\n                .fillMaxSize()\n                .padding(horizontal = 20.dp),\n            contentPadding = PaddingValues(vertical = 20.dp),\n            verticalArrangement = Arrangement.spacedBy(20.dp)\n        ) {\n            // Header\n            item {\n                Row(\n                    modifier = Modifier.fillMaxWidth(),\n                    horizontalArrangement = Arrangement.SpaceBetween,\n                    verticalAlignment = Alignment.CenterVertically\n                ) {\n                    Column {\n                        Text(\n                            text = \"宝贝日记\",\n                            style = MaterialTheme.typography.headlineLarge,\n                            fontWeight = FontWeight.Bold,\n                            color = NeumorphismColors.textPrimary\n                        )\n                        Text(\n                            text = \"记录每一个珍贵时刻\",\n                            style = MaterialTheme.typography.bodyMedium,\n                            color = NeumorphismColors.textSecondary\n                        )\n                    }\n                }\n            }\n\n            // 今日统计卡片\n            if (babies.isNotEmpty()) {\n                item {\n                    TodayStatsSection(repository, babies.first())\n                }\n            }\n\n            // 宝宝卡片\n            if (babies.isEmpty()) {\n                item {\n                    EmptyStateCard(\n                        onClick = { navController.navigate(\"${Screen.AddBaby.route}?babyId=\") }\n                    )\n                }\n            } else {\n                item {\n                    Text(\n                        text = \"我的宝宝\",\n                        style = MaterialTheme.typography.titleLarge,\n                        fontWeight = FontWeight.Bold,\n                        color = NeumorphismColors.textPrimary,\n                        modifier = Modifier.padding(vertical = 8.dp)\n                    )\n                }\n\n                items(babies) { baby ->\n                    ModernBabyCard(\n                        baby = baby,\n                        repository = repository,\n                        onClick = {\n                            // Navigate to baby details\n                        }\n                    )\n                }\n            }\n\n            // 快速操作区域\n            if (babies.isNotEmpty()) {\n                item {\n                    QuickActionsSection(navController, babies.first())\n                }\n            }\n\n            // 底部间距，避免被悬浮按钮遮挡\n            item {\n                Spacer(modifier = Modifier.height(80.dp))\n            }\n        }\n\n        // 悬浮按钮\n        NeumorphismFloatingActionButton(\n            onClick = { showAddDialog = true },\n            modifier = Modifier\n                .align(Alignment.BottomEnd)\n                .padding(20.dp)\n        ) {\n            Icon(\n                Icons.Default.Add,\n                contentDescription = \"添加记录\",\n                tint = Color.White\n            )\n        }\n    }\n\n    // 添加记录对话框\n    if (showAddDialog) {\n        AddRecordDialog(\n            babies = babies,\n            onDismiss = { showAddDialog = false },\n            onNavigate = { route ->\n                showAddDialog = false\n                navController.navigate(route)\n            }\n        )\n    }\n}\n\n// 今日统计区域\n@Composable\nfun TodayStatsSection(repository: BabyRepository, baby: Baby) {\n    val today = remember {\n        Calendar.getInstance().apply {\n            set(Calendar.HOUR_OF_DAY, 0)\n            set(Calendar.MINUTE, 0)\n            set(Calendar.SECOND, 0)\n            set(Calendar.MILLISECOND, 0)\n        }.time\n    }\n\n    val feedingRecords by repository.getFeedingRecordsByBaby(baby.id).collectAsState(initial = emptyList())\n    val todayFeedings = feedingRecords.filter { it.startTime >= today }\n\n    Column {\n        Text(\n            text = \"今日概览\",\n            style = MaterialTheme.typography.titleLarge,\n            fontWeight = FontWeight.Bold,\n            color = NeumorphismColors.textPrimary,\n            modifier = Modifier.padding(vertical = 8.dp)\n        )\n\n        LazyRow(\n            horizontalArrangement = Arrangement.spacedBy(12.dp)\n        ) {\n            item {\n                StatCard(\n                    title = \"喂养次数\",\n                    value = \"${todayFeedings.size}\",\n                    subtitle = \"次\",\n                    color = NeumorphismColors.primary,\n                    modifier = Modifier.width(120.dp)\n                )\n            }\n\n            item {\n                val totalAmount = todayFeedings.filter { it.type == \"bottle\" }\n                    .mapNotNull { it.amount }.sum()\n                StatCard(\n                    title = \"奶量\",\n                    value = \"${totalAmount.toInt()}\",\n                    subtitle = \"ml\",\n                    color = NeumorphismColors.secondary,\n                    modifier = Modifier.width(120.dp)\n                )\n            }\n\n            item {\n                val ageInDays = ((Date().time - baby.birthDate.time) / (1000 * 60 * 60 * 24)).toInt()\n                StatCard(\n                    title = \"成长天数\",\n                    value = \"$ageInDays\",\n                    subtitle = \"天\",\n                    color = NeumorphismColors.accent,\n                    modifier = Modifier.width(120.dp)\n                )\n            }\n        }\n    }\n}\n\n// 空状态卡片\n@Composable\nfun EmptyStateCard(onClick: () -> Unit) {\n    NeumorphismCard(\n        modifier = Modifier\n            .fillMaxWidth()\n            .padding(vertical = 20.dp),\n        elevation = 8,\n        cornerRadius = 24\n    ) {\n        Column(\n            horizontalAlignment = Alignment.CenterHorizontally,\n            modifier = Modifier.padding(24.dp)\n        ) {\n            Icon(\n                Icons.Default.ChildCare,\n                contentDescription = null,\n                modifier = Modifier.size(64.dp),\n                tint = NeumorphismColors.primary\n            )\n            Spacer(modifier = Modifier.height(16.dp))\n            Text(\n                text = \"开始记录宝宝的成长\",\n                style = MaterialTheme.typography.titleLarge,\n                fontWeight = FontWeight.Bold,\n                color = NeumorphismColors.textPrimary\n            )\n            Spacer(modifier = Modifier.height(8.dp))\n            Text(\n                text = \"添加第一个宝宝档案，开启美好的记录之旅\",\n                style = MaterialTheme.typography.bodyMedium,\n                color = NeumorphismColors.textSecondary\n            )\n            Spacer(modifier = Modifier.height(20.dp))\n            NeumorphismButton(\n                onClick = onClick,\n                modifier = Modifier.fillMaxWidth()\n            ) {\n                Icon(Icons.Default.Add, contentDescription = null)\n                Spacer(modifier = Modifier.width(8.dp))\n                Text(\"添加宝宝档案\")\n            }\n        }\n    }\n}\n\n// 现代化宝宝卡片\n@Composable\nfun ModernBabyCard(\n    baby: Baby,\n    repository: BabyRepository,\n    onClick: () -> Unit\n) {\n    val ageInDays = ((Date().time - baby.birthDate.time) / (1000 * 60 * 60 * 24)).toInt()\n    val ageText = when {\n        ageInDays < 30 -> \"${ageInDays}天\"\n        ageInDays < 365 -> \"${ageInDays / 30}个月\"\n        else -> \"${ageInDays / 365}岁${(ageInDays % 365) / 30}个月\"\n    }\n\n    NeumorphismCard(\n        modifier = Modifier.fillMaxWidth(),\n        elevation = 6,\n        cornerRadius = 20\n    ) {\n        Row(\n            modifier = Modifier.fillMaxWidth(),\n            horizontalArrangement = Arrangement.SpaceBetween,\n            verticalAlignment = Alignment.CenterVertically\n        ) {\n            Column(modifier = Modifier.weight(1f)) {\n                Text(\n                    text = baby.name,\n                    style = MaterialTheme.typography.titleLarge,\n                    fontWeight = FontWeight.Bold,\n                    color = NeumorphismColors.textPrimary\n                )\n                Spacer(modifier = Modifier.height(4.dp))\n                Text(\n                    text = ageText,\n                    style = MaterialTheme.typography.bodyLarge,\n                    color = NeumorphismColors.primary,\n                    fontWeight = FontWeight.Medium\n                )\n                Text(\n                    text = SimpleDateFormat(\"yyyy年MM月dd日\", Locale.getDefault()).format(baby.birthDate),\n                    style = MaterialTheme.typography.bodySmall,\n                    color = NeumorphismColors.textSecondary\n                )\n            }\n\n            Box(\n                modifier = Modifier\n                    .size(60.dp)\n                    .clip(RoundedCornerShape(30.dp))\n                    .background(\n                        if (baby.gender == \"male\") NeumorphismColors.primary.copy(alpha = 0.1f)\n                        else NeumorphismColors.secondary.copy(alpha = 0.1f)\n                    ),\n                contentAlignment = Alignment.Center\n            ) {\n                Icon(\n                    Icons.Default.ChildCare,\n                    contentDescription = baby.gender,\n                    modifier = Modifier.size(32.dp),\n                    tint = if (baby.gender == \"male\") NeumorphismColors.primary else NeumorphismColors.secondary\n                )\n            }\n        }\n    }\n}\n", "modifiedCode": "package com.example.babylog.ui.screens\n\nimport androidx.compose.foundation.background\nimport androidx.compose.foundation.layout.*\nimport androidx.compose.foundation.lazy.LazyColumn\nimport androidx.compose.foundation.lazy.LazyRow\nimport androidx.compose.foundation.lazy.items\nimport androidx.compose.foundation.shape.RoundedCornerShape\nimport androidx.compose.material.icons.Icons\nimport androidx.compose.material.icons.filled.*\nimport androidx.compose.material3.*\nimport androidx.compose.runtime.*\nimport androidx.compose.ui.Alignment\nimport androidx.compose.ui.Modifier\nimport androidx.compose.ui.draw.clip\nimport androidx.compose.ui.graphics.Color\nimport androidx.compose.ui.platform.LocalContext\nimport androidx.compose.ui.text.font.FontWeight\nimport androidx.compose.ui.unit.dp\nimport androidx.navigation.NavController\nimport com.example.babylog.data.database.BabyLogDatabase\nimport com.example.babylog.data.entity.Baby\nimport com.example.babylog.data.repository.BabyRepository\nimport com.example.babylog.navigation.Screen\nimport com.example.babylog.ui.theme.*\nimport java.text.SimpleDateFormat\nimport java.util.*\n\n@OptIn(ExperimentalMaterial3Api::class)\n@Composable\nfun HomeScreen(navController: NavController) {\n    val context = LocalContext.current\n    val database = BabyLogDatabase.getDatabase(context)\n    val repository = BabyRepository(\n        database.babyDao(),\n        database.healthRecordDao(),\n        database.milestoneDao(),\n        database.feedingRecordDao(),\n        database.photoDao()\n    )\n\n    val babies by repository.getAllBabies().collectAsState(initial = emptyList())\n    var showAddDialog by remember { mutableStateOf(false) }\n\n    Box(\n        modifier = Modifier\n            .fillMaxSize()\n            .background(NeumorphismColors.background)\n    ) {\n        LazyColumn(\n            modifier = Modifier\n                .fillMaxSize()\n                .padding(horizontal = 20.dp),\n            contentPadding = PaddingValues(vertical = 20.dp),\n            verticalArrangement = Arrangement.spacedBy(20.dp)\n        ) {\n            // Header\n            item {\n                Row(\n                    modifier = Modifier.fillMaxWidth(),\n                    horizontalArrangement = Arrangement.SpaceBetween,\n                    verticalAlignment = Alignment.CenterVertically\n                ) {\n                    Column {\n                        Text(\n                            text = \"宝贝日记\",\n                            style = MaterialTheme.typography.headlineLarge,\n                            fontWeight = FontWeight.Bold,\n                            color = NeumorphismColors.textPrimary\n                        )\n                        Text(\n                            text = \"记录每一个珍贵时刻\",\n                            style = MaterialTheme.typography.bodyMedium,\n                            color = NeumorphismColors.textSecondary\n                        )\n                    }\n                }\n            }\n\n            // 今日统计卡片\n            if (babies.isNotEmpty()) {\n                item {\n                    TodayStatsSection(repository, babies.first())\n                }\n            }\n\n            // 宝宝卡片\n            if (babies.isEmpty()) {\n                item {\n                    EmptyStateCard(\n                        onClick = { navController.navigate(\"${Screen.AddBaby.route}?babyId=\") }\n                    )\n                }\n            } else {\n                item {\n                    Text(\n                        text = \"我的宝宝\",\n                        style = MaterialTheme.typography.titleLarge,\n                        fontWeight = FontWeight.Bold,\n                        color = NeumorphismColors.textPrimary,\n                        modifier = Modifier.padding(vertical = 8.dp)\n                    )\n                }\n\n                items(babies) { baby ->\n                    ModernBabyCard(\n                        baby = baby,\n                        repository = repository,\n                        onClick = {\n                            // Navigate to baby details\n                        }\n                    )\n                }\n            }\n\n            // 快速操作区域\n            if (babies.isNotEmpty()) {\n                item {\n                    QuickActionsSection(navController, babies.first())\n                }\n            }\n\n            // 底部间距，避免被悬浮按钮遮挡\n            item {\n                Spacer(modifier = Modifier.height(80.dp))\n            }\n        }\n\n        // 悬浮按钮\n        NeumorphismFloatingActionButton(\n            onClick = { showAddDialog = true },\n            modifier = Modifier\n                .align(Alignment.BottomEnd)\n                .padding(20.dp)\n        ) {\n            Icon(\n                Icons.Default.Add,\n                contentDescription = \"添加记录\",\n                tint = Color.White\n            )\n        }\n    }\n\n    // 添加记录对话框\n    if (showAddDialog) {\n        AddRecordDialog(\n            babies = babies,\n            onDismiss = { showAddDialog = false },\n            onNavigate = { route ->\n                showAddDialog = false\n                navController.navigate(route)\n            }\n        )\n    }\n}\n\n// 今日统计区域\n@Composable\nfun TodayStatsSection(repository: BabyRepository, baby: Baby) {\n    val today = remember {\n        Calendar.getInstance().apply {\n            set(Calendar.HOUR_OF_DAY, 0)\n            set(Calendar.MINUTE, 0)\n            set(Calendar.SECOND, 0)\n            set(Calendar.MILLISECOND, 0)\n        }.time\n    }\n\n    val feedingRecords by repository.getFeedingRecordsByBaby(baby.id).collectAsState(initial = emptyList())\n    val todayFeedings = feedingRecords.filter { it.startTime >= today }\n\n    Column {\n        Text(\n            text = \"今日概览\",\n            style = MaterialTheme.typography.titleLarge,\n            fontWeight = FontWeight.Bold,\n            color = NeumorphismColors.textPrimary,\n            modifier = Modifier.padding(vertical = 8.dp)\n        )\n\n        LazyRow(\n            horizontalArrangement = Arrangement.spacedBy(12.dp)\n        ) {\n            item {\n                StatCard(\n                    title = \"喂养次数\",\n                    value = \"${todayFeedings.size}\",\n                    subtitle = \"次\",\n                    color = NeumorphismColors.primary,\n                    modifier = Modifier.width(120.dp)\n                )\n            }\n\n            item {\n                val totalAmount = todayFeedings.filter { it.type == \"bottle\" }\n                    .mapNotNull { it.amount }.sum()\n                StatCard(\n                    title = \"奶量\",\n                    value = \"${totalAmount.toInt()}\",\n                    subtitle = \"ml\",\n                    color = NeumorphismColors.secondary,\n                    modifier = Modifier.width(120.dp)\n                )\n            }\n\n            item {\n                val ageInDays = ((Date().time - baby.birthDate.time) / (1000 * 60 * 60 * 24)).toInt()\n                StatCard(\n                    title = \"成长天数\",\n                    value = \"$ageInDays\",\n                    subtitle = \"天\",\n                    color = NeumorphismColors.accent,\n                    modifier = Modifier.width(120.dp)\n                )\n            }\n        }\n    }\n}\n\n// 空状态卡片\n@Composable\nfun EmptyStateCard(onClick: () -> Unit) {\n    NeumorphismCard(\n        modifier = Modifier\n            .fillMaxWidth()\n            .padding(vertical = 20.dp),\n        elevation = 8,\n        cornerRadius = 24\n    ) {\n        Column(\n            horizontalAlignment = Alignment.CenterHorizontally,\n            modifier = Modifier.padding(24.dp)\n        ) {\n            Icon(\n                Icons.Default.ChildCare,\n                contentDescription = null,\n                modifier = Modifier.size(64.dp),\n                tint = NeumorphismColors.primary\n            )\n            Spacer(modifier = Modifier.height(16.dp))\n            Text(\n                text = \"开始记录宝宝的成长\",\n                style = MaterialTheme.typography.titleLarge,\n                fontWeight = FontWeight.Bold,\n                color = NeumorphismColors.textPrimary\n            )\n            Spacer(modifier = Modifier.height(8.dp))\n            Text(\n                text = \"添加第一个宝宝档案，开启美好的记录之旅\",\n                style = MaterialTheme.typography.bodyMedium,\n                color = NeumorphismColors.textSecondary\n            )\n            Spacer(modifier = Modifier.height(20.dp))\n            NeumorphismButton(\n                onClick = onClick,\n                modifier = Modifier.fillMaxWidth()\n            ) {\n                Icon(Icons.Default.Add, contentDescription = null)\n                Spacer(modifier = Modifier.width(8.dp))\n                Text(\"添加宝宝档案\")\n            }\n        }\n    }\n}\n\n// 现代化宝宝卡片\n@Composable\nfun ModernBabyCard(\n    baby: Baby,\n    repository: BabyRepository,\n    onClick: () -> Unit\n) {\n    val ageInDays = ((Date().time - baby.birthDate.time) / (1000 * 60 * 60 * 24)).toInt()\n    val ageText = when {\n        ageInDays < 30 -> \"${ageInDays}天\"\n        ageInDays < 365 -> \"${ageInDays / 30}个月\"\n        else -> \"${ageInDays / 365}岁${(ageInDays % 365) / 30}个月\"\n    }\n\n    NeumorphismCard(\n        modifier = Modifier.fillMaxWidth(),\n        elevation = 6,\n        cornerRadius = 20\n    ) {\n        Row(\n            modifier = Modifier.fillMaxWidth(),\n            horizontalArrangement = Arrangement.SpaceBetween,\n            verticalAlignment = Alignment.CenterVertically\n        ) {\n            Column(modifier = Modifier.weight(1f)) {\n                Text(\n                    text = baby.name,\n                    style = MaterialTheme.typography.titleLarge,\n                    fontWeight = FontWeight.Bold,\n                    color = NeumorphismColors.textPrimary\n                )\n                Spacer(modifier = Modifier.height(4.dp))\n                Text(\n                    text = ageText,\n                    style = MaterialTheme.typography.bodyLarge,\n                    color = NeumorphismColors.primary,\n                    fontWeight = FontWeight.Medium\n                )\n                Text(\n                    text = SimpleDateFormat(\"yyyy年MM月dd日\", Locale.getDefault()).format(baby.birthDate),\n                    style = MaterialTheme.typography.bodySmall,\n                    color = NeumorphismColors.textSecondary\n                )\n            }\n\n            Box(\n                modifier = Modifier\n                    .size(60.dp)\n                    .clip(RoundedCornerShape(30.dp))\n                    .background(\n                        if (baby.gender == \"male\") NeumorphismColors.primary.copy(alpha = 0.1f)\n                        else NeumorphismColors.secondary.copy(alpha = 0.1f)\n                    ),\n                contentAlignment = Alignment.Center\n            ) {\n                Icon(\n                    Icons.Default.ChildCare,\n                    contentDescription = baby.gender,\n                    modifier = Modifier.size(32.dp),\n                    tint = if (baby.gender == \"male\") NeumorphismColors.primary else NeumorphismColors.secondary\n                )\n            }\n        }\n    }\n}\n\n// 快速操作区域\n@Composable\nfun QuickActionsSection(navController: NavController, baby: Baby) {\n    Column {\n        Text(\n            text = \"快速记录\",\n            style = MaterialTheme.typography.titleLarge,\n            fontWeight = FontWeight.Bold,\n            color = NeumorphismColors.textPrimary,\n            modifier = Modifier.padding(vertical = 8.dp)\n        )\n\n        LazyRow(\n            horizontalArrangement = Arrangement.spacedBy(12.dp)\n        ) {\n            item {\n                QuickActionCard(\n                    title = \"喂养\",\n                    icon = Icons.Default.Restaurant,\n                    color = NeumorphismColors.primary,\n                    onClick = { navController.navigate(\"${Screen.AddFeeding.route}?babyId=${baby.id}\") }\n                )\n            }\n\n            item {\n                QuickActionCard(\n                    title = \"健康\",\n                    icon = Icons.Default.Favorite,\n                    color = NeumorphismColors.secondary,\n                    onClick = { navController.navigate(\"${Screen.AddHealth.route}?babyId=${baby.id}\") }\n                )\n            }\n\n            item {\n                QuickActionCard(\n                    title = \"里程碑\",\n                    icon = Icons.Default.Star,\n                    color = NeumorphismColors.accent,\n                    onClick = { navController.navigate(\"${Screen.AddMilestone.route}?babyId=${baby.id}\") }\n                )\n            }\n        }\n    }\n}\n\n// 快速操作卡片\n@Composable\nfun QuickActionCard(\n    title: String,\n    icon: androidx.compose.ui.graphics.vector.ImageVector,\n    color: Color,\n    onClick: () -> Unit\n) {\n    NeumorphismCard(\n        modifier = Modifier\n            .width(100.dp)\n            .height(100.dp),\n        elevation = 4,\n        cornerRadius = 16\n    ) {\n        Column(\n            horizontalAlignment = Alignment.CenterHorizontally,\n            verticalArrangement = Arrangement.Center,\n            modifier = Modifier.fillMaxSize()\n        ) {\n            Box(\n                modifier = Modifier\n                    .size(40.dp)\n                    .clip(RoundedCornerShape(20.dp))\n                    .background(color.copy(alpha = 0.1f)),\n                contentAlignment = Alignment.Center\n            ) {\n                Icon(\n                    icon,\n                    contentDescription = title,\n                    modifier = Modifier.size(24.dp),\n                    tint = color\n                )\n            }\n            Spacer(modifier = Modifier.height(8.dp))\n            Text(\n                text = title,\n                style = MaterialTheme.typography.bodySmall,\n                color = NeumorphismColors.textPrimary,\n                fontWeight = FontWeight.Medium\n            )\n        }\n    }\n}\n\n// 添加记录对话框\n@Composable\nfun AddRecordDialog(\n    babies: List<Baby>,\n    onDismiss: () -> Unit,\n    onNavigate: (String) -> Unit\n) {\n    if (babies.isEmpty()) {\n        AlertDialog(\n            onDismissRequest = onDismiss,\n            title = { Text(\"提示\") },\n            text = { Text(\"请先添加宝宝档案\") },\n            confirmButton = {\n                TextButton(onClick = { onNavigate(\"${Screen.AddBaby.route}?babyId=\") }) {\n                    Text(\"添加宝宝\")\n                }\n            },\n            dismissButton = {\n                TextButton(onClick = onDismiss) {\n                    Text(\"取消\")\n                }\n            }\n        )\n        return\n    }\n\n    val baby = babies.first()\n\n    AlertDialog(\n        onDismissRequest = onDismiss,\n        title = {\n            Text(\n                \"添加记录\",\n                style = MaterialTheme.typography.titleLarge,\n                fontWeight = FontWeight.Bold\n            )\n        },\n        text = {\n            Column {\n                Text(\n                    \"为 ${baby.name} 添加记录\",\n                    style = MaterialTheme.typography.bodyMedium,\n                    color = NeumorphismColors.textSecondary\n                )\n                Spacer(modifier = Modifier.height(16.dp))\n\n                val actions = listOf(\n                    \"喂养记录\" to \"${Screen.AddFeeding.route}?babyId=${baby.id}\",\n                    \"健康记录\" to \"${Screen.AddHealth.route}?babyId=${baby.id}\",\n                    \"成长里程碑\" to \"${Screen.AddMilestone.route}?babyId=${baby.id}\"\n                )\n\n                actions.forEach { (title, route) ->\n                    TextButton(\n                        onClick = { onNavigate(route) },\n                        modifier = Modifier.fillMaxWidth()\n                    ) {\n                        Text(title)\n                    }\n                }\n            }\n        },\n        confirmButton = {},\n        dismissButton = {\n            TextButton(onClick = onDismiss) {\n                Text(\"取消\")\n            }\n        }\n    )\n}\n"}
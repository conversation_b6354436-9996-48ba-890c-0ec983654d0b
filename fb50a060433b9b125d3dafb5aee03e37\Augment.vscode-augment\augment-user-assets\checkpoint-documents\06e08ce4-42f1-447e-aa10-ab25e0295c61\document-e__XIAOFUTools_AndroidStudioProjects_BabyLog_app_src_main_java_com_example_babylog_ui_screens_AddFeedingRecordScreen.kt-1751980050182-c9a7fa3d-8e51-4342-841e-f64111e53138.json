{"path": {"rootPath": "e:\\XIAOFUTools\\AndroidStudioProjects\\BabyLog", "relPath": "app\\src\\main\\java\\com\\example\\babylog\\ui\\screens\\AddFeedingRecordScreen.kt"}, "originalCode": "package com.example.babylog.ui.screens\n\nimport androidx.compose.foundation.background\nimport androidx.compose.foundation.clickable\nimport androidx.compose.foundation.layout.*\nimport androidx.compose.foundation.lazy.LazyColumn\nimport androidx.compose.foundation.selection.selectable\nimport androidx.compose.foundation.shape.RoundedCornerShape\nimport androidx.compose.material.icons.Icons\nimport androidx.compose.material.icons.filled.*\nimport androidx.compose.material3.*\nimport androidx.compose.runtime.*\nimport androidx.compose.ui.Alignment\nimport androidx.compose.ui.Modifier\nimport androidx.compose.ui.draw.clip\nimport androidx.compose.ui.graphics.Color\nimport androidx.compose.ui.platform.LocalContext\nimport androidx.compose.ui.text.font.FontWeight\nimport androidx.compose.ui.unit.dp\nimport androidx.navigation.NavController\nimport com.example.babylog.data.database.BabyLogDatabase\nimport com.example.babylog.data.entity.FeedingRecord\nimport com.example.babylog.data.repository.BabyRepository\nimport com.example.babylog.ui.components.DateTimePickerDialog\nimport com.example.babylog.ui.components.EditableTimeField\nimport com.example.babylog.ui.theme.*\nimport kotlinx.coroutines.launch\nimport java.text.SimpleDateFormat\nimport java.util.*\n\n@OptIn(ExperimentalMaterial3Api::class)\n@Composable\nfun AddFeedingRecordScreen(\n    navController: NavController,\n    babyId: String\n) {\n    val context = LocalContext.current\n    val database = BabyLogDatabase.getDatabase(context)\n    val repository = BabyRepository(\n        database.babyDao(),\n        database.healthRecordDao(),\n        database.milestoneDao(),\n        database.feedingRecordDao(),\n        database.photoDao()\n    )\n    \n    val scope = rememberCoroutineScope()\n    \n    // Form state\n    var selectedType by remember { mutableStateOf(\"breastfeeding\") }\n    var amount by remember { mutableStateOf(\"\") }\n    var duration by remember { mutableStateOf(\"\") }\n    var foodName by remember { mutableStateOf(\"\") }\n    var notes by remember { mutableStateOf(\"\") }\n    var startTime by remember { mutableStateOf(Date()) }\n    var endTime by remember { mutableStateOf<Date?>(null) }\n    \n    val feedingTypes = listOf(\n        \"breastfeeding\" to \"母乳喂养\",\n        \"bottle\" to \"奶瓶喂养\",\n        \"solid_food\" to \"辅食\"\n    )\n    \n    val commonFoods = listOf(\n        \"米粉\", \"蛋黄\", \"苹果泥\", \"香蕉泥\", \"胡萝卜泥\", \"南瓜泥\",\n        \"土豆泥\", \"红薯泥\", \"青菜泥\", \"肉泥\", \"鱼泥\", \"粥\"\n    )\n    \n    Box(\n        modifier = Modifier\n            .fillMaxSize()\n            .background(NeumorphismColors.background)\n    ) {\n        LazyColumn(\n            modifier = Modifier\n                .fillMaxSize()\n                .padding(horizontal = 20.dp),\n            contentPadding = PaddingValues(vertical = 16.dp),\n            verticalArrangement = Arrangement.spacedBy(16.dp)\n        ) {\n            // Header\n            item {\n                Row(\n                    modifier = Modifier.fillMaxWidth(),\n                    horizontalArrangement = Arrangement.SpaceBetween,\n                    verticalAlignment = Alignment.CenterVertically\n                ) {\n                    IconButton(onClick = { navController.navigateUp() }) {\n                        Icon(\n                            Icons.Default.ArrowBack,\n                            contentDescription = \"返回\",\n                            tint = NeumorphismColors.textPrimary\n                        )\n                    }\n\n                    Column(horizontalAlignment = Alignment.CenterHorizontally) {\n                        Text(\n                            text = \"添加喂养记录\",\n                            style = MaterialTheme.typography.headlineMedium,\n                            fontWeight = FontWeight.Bold,\n                            color = NeumorphismColors.textPrimary\n                        )\n                        Text(\n                            text = \"记录宝宝的喂养情况\",\n                            style = MaterialTheme.typography.bodyMedium,\n                            color = NeumorphismColors.textSecondary\n                        )\n                    }\n\n                    // 占位符保持布局平衡\n                    Box(modifier = Modifier.size(48.dp))\n                }\n            }\n\n            // 喂养类型选择卡片\n            item {\n                NeumorphismCard(\n                    modifier = Modifier.fillMaxWidth(),\n                    elevation = 6,\n                    cornerRadius = 20\n                ) {\n                    Column {\n                        Text(\n                            text = \"喂养类型\",\n                            style = MaterialTheme.typography.titleLarge,\n                            fontWeight = FontWeight.Bold,\n                            color = NeumorphismColors.textPrimary\n                        )\n                        Spacer(modifier = Modifier.height(16.dp))\n\n                        // 喂养类型选择\n                        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {\n                            listOf(\n                                \"breastfeeding\" to \"母乳喂养\",\n                                \"bottle\" to \"奶瓶喂养\",\n                                \"solid_food\" to \"辅食喂养\"\n                            ).forEach { (type, label) ->\n                                Row(\n                                    modifier = Modifier\n                                        .fillMaxWidth()\n                                        .clip(RoundedCornerShape(12.dp))\n                                        .background(\n                                            if (selectedType == type)\n                                                NeumorphismColors.primary.copy(alpha = 0.1f)\n                                            else Color.Transparent\n                                        )\n                                        .clickable { selectedType = type }\n                                        .padding(12.dp),\n                                    verticalAlignment = Alignment.CenterVertically\n                                ) {\n                                    RadioButton(\n                                        selected = selectedType == type,\n                                        onClick = { selectedType = type },\n                                        colors = RadioButtonDefaults.colors(\n                                            selectedColor = NeumorphismColors.primary,\n                                            unselectedColor = NeumorphismColors.textSecondary\n                                        )\n                                    )\n                                    Spacer(modifier = Modifier.width(12.dp))\n                                    Icon(\n                                        when (type) {\n                                            \"breastfeeding\" -> Icons.Default.ChildCare\n                                            \"bottle\" -> Icons.Default.LocalDrink\n                                            \"solid_food\" -> Icons.Default.Restaurant\n                                            else -> Icons.Default.Restaurant\n                                        },\n                                        contentDescription = null,\n                                        modifier = Modifier.size(20.dp),\n                                        tint = if (selectedType == type)\n                                            NeumorphismColors.primary\n                                        else NeumorphismColors.textSecondary\n                                    )\n                                    Spacer(modifier = Modifier.width(8.dp))\n                                    Text(\n                                        text = label,\n                                        style = MaterialTheme.typography.bodyLarge,\n                                        fontWeight = if (selectedType == type) FontWeight.Bold else FontWeight.Normal,\n                                        color = if (selectedType == type)\n                                            NeumorphismColors.primary\n                                        else NeumorphismColors.textPrimary\n                                    )\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n\n            // 时间设置卡片\n            item {\n                NeumorphismCard(\n                    modifier = Modifier.fillMaxWidth(),\n                    elevation = 6,\n                    cornerRadius = 20\n                ) {\n                    Column {\n                        Text(\n                            text = \"时间设置\",\n                            style = MaterialTheme.typography.titleLarge,\n                            fontWeight = FontWeight.Bold,\n                            color = NeumorphismColors.textPrimary\n                        )\n                        Spacer(modifier = Modifier.height(16.dp))\n\n                        // 开始时间\n                        EditableTimeField(\n                            label = \"开始时间\",\n                            value = startTime,\n                            onValueChange = { startTime = it },\n                            modifier = Modifier.fillMaxWidth()\n                        )\n\n                        if (selectedType == \"breastfeeding\") {\n                            Spacer(modifier = Modifier.height(16.dp))\n                            // 结束时间（母乳喂养可选）\n                            if (endTime != null) {\n                                EditableTimeField(\n                                    label = \"结束时间（可选）\",\n                                    value = endTime!!,\n                                    onValueChange = { endTime = it },\n                                    modifier = Modifier.fillMaxWidth(),\n                                    isOptional = true,\n                                    onClear = { endTime = null }\n                                )\n                            } else {\n                                OutlinedTextField(\n                                    value = \"\",\n                                    onValueChange = { },\n                                    label = { Text(\"结束时间（可选）\") },\n                                    modifier = Modifier.fillMaxWidth(),\n                                    readOnly = true,\n                                    colors = getTextFieldColors(),\n                                    placeholder = { Text(\"点击添加结束时间\") },\n                                    trailingIcon = {\n                                        IconButton(onClick = {\n                                            // 设置结束时间为开始时间后30分钟\n                                            val calendar = Calendar.getInstance()\n                                            calendar.time = startTime\n                                            calendar.add(Calendar.MINUTE, 30)\n                                            endTime = calendar.time\n                                        }) {\n                                            Icon(\n                                                Icons.Default.Add,\n                                                contentDescription = \"添加结束时间\",\n                                                tint = NeumorphismColors.primary\n                                            )\n                                        }\n                                    }\n                                )\n                            }\n                        }\n                    }\n                }\n            }\n\n            // 详细信息卡片\n            item {\n                NeumorphismCard(\n                    modifier = Modifier.fillMaxWidth(),\n                    elevation = 6,\n                    cornerRadius = 20\n                ) {\n                    Column {\n                        Text(\n                            text = \"详细信息\",\n                            style = MaterialTheme.typography.titleLarge,\n                            fontWeight = FontWeight.Bold,\n                            color = NeumorphismColors.textPrimary\n                        )\n                        Spacer(modifier = Modifier.height(16.dp))\n\n                        when (selectedType) {\n                            \"breastfeeding\" -> {\n                                OutlinedTextField(\n                                    value = duration,\n                                    onValueChange = { duration = it },\n                                    label = { Text(\"喂养时长（分钟）*\") },\n                                    modifier = Modifier.fillMaxWidth(),\n                                    singleLine = true,\n                                    suffix = { Text(\"分钟\") },\n                                    colors = getTextFieldColors()\n                                )\n                            }\n                            \"bottle\" -> {\n                                OutlinedTextField(\n                                    value = amount,\n                                    onValueChange = { amount = it },\n                                    label = { Text(\"奶量（ml）*\") },\n                                    modifier = Modifier.fillMaxWidth(),\n                                    singleLine = true,\n                                    suffix = { Text(\"ml\") },\n                                    colors = getTextFieldColors()\n                                )\n                            }\n                            \"solid_food\" -> {\n                                OutlinedTextField(\n                                    value = foodName,\n                                    onValueChange = { foodName = it },\n                                    label = { Text(\"食物名称*\") },\n                                    modifier = Modifier.fillMaxWidth(),\n                                    singleLine = true,\n                                    placeholder = { Text(\"如：苹果泥、米粉等\") },\n                                    colors = getTextFieldColors()\n                                )\n                                Spacer(modifier = Modifier.height(16.dp))\n                                OutlinedTextField(\n                                    value = amount,\n                                    onValueChange = { amount = it },\n                                    label = { Text(\"食用量（g）\") },\n                                    modifier = Modifier.fillMaxWidth(),\n                                    singleLine = true,\n                                    suffix = { Text(\"g\") },\n                                    colors = getTextFieldColors()\n                                )\n                            }\n                        }\n\n                        Spacer(modifier = Modifier.height(16.dp))\n\n                        // 备注\n                        OutlinedTextField(\n                            value = notes,\n                            onValueChange = { notes = it },\n                            label = { Text(\"备注\") },\n                            modifier = Modifier.fillMaxWidth(),\n                            minLines = 3,\n                            maxLines = 5,\n                            placeholder = { Text(\"记录宝宝的反应、食欲情况等\") },\n                            colors = getTextFieldColors()\n                        )\n                    }\n                }\n            }\n\n            // 保存按钮\n            item {\n                NeumorphismButton(\n                onClick = {\n                    scope.launch {\n                        val feedingRecord = FeedingRecord(\n                            id = UUID.randomUUID().toString(),\n                            babyId = babyId,\n                            type = selectedType,\n                            amount = when (selectedType) {\n                                \"bottle\" -> amount.toDoubleOrNull()\n                                \"solid_food\" -> amount.toDoubleOrNull()\n                                else -> null\n                            },\n                            duration = if (selectedType == \"breastfeeding\") \n                                duration.toIntOrNull() else null,\n                            foodName = if (selectedType == \"solid_food\" && foodName.isNotBlank()) \n                                foodName else null,\n                            notes = if (notes.isNotBlank()) notes else null,\n                            startTime = startTime,\n                            endTime = endTime\n                        )\n                        \n                        repository.insertFeedingRecord(feedingRecord)\n                        navController.navigateUp()\n                    }\n                },\n                enabled = when (selectedType) {\n                    \"breastfeeding\" -> duration.isNotBlank() && duration.toIntOrNull() != null\n                    \"bottle\" -> amount.isNotBlank() && amount.toDoubleOrNull() != null\n                    \"solid_food\" -> foodName.isNotBlank()\n                    else -> true\n                },\n                modifier = Modifier.fillMaxWidth()\n            ) {\n                Icon(Icons.Default.Save, contentDescription = null)\n                Spacer(modifier = Modifier.width(8.dp))\n                Text(\"保存喂养记录\")\n            }\n        }\n    }\n}\n\n\n}\n", "modifiedCode": "package com.example.babylog.ui.screens\n\nimport androidx.compose.foundation.background\nimport androidx.compose.foundation.clickable\nimport androidx.compose.foundation.layout.*\nimport androidx.compose.foundation.lazy.LazyColumn\nimport androidx.compose.foundation.selection.selectable\nimport androidx.compose.foundation.shape.RoundedCornerShape\nimport androidx.compose.material.icons.Icons\nimport androidx.compose.material.icons.filled.*\nimport androidx.compose.material3.*\nimport androidx.compose.runtime.*\nimport androidx.compose.ui.Alignment\nimport androidx.compose.ui.Modifier\nimport androidx.compose.ui.draw.clip\nimport androidx.compose.ui.graphics.Color\nimport androidx.compose.ui.platform.LocalContext\nimport androidx.compose.ui.text.font.FontWeight\nimport androidx.compose.ui.unit.dp\nimport androidx.navigation.NavController\nimport com.example.babylog.data.database.BabyLogDatabase\nimport com.example.babylog.data.entity.FeedingRecord\nimport com.example.babylog.data.repository.BabyRepository\nimport com.example.babylog.ui.components.DateTimePickerDialog\nimport com.example.babylog.ui.components.EditableTimeField\nimport com.example.babylog.ui.theme.*\nimport kotlinx.coroutines.launch\nimport java.text.SimpleDateFormat\nimport java.util.*\n\n@OptIn(ExperimentalMaterial3Api::class)\n@Composable\nfun AddFeedingRecordScreen(\n    navController: NavController,\n    babyId: String\n) {\n    val context = LocalContext.current\n    val database = BabyLogDatabase.getDatabase(context)\n    val repository = BabyRepository(\n        database.babyDao(),\n        database.healthRecordDao(),\n        database.milestoneDao(),\n        database.feedingRecordDao(),\n        database.photoDao()\n    )\n    \n    val scope = rememberCoroutineScope()\n    \n    // Form state\n    var selectedType by remember { mutableStateOf(\"breastfeeding\") }\n    var amount by remember { mutableStateOf(\"\") }\n    var duration by remember { mutableStateOf(\"\") }\n    var foodName by remember { mutableStateOf(\"\") }\n    var notes by remember { mutableStateOf(\"\") }\n    var startTime by remember { mutableStateOf(Date()) }\n    var endTime by remember { mutableStateOf<Date?>(null) }\n    \n    val feedingTypes = listOf(\n        \"breastfeeding\" to \"母乳喂养\",\n        \"bottle\" to \"奶瓶喂养\",\n        \"solid_food\" to \"辅食\"\n    )\n    \n    val commonFoods = listOf(\n        \"米粉\", \"蛋黄\", \"苹果泥\", \"香蕉泥\", \"胡萝卜泥\", \"南瓜泥\",\n        \"土豆泥\", \"红薯泥\", \"青菜泥\", \"肉泥\", \"鱼泥\", \"粥\"\n    )\n    \n    Box(\n        modifier = Modifier\n            .fillMaxSize()\n            .background(NeumorphismColors.background)\n    ) {\n        LazyColumn(\n            modifier = Modifier\n                .fillMaxSize()\n                .padding(horizontal = 20.dp),\n            contentPadding = PaddingValues(vertical = 16.dp),\n            verticalArrangement = Arrangement.spacedBy(16.dp)\n        ) {\n            // Header\n            item {\n                Row(\n                    modifier = Modifier.fillMaxWidth(),\n                    horizontalArrangement = Arrangement.SpaceBetween,\n                    verticalAlignment = Alignment.CenterVertically\n                ) {\n                    IconButton(onClick = { navController.navigateUp() }) {\n                        Icon(\n                            Icons.Default.ArrowBack,\n                            contentDescription = \"返回\",\n                            tint = NeumorphismColors.textPrimary\n                        )\n                    }\n\n                    Column(horizontalAlignment = Alignment.CenterHorizontally) {\n                        Text(\n                            text = \"添加喂养记录\",\n                            style = MaterialTheme.typography.headlineMedium,\n                            fontWeight = FontWeight.Bold,\n                            color = NeumorphismColors.textPrimary\n                        )\n                        Text(\n                            text = \"记录宝宝的喂养情况\",\n                            style = MaterialTheme.typography.bodyMedium,\n                            color = NeumorphismColors.textSecondary\n                        )\n                    }\n\n                    // 占位符保持布局平衡\n                    Box(modifier = Modifier.size(48.dp))\n                }\n            }\n\n            // 喂养类型选择卡片\n            item {\n                NeumorphismCard(\n                    modifier = Modifier.fillMaxWidth(),\n                    elevation = 6,\n                    cornerRadius = 20\n                ) {\n                    Column {\n                        Text(\n                            text = \"喂养类型\",\n                            style = MaterialTheme.typography.titleLarge,\n                            fontWeight = FontWeight.Bold,\n                            color = NeumorphismColors.textPrimary\n                        )\n                        Spacer(modifier = Modifier.height(16.dp))\n\n                        // 喂养类型选择\n                        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {\n                            listOf(\n                                \"breastfeeding\" to \"母乳喂养\",\n                                \"bottle\" to \"奶瓶喂养\",\n                                \"solid_food\" to \"辅食喂养\"\n                            ).forEach { (type, label) ->\n                                Row(\n                                    modifier = Modifier\n                                        .fillMaxWidth()\n                                        .clip(RoundedCornerShape(12.dp))\n                                        .background(\n                                            if (selectedType == type)\n                                                NeumorphismColors.primary.copy(alpha = 0.1f)\n                                            else Color.Transparent\n                                        )\n                                        .clickable { selectedType = type }\n                                        .padding(12.dp),\n                                    verticalAlignment = Alignment.CenterVertically\n                                ) {\n                                    RadioButton(\n                                        selected = selectedType == type,\n                                        onClick = { selectedType = type },\n                                        colors = RadioButtonDefaults.colors(\n                                            selectedColor = NeumorphismColors.primary,\n                                            unselectedColor = NeumorphismColors.textSecondary\n                                        )\n                                    )\n                                    Spacer(modifier = Modifier.width(12.dp))\n                                    Icon(\n                                        when (type) {\n                                            \"breastfeeding\" -> Icons.Default.ChildCare\n                                            \"bottle\" -> Icons.Default.LocalDrink\n                                            \"solid_food\" -> Icons.Default.Restaurant\n                                            else -> Icons.Default.Restaurant\n                                        },\n                                        contentDescription = null,\n                                        modifier = Modifier.size(20.dp),\n                                        tint = if (selectedType == type)\n                                            NeumorphismColors.primary\n                                        else NeumorphismColors.textSecondary\n                                    )\n                                    Spacer(modifier = Modifier.width(8.dp))\n                                    Text(\n                                        text = label,\n                                        style = MaterialTheme.typography.bodyLarge,\n                                        fontWeight = if (selectedType == type) FontWeight.Bold else FontWeight.Normal,\n                                        color = if (selectedType == type)\n                                            NeumorphismColors.primary\n                                        else NeumorphismColors.textPrimary\n                                    )\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n\n            // 时间设置卡片\n            item {\n                NeumorphismCard(\n                    modifier = Modifier.fillMaxWidth(),\n                    elevation = 6,\n                    cornerRadius = 20\n                ) {\n                    Column {\n                        Text(\n                            text = \"时间设置\",\n                            style = MaterialTheme.typography.titleLarge,\n                            fontWeight = FontWeight.Bold,\n                            color = NeumorphismColors.textPrimary\n                        )\n                        Spacer(modifier = Modifier.height(16.dp))\n\n                        // 开始时间\n                        EditableTimeField(\n                            label = \"开始时间\",\n                            value = startTime,\n                            onValueChange = { startTime = it },\n                            modifier = Modifier.fillMaxWidth()\n                        )\n\n                        if (selectedType == \"breastfeeding\") {\n                            Spacer(modifier = Modifier.height(16.dp))\n                            // 结束时间（母乳喂养可选）\n                            if (endTime != null) {\n                                EditableTimeField(\n                                    label = \"结束时间（可选）\",\n                                    value = endTime!!,\n                                    onValueChange = { endTime = it },\n                                    modifier = Modifier.fillMaxWidth(),\n                                    isOptional = true,\n                                    onClear = { endTime = null }\n                                )\n                            } else {\n                                OutlinedTextField(\n                                    value = \"\",\n                                    onValueChange = { },\n                                    label = { Text(\"结束时间（可选）\") },\n                                    modifier = Modifier.fillMaxWidth(),\n                                    readOnly = true,\n                                    colors = getTextFieldColors(),\n                                    placeholder = { Text(\"点击添加结束时间\") },\n                                    trailingIcon = {\n                                        IconButton(onClick = {\n                                            // 设置结束时间为开始时间后30分钟\n                                            val calendar = Calendar.getInstance()\n                                            calendar.time = startTime\n                                            calendar.add(Calendar.MINUTE, 30)\n                                            endTime = calendar.time\n                                        }) {\n                                            Icon(\n                                                Icons.Default.Add,\n                                                contentDescription = \"添加结束时间\",\n                                                tint = NeumorphismColors.primary\n                                            )\n                                        }\n                                    }\n                                )\n                            }\n                        }\n                    }\n                }\n            }\n\n            // 详细信息卡片\n            item {\n                NeumorphismCard(\n                    modifier = Modifier.fillMaxWidth(),\n                    elevation = 6,\n                    cornerRadius = 20\n                ) {\n                    Column {\n                        Text(\n                            text = \"详细信息\",\n                            style = MaterialTheme.typography.titleLarge,\n                            fontWeight = FontWeight.Bold,\n                            color = NeumorphismColors.textPrimary\n                        )\n                        Spacer(modifier = Modifier.height(16.dp))\n\n                        when (selectedType) {\n                            \"breastfeeding\" -> {\n                                OutlinedTextField(\n                                    value = duration,\n                                    onValueChange = { duration = it },\n                                    label = { Text(\"喂养时长（分钟）*\") },\n                                    modifier = Modifier.fillMaxWidth(),\n                                    singleLine = true,\n                                    suffix = { Text(\"分钟\") },\n                                    colors = getTextFieldColors()\n                                )\n                            }\n                            \"bottle\" -> {\n                                OutlinedTextField(\n                                    value = amount,\n                                    onValueChange = { amount = it },\n                                    label = { Text(\"奶量（ml）*\") },\n                                    modifier = Modifier.fillMaxWidth(),\n                                    singleLine = true,\n                                    suffix = { Text(\"ml\") },\n                                    colors = getTextFieldColors()\n                                )\n                            }\n                            \"solid_food\" -> {\n                                OutlinedTextField(\n                                    value = foodName,\n                                    onValueChange = { foodName = it },\n                                    label = { Text(\"食物名称*\") },\n                                    modifier = Modifier.fillMaxWidth(),\n                                    singleLine = true,\n                                    placeholder = { Text(\"如：苹果泥、米粉等\") },\n                                    colors = getTextFieldColors()\n                                )\n                                Spacer(modifier = Modifier.height(16.dp))\n                                OutlinedTextField(\n                                    value = amount,\n                                    onValueChange = { amount = it },\n                                    label = { Text(\"食用量（g）\") },\n                                    modifier = Modifier.fillMaxWidth(),\n                                    singleLine = true,\n                                    suffix = { Text(\"g\") },\n                                    colors = getTextFieldColors()\n                                )\n                            }\n                        }\n\n                        Spacer(modifier = Modifier.height(16.dp))\n\n                        // 备注\n                        OutlinedTextField(\n                            value = notes,\n                            onValueChange = { notes = it },\n                            label = { Text(\"备注\") },\n                            modifier = Modifier.fillMaxWidth(),\n                            minLines = 3,\n                            maxLines = 5,\n                            placeholder = { Text(\"记录宝宝的反应、食欲情况等\") },\n                            colors = getTextFieldColors()\n                        )\n                    }\n                }\n            }\n\n            // 保存按钮\n            item {\n                NeumorphismButton(\n                onClick = {\n                    scope.launch {\n                        val feedingRecord = FeedingRecord(\n                            id = UUID.randomUUID().toString(),\n                            babyId = babyId,\n                            type = selectedType,\n                            amount = when (selectedType) {\n                                \"bottle\" -> amount.toDoubleOrNull()\n                                \"solid_food\" -> amount.toDoubleOrNull()\n                                else -> null\n                            },\n                            duration = if (selectedType == \"breastfeeding\") \n                                duration.toIntOrNull() else null,\n                            foodName = if (selectedType == \"solid_food\" && foodName.isNotBlank()) \n                                foodName else null,\n                            notes = if (notes.isNotBlank()) notes else null,\n                            startTime = startTime,\n                            endTime = endTime\n                        )\n                        \n                        repository.insertFeedingRecord(feedingRecord)\n                        navController.navigateUp()\n                    }\n                },\n                enabled = when (selectedType) {\n                    \"breastfeeding\" -> duration.isNotBlank() && duration.toIntOrNull() != null\n                    \"bottle\" -> amount.isNotBlank() && amount.toDoubleOrNull() != null\n                    \"solid_food\" -> foodName.isNotBlank()\n                    else -> true\n                },\n                modifier = Modifier.fillMaxWidth()\n            ) {\n                Icon(Icons.Default.Save, contentDescription = null)\n                Spacer(modifier = Modifier.width(8.dp))\n                Text(\"保存喂养记录\")\n            }\n        }\n    }\n}\n\n\n}\n"}
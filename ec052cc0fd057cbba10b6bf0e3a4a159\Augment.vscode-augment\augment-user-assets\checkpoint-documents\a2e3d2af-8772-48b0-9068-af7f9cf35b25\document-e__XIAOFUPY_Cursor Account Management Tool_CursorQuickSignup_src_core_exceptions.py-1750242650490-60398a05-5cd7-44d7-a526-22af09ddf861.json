{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/core/exceptions.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n异常定义模块\n\n定义应用程序中使用的所有自定义异常类。\n\"\"\"\n\nfrom typing import Optional, Any\n\n\nclass AppError(Exception):\n    \"\"\"应用程序基础异常类\"\"\"\n    \n    def __init__(self, message: str, error_code: Optional[int] = None, details: Optional[Any] = None):\n        \"\"\"初始化异常\n        \n        Args:\n            message: 错误消息\n            error_code: 错误代码\n            details: 错误详情\n        \"\"\"\n        super().__init__(message)\n        self.message = message\n        self.error_code = error_code\n        self.details = details\n    \n    def __str__(self) -> str:\n        if self.error_code:\n            return f\"[{self.error_code}] {self.message}\"\n        return self.message\n\n\nclass ConfigError(AppError):\n    \"\"\"配置相关异常\"\"\"\n    pass\n\n\nclass ServiceError(AppError):\n    \"\"\"服务层异常\"\"\"\n    pass\n\n\nclass ValidationError(AppError):\n    \"\"\"验证异常\"\"\"\n    pass\n\n\nclass NetworkError(AppError):\n    \"\"\"网络异常\"\"\"\n    pass\n\n\nclass AuthenticationError(AppError):\n    \"\"\"认证异常\"\"\"\n    pass\n\n\nclass BrowserError(AppError):\n    \"\"\"浏览器操作异常\"\"\"\n    pass\n\n\nclass EmailError(AppError):\n    \"\"\"邮箱处理异常\"\"\"\n    pass\n\n\nclass FileOperationError(AppError):\n    \"\"\"文件操作异常\"\"\"\n    pass\n\n\nclass ResetError(AppError):\n    \"\"\"重置操作异常\"\"\"\n    pass\n\n\nclass AccountError(AppError):\n    \"\"\"账号相关异常\"\"\"\n    pass\n\n\nclass RegistrationError(AppError):\n    \"\"\"注册异常\"\"\"\n    pass\n\n\nclass VerificationError(AppError):\n    \"\"\"验证码异常\"\"\"\n    pass\n\n\nclass DatabaseError(AppError):\n    \"\"\"数据库异常\"\"\"\n    pass\n\n\nclass UIError(AppError):\n    \"\"\"UI相关异常\"\"\"\n    pass\n"}
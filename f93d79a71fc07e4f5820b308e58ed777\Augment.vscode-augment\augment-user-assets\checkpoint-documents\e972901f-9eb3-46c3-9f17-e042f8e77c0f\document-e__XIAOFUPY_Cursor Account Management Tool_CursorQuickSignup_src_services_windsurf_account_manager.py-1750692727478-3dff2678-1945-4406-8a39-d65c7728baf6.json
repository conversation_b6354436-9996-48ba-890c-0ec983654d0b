{"path": {"rootPath": "e:\\XIAOFUPY\\Cursor Account Management Tool\\CursorQuickSignup", "relPath": "src/services/windsurf/account_manager.py"}, "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nWindsurf账号管理服务\n\n提供Windsurf账号的管理功能。\n\"\"\"\n\nimport asyncio\nimport aiohttp\nfrom typing import List, Dict, Any, Optional\nfrom datetime import datetime\n\nfrom ...core import info, error, warning, debug, get_database\nfrom ...models import WindsurfAccount\nfrom ...core.exceptions import AuthenticationError, NetworkError\n\n\nclass WindsurfAccountManager:\n    \"\"\"Windsurf账号管理服务类\"\"\"\n    \n    def __init__(self):\n        \"\"\"初始化账号管理服务\"\"\"\n        self.database = get_database()\n    \n    def get_all_accounts(self) -> List[WindsurfAccount]:\n        \"\"\"获取所有Windsurf账号\n        \n        Returns:\n            List[WindsurfAccount]: 账号列表\n        \"\"\"\n        try:\n            accounts_data = self.database.get_windsurf_accounts()\n            accounts = []\n            \n            for data in accounts_data:\n                try:\n                    account = WindsurfAccount.from_dict(data)\n                    accounts.append(account)\n                except Exception as e:\n                    warning(f\"解析Windsurf账号数据失败: {e}\")\n                    continue\n            \n            info(f\"获取到 {len(accounts)} 个Windsurf账号\")\n            return accounts\n            \n        except Exception as e:\n            error(f\"获取Windsurf账号列表失败: {e}\")\n            return []\n    \n    def get_account_by_email(self, email: str) -> Optional[WindsurfAccount]:\n        \"\"\"根据邮箱获取账号\n        \n        Args:\n            email: 邮箱地址\n            \n        Returns:\n            Optional[WindsurfAccount]: 账号实例，未找到时返回None\n        \"\"\"\n        accounts = self.get_all_accounts()\n        for account in accounts:\n            if account.email == email:\n                return account\n        return None\n    \n    def update_account(self, email: str, updates: Dict[str, Any]) -> bool:\n        \"\"\"更新账号信息\n        \n        Args:\n            email: 邮箱地址\n            updates: 更新数据\n            \n        Returns:\n            bool: 更新是否成功\n        \"\"\"\n        try:\n            # 添加更新时间\n            updates[\"updated_at\"] = datetime.now().isoformat()\n            \n            success = self.database.update_windsurf_account(email, updates)\n            if success:\n                info(f\"Windsurf账号 {email} 更新成功\")\n            else:\n                warning(f\"Windsurf账号 {email} 更新失败\")\n            \n            return success\n            \n        except Exception as e:\n            error(f\"更新Windsurf账号失败: {e}\")\n            return False\n    \n    def delete_account(self, email: str) -> bool:\n        \"\"\"删除账号\n        \n        Args:\n            email: 邮箱地址\n            \n        Returns:\n            bool: 删除是否成功\n        \"\"\"\n        try:\n            success = self.database.delete_windsurf_account(email)\n            if success:\n                info(f\"Windsurf账号 {email} 删除成功\")\n            else:\n                warning(f\"Windsurf账号 {email} 删除失败\")\n            \n            return success\n            \n        except Exception as e:\n            error(f\"删除Windsurf账号失败: {e}\")\n            return False\n    \n    async def check_account_status(self, account: WindsurfAccount) -> Dict[str, Any]:\n        \"\"\"检查账号状态\n        \n        Args:\n            account: 账号实例\n            \n        Returns:\n            Dict[str, Any]: 账号状态信息\n        \"\"\"\n        try:\n            if not account.token:\n                return {\n                    \"status\": \"invalid\",\n                    \"message\": \"缺少Token\",\n                    \"valid\": False\n                }\n            \n            # 检查Token有效性\n            token_valid = await self._verify_token(account.token)\n            \n            if not token_valid:\n                return {\n                    \"status\": \"expired\",\n                    \"message\": \"Token已过期\",\n                    \"valid\": False\n                }\n            \n            # 获取用户信息\n            user_info = await self._get_user_info(account.token)\n            \n            if user_info:\n                return {\n                    \"status\": \"active\",\n                    \"message\": \"账号正常\",\n                    \"valid\": True,\n                    \"user_info\": user_info\n                }\n            else:\n                return {\n                    \"status\": \"error\",\n                    \"message\": \"无法获取用户信息\",\n                    \"valid\": False\n                }\n                \n        except Exception as e:\n            error(f\"检查Windsurf账号状态失败: {e}\")\n            return {\n                \"status\": \"error\",\n                \"message\": f\"检查失败: {e}\",\n                \"valid\": False\n            }\n    \n    async def check_all_accounts_status(self) -> Dict[str, Any]:\n        \"\"\"检查所有账号状态\n        \n        Returns:\n            Dict[str, Any]: 检查结果统计\n        \"\"\"\n        accounts = self.get_all_accounts()\n        \n        results = {\n            \"total\": len(accounts),\n            \"active\": 0,\n            \"expired\": 0,\n            \"invalid\": 0,\n            \"error\": 0,\n            \"details\": []\n        }\n        \n        info(f\"开始检查 {len(accounts)} 个Windsurf账号状态\")\n        \n        # 并发检查账号状态\n        tasks = []\n        for account in accounts:\n            task = self.check_account_status(account)\n            tasks.append(task)\n        \n        try:\n            status_results = await asyncio.gather(*tasks, return_exceptions=True)\n            \n            for i, result in enumerate(status_results):\n                account = accounts[i]\n                \n                if isinstance(result, Exception):\n                    results[\"error\"] += 1\n                    result_data = {\n                        \"email\": account.email,\n                        \"status\": \"error\",\n                        \"message\": str(result),\n                        \"valid\": False\n                    }\n                else:\n                    status = result.get(\"status\", \"error\")\n                    if status == \"active\":\n                        results[\"active\"] += 1\n                    elif status == \"expired\":\n                        results[\"expired\"] += 1\n                    elif status == \"invalid\":\n                        results[\"invalid\"] += 1\n                    else:\n                        results[\"error\"] += 1\n                    \n                    result_data = result.copy()\n                    result_data[\"email\"] = account.email\n                \n                results[\"details\"].append(result_data)\n                \n                # 更新账号状态到数据库\n                self.update_account(account.email, {\n                    \"status\": result_data[\"status\"],\n                    \"last_check\": datetime.now().isoformat()\n                })\n        \n        except Exception as e:\n            error(f\"批量检查Windsurf账号状态失败: {e}\")\n        \n        info(f\"Windsurf账号状态检查完成: 正常 {results['active']} 个，过期 {results['expired']} 个，无效 {results['invalid']} 个，错误 {results['error']} 个\")\n        return results\n    \n    async def _verify_token(self, token: str) -> bool:\n        \"\"\"验证Token有效性\n        \n        Args:\n            token: 访问Token\n            \n        Returns:\n            bool: Token是否有效\n        \"\"\"\n        try:\n            headers = {\n                \"Authorization\": f\"Bearer {token}\",\n                \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\"\n            }\n            \n            async with aiohttp.ClientSession() as session:\n                async with session.get(\n                    \"https://api.windsurf.com/auth/verify\",  # 假设的API端点\n                    headers=headers,\n                    timeout=aiohttp.ClientTimeout(total=10)\n                ) as response:\n                    return response.status == 200\n                    \n        except Exception as e:\n            debug(f\"Windsurf Token验证失败: {e}\")\n            return False\n    \n    async def _get_user_info(self, token: str) -> Optional[Dict[str, Any]]:\n        \"\"\"获取用户信息\n        \n        Args:\n            token: 访问Token\n            \n        Returns:\n            Optional[Dict[str, Any]]: 用户信息，失败时返回None\n        \"\"\"\n        try:\n            headers = {\n                \"Authorization\": f\"Bearer {token}\",\n                \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\"\n            }\n            \n            async with aiohttp.ClientSession() as session:\n                async with session.get(\n                    \"https://api.windsurf.com/user/me\",  # 假设的API端点\n                    headers=headers,\n                    timeout=aiohttp.ClientTimeout(total=10)\n                ) as response:\n                    if response.status == 200:\n                        return await response.json()\n                    else:\n                        return None\n                        \n        except Exception as e:\n            debug(f\"获取Windsurf用户信息失败: {e}\")\n            return None\n    \n    def export_accounts(self, file_path: str, format_type: str = \"json\") -> bool:\n        \"\"\"导出账号数据\n        \n        Args:\n            file_path: 导出文件路径\n            format_type: 导出格式 (json, csv, txt)\n            \n        Returns:\n            bool: 导出是否成功\n        \"\"\"\n        try:\n            accounts = self.get_all_accounts()\n            \n            if format_type.lower() == \"json\":\n                return self._export_to_json(accounts, file_path)\n            elif format_type.lower() == \"csv\":\n                return self._export_to_csv(accounts, file_path)\n            elif format_type.lower() == \"txt\":\n                return self._export_to_txt(accounts, file_path)\n            else:\n                error(f\"不支持的导出格式: {format_type}\")\n                return False\n                \n        except Exception as e:\n            error(f\"导出Windsurf账号数据失败: {e}\")\n            return False\n    \n    def _export_to_json(self, accounts: List[WindsurfAccount], file_path: str) -> bool:\n        \"\"\"导出为JSON格式\"\"\"\n        import json\n        \n        try:\n            data = [account.to_dict() for account in accounts]\n            \n            with open(file_path, 'w', encoding='utf-8') as f:\n                json.dump(data, f, ensure_ascii=False, indent=2)\n            \n            info(f\"Windsurf账号数据已导出到: {file_path}\")\n            return True\n            \n        except Exception as e:\n            error(f\"导出JSON失败: {e}\")\n            return False\n    \n    def _export_to_csv(self, accounts: List[WindsurfAccount], file_path: str) -> bool:\n        \"\"\"导出为CSV格式\"\"\"\n        import csv\n        \n        try:\n            with open(file_path, 'w', newline='', encoding='utf-8') as f:\n                writer = csv.writer(f)\n                \n                # 写入表头\n                writer.writerow([\n                    \"邮箱\", \"密码\", \"Token\", \"用户ID\", \"订阅类型\", \n                    \"状态\", \"创建时间\", \"最后检查时间\"\n                ])\n                \n                # 写入数据\n                for account in accounts:\n                    writer.writerow([\n                        account.email,\n                        account.password,\n                        account.token,\n                        account.user_id,\n                        account.subscription_type,\n                        account.status,\n                        account.created_at,\n                        account.last_check\n                    ])\n            \n            info(f\"Windsurf账号数据已导出到: {file_path}\")\n            return True\n            \n        except Exception as e:\n            error(f\"导出CSV失败: {e}\")\n            return False\n    \n    def _export_to_txt(self, accounts: List[WindsurfAccount], file_path: str) -> bool:\n        \"\"\"导出为TXT格式\"\"\"\n        try:\n            with open(file_path, 'w', encoding='utf-8') as f:\n                f.write(\"Windsurf账号列表\\n\")\n                f.write(\"=\" * 50 + \"\\n\\n\")\n                \n                for i, account in enumerate(accounts, 1):\n                    f.write(f\"账号 {i}:\\n\")\n                    f.write(f\"  邮箱: {account.email}\\n\")\n                    f.write(f\"  密码: {account.password}\\n\")\n                    f.write(f\"  Token: {account.token}\\n\")\n                    f.write(f\"  用户ID: {account.user_id}\\n\")\n                    f.write(f\"  订阅类型: {account.subscription_type}\\n\")\n                    f.write(f\"  状态: {account.status}\\n\")\n                    f.write(f\"  创建时间: {account.created_at}\\n\")\n                    f.write(f\"  最后检查: {account.last_check}\\n\")\n                    f.write(\"-\" * 30 + \"\\n\\n\")\n            \n            info(f\"Windsurf账号数据已导出到: {file_path}\")\n            return True\n            \n        except Exception as e:\n            error(f\"导出TXT失败: {e}\")\n            return False\n"}